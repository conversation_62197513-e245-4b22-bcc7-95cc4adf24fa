<?php

namespace App\Data\Form;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\NameData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\DataCollection;


class FormGeneralData extends BaseNonNullableData
{
    public function __construct(
        public ?string $id=null,
        #[MapOutputName('lb'),MapInputName('lb')]
        public ?NameData $label=null,
        #[MapOutputName('ne'),MapInputName('ne')]
        public ?string $name=null,
        #[MapOutputName('val'),MapInputName('val')]
        public $value=null,
        #[MapOutputName('ex'),MapInputName('ex')]
        public $extra=null,
        #[MapOutputName('mv'),MapInputName('mv')]
        public $minValue=null,
        #[MapOutputName('ml'),MapInputName('ml')]
        public ?int $maxLength=null,
        #[MapOutputName('ldg'),MapInputName('ldg')]
        public $leading=null
    ) {
    }
}
