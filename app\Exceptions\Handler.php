<?php

namespace App\Exceptions;

use Throwable;
use View;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Client\ConnectionException;
class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function report(Throwable $exception)
    {
         // Kill reporting if this is an "access denied" (code 9) OAuthServerException.
         if ($exception instanceof \League\OAuth2\Server\Exception\OAuthServerException && $exception->getCode() == 9) {
            return;
        }
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $exception)
    {

        /*if ($this->isHttpException($exception)) {
            $statusCode = $exception->getStatusCode();
            return view(config('general.theme.selected').'.errors.index')->with('statusCode',$statusCode);
        }*/
        return parent::render($request, $exception);
        /*if(env('APP_DEBUG') == false){
            return redirect('/');
        }else{
            return parent::render($request, $exception);
        }*/
    }

    // protected function registerErrorViewPaths()
    // {
    //     $paths = collect(config('view.paths'));

    //     View::replaceNamespace('errors', $paths->map(function ($path) {
    //         return "{$path}/".config('general.theme.selected')."/errors";
    //     })->push(__DIR__.'/views')->all());
    // }
    /**
     * Determine if the exception should be reported.
     *
     * @param  \Throwable  $e
     * @return bool
     */
    public function shouldReport(Throwable $e)
    {
        //return false;
        if ($e instanceof \GuzzleHttp\Exception\ConnectException && strpos($e->getMessage(), 'cURL error 28') !== false) {
        // return false;
            $settings=app(\App\Settings\ThirdPartySettings::class)->utilityPayement;
            if($settings->is_test){
                $settings=$settings->test_data;
            }
            $url=$e->getRequest()->getUri();
            if (strpos($url, $settings->url) === 0) {
                return false;
            }
        }
        return parent::shouldReport($e);
    }
}
