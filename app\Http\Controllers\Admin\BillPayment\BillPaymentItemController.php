<?php

namespace App\Http\Controllers\Admin\BillPayment;
use App\Data\BaseNonNullableDataCollection;
use App\Data\Form\FormBaseData;
use App\Data\GeneralItemData;
use App\Http\Controllers\Controller;
use App\Models\BillPaymentFilter;
use App\Models\BillPaymentModelFilter;
use App\Models\BillPaymentItem;
use App\Models\BillPaymentModelFilterOption;
use App\Models\BillPaymentService;
use Illuminate\Http\Request;
use Auth;

class BillPaymentItemController extends Controller
{
    protected $viewTypes=[
        "chip"=>"Chip",
        "grid"=>"Grid"
    ];
    protected $status=[
        "0"=>"Unactive",
        "1"=>"Active",
    ];

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['bill_payment.item.*','bill_payment.item.list'])) abort(401);

        $filter=$request->all();

        $items=BillPaymentItem::select('id','title','status','image','biller','created_at')->orderBy('id','desc');


        if($request->filled('filter') ){
            $items=$items->whereHas('filters',function($query) use($request){
                $query->where('id',$request->filter);
            });
        }
        if($request->filled('biller') ){
            $items=$items->where('biller',$filter['biller']);
        }
        if($request->filled('service') ){
            $items=$items->where('bill_payment_service_id',$filter['service']);
        }
        if($request->filled('status') ){
            $items=$items->where('status',$filter['status']);
        }
        if($request->filled('searchname'))
            $items=$items->where(function($query) use($filter){
                return $query->where('title->ar','like','%'.$filter['searchname'].'%')
                ->orWhere('title->en','like','%'.$filter['searchname'].'%');
            });

        $items=$items->paginate(15);

      //  GeneralItemData::collect($items->paginate(15));//->wrap('paginated_data');

        $filters=BillPaymentFilter::select('id','title')->where('status',1)->get();
        $services=BillPaymentService::select('id','title')->get();
        $billers=BillPaymentItem::select('biller')->groupBy('biller')->pluck('biller');

        return view('default.admin.bill_payment.item.index')
        ->with('items', $items)
        ->with('filters', $filters)
        ->with('services', $services)
        ->with('billers', $billers)
        ->with('status', $this->status)
        ->with('filter', $filter);
    }


    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        if (! Auth::user()->canAny(['bill_payment.item.*','bill_payment.item.create'])) abort(401);

        return view('default.admin.bill_payment.item.view')
        ->with('types', $this->types);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\BillPaymentItem  $item
     * @return \Illuminate\Contracts\View\View
     */
    public function show(BillPaymentItem $item){
        if (! Auth::user()->canAny(['bill_payment.item.*','bill_payment.item.view','bill_payment.item.edit'])) abort(401);

        $filters=BillPaymentFilter::select('id','title')->where('status',1)->get();


        return view('default.admin.bill_payment.item.view')
        ->with('item', $item)
        ->with('viewTypes', $this->viewTypes)
        ->with('filters', $filters);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BillPaymentItem  $item
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, BillPaymentItem $item)
    {
        if (! Auth::user()->canAny(['bill_payment.item.*','bill_payment.item.edit'])) abort(401);
        //return response()->json($request->all());
        $this->validate($request,[
            'title.en' => 'required|max:500',
            'title.ar' => 'required|max:500',
            'view_type' => 'nullable|in:'.join(',',collect($this->viewTypes)->keys()->toArray()),
            'biller' => 'nullable|max:500',
            'biller_category' => 'nullable|max:500',
            'additional_fields' => 'nullable|array',
            'filters' => 'nullable|array',
            'status' => 'required|in:0,1',
            'fields' => 'nullable',
        ]);
        return $this->save($request,$item);
    }

    public function save(Request $request, ?BillPaymentItem $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new BillPaymentItem;
        }

        $this->setImage('image',"billPayments");

        // dd($request->all());
        // return;
        $item->image = $request->image;
        $item->title = $request->title;
        $item->view_type = $request->view_type;
        $item->biller = $request->biller;
        $item->biller_category = $request->biller_category;
        $item->additional_fields = $request->additional_fields;
        $item->status = $request->status;
        if(Auth::user()->hasRole('developer') && $request->filled('fields')){
            $item->fields=FormBaseData::collect(json_decode($request->fields),BaseNonNullableDataCollection::class);
        }
        $item->save();

        BillPaymentModelFilterOption::where('model_id', $item->id)
        ->where('model_type', BillPaymentItem::class)
        ->delete();
        if($request->filled('filter') &&  count($request->filter)){
            $filterItems=[];
            foreach($request->filter as $key=>$values){
                foreach($values as $value){
                    $filterItems[]=[
                        "model_type"=>BillPaymentItem::class,
                        "model_id"  =>$item->id,
                        "bill_payment_filter_option_id"=>$value,
                        "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                    ];
                }
            }
            if(!empty($filterItems)){
                collect($filterItems)->chunk(100)
                ->each(function ($chunked) {
                    BillPaymentModelFilterOption::insert($chunked->values()->toArray());
                });
            }
        }

        BillPaymentModelFilter::where('model_id', $item->id)
        ->where('model_type', BillPaymentItem::class)
        ->delete();
        if($request->filled('filters') &&  count($request->filters)){
            $filterItems=[];
            $parentId=null;
            foreach($request->filters as $filter){
                $filterItems[]=[
                    "model_type"=>BillPaymentItem::class,
                    "model_id"  =>$item->id,
                    "bill_payment_filter_id"=>$filter,
                    "parent_filter_id"=>$parentId,
                    "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                ];
                $parentId=$filter;
            }
            if(!empty($filterItems)){
                collect($filterItems)->chunk(100)
                ->each(function ($chunked) {
                    BillPaymentModelFilter::insert($chunked->values()->toArray());
                });
            }
        }


        BillPaymentService::cacheUpdate(["catalog:$item->bill_payment_service_id"]);

        if(isset($isNew)){
            return redirect(route("admin.billPayment.item.show",$item->id))
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['bill_payment.item.*','bill_payment.item.delete'])) return abort(401);

    }

}
