<?php

namespace App\Models;

use App\Data\BaseNonNullableDataCollection;
use App\Data\Form\FormFieldData;
use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\LaravelData\DataCollection;

class BillPaymentItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'id',
        'bill_payment_service_id',
        'title',
        'image',
        'biller',
        'biller_category',
        'additional_fields',
        'view_type',
        'status',
        'fields',
        'payload'
    ];
    protected $casts = [
        'title' => 'object',
        'fields'     => BaseNonNullableDataCollection::class.':'.FormFieldData::class,
        'payload' => 'object',
        "additional_fields"=>'array',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    public function getNameAttribute()
    {
        $local=app()->getLocale();
        return  $this->title?->{"$local"};
    }
    public function service()
    {
        return $this->belongsTo(BillPaymentService::class, 'bill_payment_service_id');
    }

    public function bundle()
    {
        return $this->hasOne(BillPaymentBundle::class, 'bill_payment_item_id', 'id');
    }
    public function bundles()
    {
        return $this->hasMany(BillPaymentBundle::class, 'bill_payment_item_id', 'id');
    }


    public function filters()
    {
        return $this->morphToMany(BillPaymentFilter::class, "model","bill_payment_model_filters");
    }

    public function options()
    {
        return $this->morphToMany(BillPaymentFilterOption::class, "model","bill_payment_model_filter_options");
    }
}
