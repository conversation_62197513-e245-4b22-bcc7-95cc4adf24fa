<?php

namespace App;
use <PERSON>pats<PERSON>\Uuid\Uuid;
use <PERSON><PERSON>\Passport\Client as PassportClient;
class Client extends PassportClient
{ 
    /** 
     * Indicates if the IDs are auto-incrementing. 
     * 
     * @var bool 
     */ 
    public $incrementing = false; 
    public static function boot()
    { 
        parent::boot();
         static::creating(function ($model) {
            $model->uuid = Uuid::generate()->string; 
         }); 
    } 
}