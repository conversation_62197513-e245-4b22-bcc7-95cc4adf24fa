<?php

namespace App\Http\Controllers\Digx\Gold;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\OBDX\ExchangeRateData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\LimitTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Enums\TransactionStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Gold;
use App\Services\OBDX\UtilsService;
use App\Traits\AuthorizesServices;
use DB;
use Illuminate\Http\Request;


class ProfitController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::GOLD_PLATFORM
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'account'=>"required|max:20|min:20",
            'from'=>"required|integer",
            'limit'=>"required|integer",

        ]);
        $golds=Gold::select('id','amount','exchange_rate','manual_type','remarks',"created_at as date")
        //->where('party_id',auth()->user()->id)
        ->where('status',TransactionStatusEnum::COMPLETED->value)
        ->whereNotNull('manual_type')
        ->where(function($query) use($request){
            $query->where('credit_account_id->value',$request->account)
            ->orWhere('debit_account_id->value',$request->account);
        })
        ->skip( $request->from)
        ->take($request->limit)
        ->orderBy("id","DESC")
        ->get();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "GOLD-DETAILS",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "transactions"=>$golds,
        ]));
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {

        $goldAccount=AccountIdData::from([
            "value"=>request()->account
        ]);
        $linkedAccount=AccountIdData::from([
            "value"=>substr_replace($goldAccount->value,CurrencyTypeEnum::YER->value,10, 3)
        ]);
        $amount=CurrencyAmountData::from([
            "amount"=>1,
            "currency"=>$goldAccount->currencyId()
        ]);

        $exchangeRates[]=UtilsService::exchange(
            $amount,
            $goldAccount,
            $linkedAccount
        );
        $currentRate=0;//collect($result->exchangeRates);//->sum('rateNet.amount');
        foreach ($exchangeRates as $key => $value) {
            if($value->debitRate->amount>$value->creditRate->amount && $value->debitRate->currency==CurrencyTypeEnum::YER->value){
                $currentRate+=$value->debitRate->amount;
            }
        }

        $profit=DB::query()
        ->fromSub(
            Gold::select(
                DB::raw("SUM(
                    CASE
                        WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                            OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                        THEN CAST(json_value(EXCHANGE_RATE, '$.\"amount\".\"limit\".\"amount\"') AS NUMBER)
                        ELSE 0
                    END
                ) AS sum_sell_amount"),

                DB::raw("SUM(
                    CASE
                        WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                            OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                        THEN 0
                        ELSE CAST(json_value(EXCHANGE_RATE, '$.\"amount\".\"limit\".\"amount\"') AS NUMBER)
                    END
                ) AS sum_buy_amount"),

                DB::raw("SUM(
                    CASE
                        WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                            OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                        THEN CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                        ELSE 0
                    END
                ) AS sum_sell_qty"),

                DB::raw("SUM(
                    CASE
                        WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                            OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                        THEN 0
                        ELSE CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                    END
                ) AS sum_buy_qty"),

                DB::raw("SUM(
                    CASE
                        WHEN manual_type='storage'
                        THEN 0
                        ELSE (
                            CASE
                            WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                            OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                            THEN -1*CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                            ELSE CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                            END
                        )
                    END
                ) AS balance"),

                DB::raw("SUM(
                    CASE
                        WHEN manual_type='storage'
                        THEN CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                        ELSE 0
                    END
                ) AS storage_balance"),

                DB::raw("SUM(
                    CASE
                        WHEN manual_type='storage'
                        THEN (
                            CASE
                                WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                                    OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                                THEN 0
                                ELSE CAST(json_value(EXCHANGE_RATE, '$.\"amount\".\"limit\".\"amount\"') AS NUMBER)
                            END
                        )
                        ELSE 0
                    END
                ) AS storage_amount")
                )
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->where(function($query) use($goldAccount){
                    $query->whereNull('limit_type')
                    ->orWhereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value]);
                })
                ->where(function($query) use($goldAccount){
                    $query->where('credit_account_id->value',$goldAccount->value)
                    ->orWhere('debit_account_id->value',$goldAccount->value);
                })
        ,"dual")
        ->select(
            DB::raw("
            (
                (
                    $currentRate-(
                        sum_buy_amount/NULLIF(sum_buy_qty,0)
                    )
                )*(
                    sum_buy_qty-sum_sell_qty
                )
            )+(
                sum_sell_amount-(
                    sum_buy_amount/NULLIF(sum_buy_qty,0)
                )*(
                    sum_buy_qty-(sum_buy_qty-sum_sell_qty)
                )
            ) as profit"),
            DB::raw("
            (
                (
                    $currentRate-(
                        storage_amount/NULLIF(storage_balance,0)
                    )
                )* storage_balance
            ) as storage_profit"),
            DB::raw("NULLIF(balance,0) as balance"),
            DB::raw("NULLIF(storage_balance,0) as storage_balance"),
            DB::raw("(sum_buy_amount+sum_sell_amount)/NULLIF(sum_buy_qty+sum_sell_qty,0) as price"),
            DB::raw("sum_buy_amount/NULLIF(sum_buy_qty,0) as buy_price"),
            DB::raw("sum_sell_amount/NULLIF(sum_sell_qty,0) as sell_price"),

            DB::raw("$currentRate as rate"),
        )
        ->first();
        $profit=collect($profit)->toArray();
        $balanceProfit=($profit['profit']??0)-($profit['storage_profit']??0);
        if($balanceProfit){
            $profit['balance_profit']=$balanceProfit;
        }else{
            $profit['balance_profit']=null;
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "GOLD-DETAILS",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "profits"=>[
                'avg'  =>$profit
            ],
        ]));

    }
    /**
     * Store a newly created resource in storage.
     *
     * Initate gold buying/selling process
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'creditAccountId.value'=>"required_unless:manualType,withdraw|max:20|min:20",
            'debitAccountId.value'=>"required_if:manualType,==,withdraw|max:20|min:20",
            'amountRate.amount'=>"required|numeric",
            'amountRate.currency'=>"required|max:3|min:3|in:YER",
            'amount.amount'=>"required|integer",
            'amount.currency'=>"required|max:3|min:3|in:21G,24G",
            'manualType'=>"required|in:storage,deposit,withdraw",
            'remarks'=>"nullable",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GOLD",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_GOLD_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $user=$request->user()->userProfile;
        $debitAccountId=$request->filled("debitAccountId")?AccountIdData::from($request->input("debitAccountId")):null;
        $creditAccountId=$request->filled("creditAccountId")?AccountIdData::from($request->input("creditAccountId")):null;
        //$feeAccountId=AccountIdData::from($request->input("feeAccountId"));
        $amount=CurrencyAmountData::from($request->input("amount"));
        $fee=CurrencyAmountData::from([
            "amount"=>0,
            "currency"=>CurrencyTypeEnum::YER->value
        ]);
        $rate=$request->input('amountRate.amount');
        $exchangeRate=ExchangeRateData::from([
            "debitRate"=> [
                "amount"=> $request->manualType=="withdraw"?$rate:1,
                "currency"=> "YER"
            ],
            "creditRate"=> [
                "amount"=> $request->manualType=="withdraw"?1:$rate,
                "currency"=> "YER"
            ],
            "rate"=> [
                "amount"=> $rate,
                "currency"=> "YER"
            ],
            "rateNet"=> [
                "amount"=> $rate,
                "currency"=> "YER"
            ],
            "total"=> [
                "amount"=> $rate*$amount->amount,
                "currency"=> "YER"
            ],
            "limit"=> [
                "amount"=> $rate*$amount->amount,
                "currency"=> "YER"
            ]
        ]);
        $gold=Gold::create([
            "party_id"          =>$user->partyId->value,
            "status"            =>TransactionStatusEnum::COMPLETED->value,
           // "limit_type"        =>$limitType->value,
            "credit_account_id" =>$creditAccountId?->toArray(),
            "debit_account_id"  =>$debitAccountId?->toArray(),
            "fee_account_id"    =>null,
            "amount"            =>$amount->toArray(),
            "fee"               =>$fee->toArray(),
            "exchange_rate"=>[
                "amount"        =>$exchangeRate->toArray(),
            ],
            "remarks"=>$request->remarks,
            "manual_type"=>$request->manualType
        ]);

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-GOLD-$gold->id",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "paymentId"=>$gold->id,
        ]));
    }

    /**
     * Delete the specified resource in storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        Gold::whereNotNull('manual_type')
            ->where('id',$id)
            ->delete();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully delete transaction"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }

}
