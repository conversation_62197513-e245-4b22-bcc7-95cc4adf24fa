<?php

namespace App\Http\Controllers\Digx\Split;

use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\OBDX\Transfer\DomesticTransferData;
use App\Data\PaymentResultData;
use App\Data\ReceiptData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Helpers\JsonCamel\JsonCamelHelperFacade;
use App\Jobs\ProcessCanceledSplit;
use App\Models\CustomerType;
use App\Models\Party;
use App\Models\SplitPayment;
use App\Models\SplitPaymentTransaction;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use App\Services\NotificationService;
use App\Services\OBDX\BankyService;
use App\Services\OBDX\CustomerService;
use App\Services\OBDX\UtilsService;
use App\Traits\AuthorizesServices;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Enums\TransactionStatusEnum;
use App\Http\Controllers\Controller;
use App\LogItem;

use Illuminate\Http\Request;
class OrderController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::SPLIT_PAYMENT
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'from'=>"required|numeric",
            'limit'=>"required|numeric",
            'status'=>"nullable|in:".join(',',TransactionStatusEnum::values()),
            'text'=>"nullable"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-SPLIT",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_SPLIT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]),400);

        }

        $items=SplitPayment::list(
            from:$request->from??0,
            limit:$request->limit??20,
            filter: $request->text??'',
            status:$request->status??null,
        );

        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])
        ->additional([
            "transactions"=>$items->toArray()
        ])->transform());
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'creditAccountId.displayValue'=>"required",
            'creditAccountId.value'=>"required",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required|in:".join(',',CurrencyTypeEnum::values()),
            // 'fee.amount'=>"nullable|numeric",
            // 'fee.currency'=>"nullable",
            'bankCode'=>"nullable",
            'remarks'=>"required",
            'transactions'=>"required|array",
            'transactions.*.party.id'=>"required",
            // 'transactions.*.amount.amount'=>"required|numeric",
            // 'transactions.*.amount.currency'=>"required|in:".join(',',CurrencyTypeEnum::values()),
            // 'transactions.*.fee.amount'=>"nullable|numeric",
            // 'transactions.*.fee.currency'=>"nullable|in:".join(',',CurrencyTypeEnum::values()),
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-SPLIT",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_SPLIT_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]),400);
        }

        $username=null;
        $user=auth()->user();
        $party=Party::where('party_id',$user->id);
        if(isset($user->customerRole) &&  in_array($user->customerRole??'',CustomerType::getCorporateRoles())){
            $party=$party->where('username',$user->username);
            $username=$user->username;
        }
        $party=$party->first();
        if(is_null($party) ){
            Party::create([
                "party_id"=>$user->id,
                "username"=>$username,
                "name"=>$user->name
            ]);
        }

        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-SPLIT",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_SPLIT_101",
                    "type"    => "ERROR"
                ]
            ]
        ]);

        $friends=Party::select("id", "name", "image", "party_id")
        ->whereHas('friends')
        ->whereIn('id',collect($request->transactions)->pluck('party.id'))
        ->get();
        //$friends=PartyFriend::with('party')->whereIn('friend_id',collect($request->transactions)->pluck('party.id'))->get();
        // return response()->json(GeneralResponseData::from([
        //     'status'=>[
        //         "result"    => "SUCCESSFUL",
        //         "contextID" => "INI-INVOICE",
        //         "message"   => [
        //             "title"   => "The initial request has been created",
        //             "detail"  => "The initial request has been created",
        //             "code"    => "0",
        //             "type"    => "INFO"
        //         ]
        //     ]
        // ])->additional([
        //     "friends"=>$friends,
        // ])->transform());
        if($friends->count()!=count($request->transactions)){
            $errorResult->status->message->title=__("The friends list is not correct!");
            return response()->json( $errorResult,400);
        }

        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));
        $amount=CurrencyAmountData::from($request->amount);
        $fee=null;
        $paymentResult=null;
        if($request->filled('bankCode')){

            $debitAccountId=AccountIdData::from([
                "displayValue"=>ThirdPartyServiceNameData::split(),
                "value"=>ThirdPartyServiceNameData::split()
            ]);

            $object = new \stdClass();
            $object->journals= [
                "genericPayee" =>[
                    "nickName" =>'',
                    "accountName" =>'temp',
                    "accountNumber" => $creditAccountId->value,
                    "transferMode"=> 'ACC',
                    "network"=> 'NEFT',
                    "bankDetails"=>[
                        "code"=> $request->bankCode
                    ],
                ],
                "genericPayout" =>[
                    "amount" =>$amount->toArray(),
                    "purpose" =>'COMM',
                    "purposeText" => "Commercial",
                    "debitAccountId" => $debitAccountId->toArray(),
                ],
                "paymentType" => 'INDIADOMESTICFT'
            ];
            $result=BankyService::postGetGenericTransefer($object);

            if($result->status->message->code=="0" && isset($result->getAdditionalData()["payload"]) && $result->getAdditionalData()["payload"] instanceof DomesticTransferData){
                $additionalData=$result->getAdditionalData();
                $payload=$additionalData["payload"];
                $creditAccountId->displayValue=$payload->receiverName;
                $fee=$payload->fee;

                $paymentResultData =new PaymentResultData(
                    $additionalData['paymentId'],
                    null,
                    null
                );
                $paymentResult= [
                    'payment'=>$paymentResultData,
                ];

                $exchangeRate= UtilsService::exchange(
                    CurrencyAmountData::from([
                        "amount"=>1,
                        "currency"=>$amount->currency
                    ]),
                    $debitAccountId,
                    $debitAccountId,
                );

            }else{
                return response()->json( $result);
            }
        }else {
            $valid=false;
            $errorResult->status->message->title=__("You don't have the correct account!");
            $result=CustomerService::account($request,$request->input("creditAccountId.value"));
            if($result instanceof AccountData){
                $account=$result;
                $valid=$account->status=="ACTIVE" && $account->isCashOut() && $account->currencyId==$request->input("amount.currency");
            }

            if(!$valid){
                return response()->json($errorResult);
            }
            //$creditAccountId->displayValue=$payload->receiverName;

            $exchangeRate= UtilsService::exchange(
                CurrencyAmountData::from([
                    "amount"=>1,
                    "currency"=>$amount->currency
                ]),
                AccountIdData::from([
                    "value"=>substr_replace($creditAccountId->value,CurrencyTypeEnum::YER->value,10, 3)
                ]),
                $creditAccountId,
            );
        }

        $split=SplitPayment::create([
            'credit_account_id' =>$creditAccountId->toArray(),
            "amount"            =>$request->amount,
            "fee"               =>$fee?->toArray(),
            "exchange_rate"=>[
                "amount"        =>$exchangeRate->toArray(),
            ],
            'bank_code'         =>$request->bankCode??null,
            "remarks"           =>$request->remarks,
            "status"            =>TransactionStatusEnum::INIT->value,
            //"type"              =>InvoiceTransactionsTypeEnum::Payment->value,
            "payment_result"    =>$paymentResult,
        ]);
        LogItem::store($split);


        $percent=100/($friends->count()+1);
        $transactions=[
            [
                "party_id"=>auth()->user()->id,
                "split_payment_id"  =>$split->id,
                "percent"=>$percent,
                "status"            =>TransactionStatusEnum::INIT->value,
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
        ];
        foreach($friends as $friend){
            $transactions[]=[
                "party_id"=>$friend->party_id,
                "split_payment_id"  =>$split->id,
                "percent"=>$percent,
                "status"            =>TransactionStatusEnum::INIT->value,
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ];
        }
        $this->normalizePercent($transactions,$amount,$fee);

        if(!empty($transactions)){
            collect($transactions)->chunk(size: 100)
            ->each(function ($chunked) {
                SplitPaymentTransaction::insert($chunked->values()->toArray());
            });
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-INVOICE",
                "message"   => [
                    "title"   => __("The initial request has been created"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "paymentId"=>$split->id,
        ])->transform());

    }
    public function normalizePercent(array &$transactions,$amount,$fee)
    {
        //$values = [33.33333333, 33.33333333, 33.33333333];

       // Step 1: Round each value to 2 decimal places
        foreach($transactions as &$transaction){
            $transaction["percent"]=round($transaction["percent"], 4);
        }


        // Step 2: Calculate the difference from 100
        $sum = collect($transactions)->sum("percent");
        $difference = round(100 - $sum, 4);

        // Step 3: Apply the difference to the value with the highest decimal remainder
        //$remainders = array_map(fn($v) => fmod($v, 1), $values);
        //$index = array_keys($remainders, max($remainders))[0];

        // Apply the adjustment
        $transactions[0]["percent"] += $difference;

        foreach($transactions as &$transaction){
            $percent=$transaction["percent"]/100;
            $transaction["amount"]=json_encode([
                "amount"=>$percent*$amount->amount,
                "currency"=>$amount->currency,
            ]);
            if(!is_null( $fee)){
                $transaction["fee"]=json_encode([
                    "amount"=>$percent*$fee->amount,
                    "currency"=>$fee->currency,
                ]);
            }
        }
        // Optional: ensure result is still two decimal places
       // $normalized = array_map(fn($v) => round($v, 2), $rounded);
    }
    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $splitPayment=SplitPayment::show(id:$id);
        if(is_null($splitPayment)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "SPLIT-DETAILS",
                    "message"   => [
                        "title"   => __("Transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_SPLIT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]),400);
        }
        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "OIL-DETAILS",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "transferDetails"=>$splitPayment,
        ])->transform());


    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\SplitPayment  $split
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request, SplitPayment $split)
    {
        $validator=validator()->make($request->all(),[
            'transactions'=>"required|array",
            'transactions.*.id'=>"required",
            'transactions.*.percent'=>"required|numeric"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-SPLIT",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_SPLIT_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }


        LogItem::store($split);

        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-SPLIT",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_SPLIT_101",
                    "type"    => "ERROR"
                ]
            ]
        ]);

        $valid=false;
        $message="You don't have the correct account!";
        if ($split->status != TransactionStatusEnum::INIT->value || $split->type!=null) {
            $message = "This transaction has already been performed!";
        }else{
            $valid=true;
        }

        $sum=collect($request->transactions)->sum('percent');
        if(abs($sum - 100) > 0.0000001){
            $message="The percent is greater than allowd percent!";
            $valid=false;
        }

        if(!$valid){
            $errorResult->status->message->title=__($message);
            return response()->json($errorResult);
        }

        $split->load(['transactions'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class);
        }]);

        foreach ($split->transactions as $transaction) {
            $requestTransaction=collect($request->transactions)
            ->filter(function($trx)use($transaction){
                return $trx['id']==$transaction->id;
            })->first();
            if(is_null($requestTransaction)){
                $errorResult->status->message->title=__("The transaction list is not correct!");
                return response()->json($errorResult);
            }

            $transaction->percent=$requestTransaction['percent'];
            $percent=($transaction->percent/100);
            $amount=$percent*$split->amount->amount;
            $fee=$percent*($split->fee?->amount??0.0);



            if(!is_null($split->bank_code)){
                $amount=ceil($amount);
                $fee=ceil($fee);
            }else{
                $amount=round($amount,2);
                $fee=round($fee,2);
                if($transaction->party_id==$split->party_id){
                    $transaction->status=TransactionStatusEnum::COMPLETED->value;
                }
            }

            $transaction->amount=CurrencyAmountData::from([
                "amount"=> $amount,
                "currency"=>$split->amount->currency
            ]);
            if(!is_null($split->fee)){
                $transaction->fee=CurrencyAmountData::from([
                    "amount"=> $fee,
                    "currency"=>$split->fee->currency
                ]);
            }

            // Getting exchange rate of amount to equlevent local currency
            // $exchangeRate= UtilsService::exchange(
            //     CurrencyAmountData::from($payee["amount"]),
            //     AccountIdData::from($split->debit_account_id),
            //     AccountIdData::from([
            //         "value"=>substr_replace($split->debit_account_id->value,CurrencyTypeEnum::YER->value,10, 3)
            //     ])
            // );

            $transaction->type = InvoiceTransactionsTypeEnum::Payment->value;
            $transaction->save();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Split payment"),
                    'body'=>sprintf(__("You have new split payment request from [%s]!"),
                    auth()->user()->name
                    ),
                    'type'=>'operation',
                ]
            ],$transaction->party_id);
        }
        $split->type = InvoiceTransactionsTypeEnum::Payment->value;
        $split->save();

        return response()->json(GeneralResponseData::from(array(
            'status' => [
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully create split payment"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'externalReferenceId' => $split->id,
        ])->transform());
    }

/**
     * Delete the specified resource in storage.
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-SPLIT",
                "message"   => [
                    "title"   => __("Transaction not found!"),
                    "detail"  =>"",
                    "code"    => "DIGX_SWITCH_SPLIT_101",
                    "type"    => "ERROR"
                ]
            ]
        ]);

        $splitPayment=SplitPayment::with(['transactions'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class)
            ->select('id','split_payment_id','amount','fee','status','type','party_id');
        }])
        ->where("status",TransactionStatusEnum::INIT->value)
        ->where("type",InvoiceTransactionsTypeEnum::Payment->value)
        ->where('id',$id)
        ->first();

        if(!is_null(  $splitPayment)){
            if(!is_null($splitPayment->bank_code)){
                $transactions=collect($splitPayment->transactions)
                ->filter(function($transaction){
                    return $transaction->status==TransactionStatusEnum::INIT->value ||
                    $transaction->type==InvoiceTransactionsTypeEnum::Payment->value;
                })->toArray();
                if(count($transactions)){
                    $splitPayment->status=TransactionStatusEnum::CANCELED->value;
                    $splitPayment->save();
                    ProcessCanceledSplit::dispatch($splitPayment)->onQueue('critical');
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   => __("Successfully cancel split payment"),
                                "detail"  => "",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ]));
                }
            }else{
                $transactions=collect($splitPayment->transactions)
                ->filter(function($transaction){
                    return $transaction->party_id!= auth()->user()->id && ($transaction->status!=TransactionStatusEnum::INIT->value ||
                    $transaction->type!=InvoiceTransactionsTypeEnum::Payment->value);
                })->toArray();
                if(!count($transactions)){
                    $splitPayment->status=TransactionStatusEnum::CANCELED->value;
                    $splitPayment->save();
                    ProcessCanceledSplit::dispatch($splitPayment)->onQueue('critical');
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   => __("Successfully cancel split payment"),
                                "detail"  => "",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ]));
                }
            }
            $errorResult->status->message->title=__("You can't cancel this transaction!");
        }

        return response()->json($errorResult,400);

    }


    public function receipt(Request $request,SplitPayment $split)
    {
        $validator=validator()->make($request->all(),[
            'showAccountInfo' => 'Nullable|numeric|in:0,1',
            'showAccountNumber' => 'Nullable|numeric|in:0,1',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $this->generateReceipt($this->getReceiptData($split));
    }
    protected function getReceiptData(SplitPayment $splitPayment): ReceiptData
    {
        // Check if 'posts' relationship is loaded
        if (!$splitPayment->relationLoaded('party')) {
            $splitPayment->load(['party'=>function($query){
                return $query->withoutGlobalScope(UsernameScope::class);
            }]);
        }
        // $splitPayment=$transaction->splitPayment;
        $beneficiary=
            $splitPayment->credit_account_id->displayValue;
        $beneficiary.="\n{$splitPayment->credit_account_id->value}";
        return ReceiptData::from([
            "id"=> $splitPayment->id,
            "date"=> date_format(date_create($splitPayment->created_at), "Y-m-d H:i:s"),
            "title"=> __("Split payment"),
            "beneficiary"=> $beneficiary,
            "statement"=> "",
            "details"=> [
                "referenceId"=> $splitPayment->payment_result?->payment?->externalReferenceId??"",
                //"debitAccountId"=> $transaction->debit_account_id??null,
                "remarks"=>$splitPayment->remarks,
                "amount"=>$splitPayment->amount,
                "fee"=>$splitPayment->fee,
            ]
        ]);
    }
}
