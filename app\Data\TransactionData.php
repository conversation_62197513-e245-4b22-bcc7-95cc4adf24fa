<?php

namespace App\Data;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class TransactionData extends Data
{
    public ?string $id;

    public ?CurrencyAmountData $amount;

    public string $date;
    public ?string $description;

    public ?string $transactionType;

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['id']= $properties["trn"];
        $properties['amount']= CurrencyAmountData::from([
            "amount"=>$properties["amt"],
            "currency"=>$properties["ccy"],
        ]);
        $properties['date']= $properties["atd"];
        $properties['description']= $properties["lab"];
        $properties['transactionType']= $properties["tsi"]=="Cr"?"C":($properties["tsi"]=="Dr"?"D":"");

        return $properties;
    }

}
