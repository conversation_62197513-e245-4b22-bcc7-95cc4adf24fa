<?php

namespace App\Services;
use App\Data\AccountIdData;
use App\Data\GeneralResponseData;
use App\Data\TokenData;

use Illuminate\Support\Facades\Http;

use App;
use App\Models\User;

/**
 * Service to create and update orders
 */
class LoyaltyService
{
    protected $settings;

    /**
     * Service to create and update orders
     */
    public function __construct()
    {
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->loyalty;
        if($this->settings->is_test){
            $this->settings=$this->settings->test_data;
        }
    }
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }
        if(is_null($token->access_token)||empty($token->access_token)){
            $response = Http::timeout(10)

            ->asForm()
            ->post("{$this->settings->url}/login",[
                "email"=>$this->settings->client_id,
                "password"=>$this->settings->client_secret
            ]);


            if ($response->failed()) {
                return $response;
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->token)){
                $this->settings->token=TokenData::from([
                    "access_token"=>$response->token
                ]);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $loyalty=$settings->loyalty;
                if($loyalty->is_test){
                    $test_data=$loyalty->test_data;
                    $test_data->token=$this->settings->token;
                    $loyalty->test_data=$test_data;
                }else{
                    $loyalty->token=$this->settings->token;
                }
                $settings->loyalty=$loyalty;
                $settings->save();
            }
           // dd($response);

        }
        return $status;
    }
    public static function getCustomerToken($request): string|null
    {
        $loyaltyService=new static();
        $status=$loyaltyService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return null;
        }

        $params=[
            "name"=>$request->name,
        ];
        if(isset($request->email)){
            $params["email"]=$request->email;
        }
        if(isset($request->phone)){
            $params["phone"]=$request->phone;
        }
        $response = Http::timeout(120)
        ->withToken($loyaltyService->settings->token->access_token)
        ->post($loyaltyService->settings->url."/member/token",$params);

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::getCustomerToken($request);
        }

        $result=$response->object();

        if(!$response->failed() && isset($result->token)){
            return $result->token;
        }
        return null;
    }
    public static function getCustomerIdentifier($request): string|null
    {
        $loyaltyService=new static();
        $status=$loyaltyService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return null;
        }

        $params=[
            "name"=>$request->name,
        ];
        if(isset($request->email)){
            $params["email"]=$request->email;
        }
        if(isset($request->phone)){
            $params["phone"]=$request->phone;
        }
        $response = Http::timeout(120)
        ->withToken($loyaltyService->settings->token->access_token)
        ->post($loyaltyService->settings->url."/member/identifier",$params);

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::getCustomerIdentifier($request);
        }

        $result=$response->object();

        if(!$response->failed() && isset($result->identifier)){
            return $result->identifier;
        }
        return null;
    }

    public static function addPurchase($request): GeneralResponseData
    {
        $loyaltyService=new static();
        $status=$loyaltyService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $response = Http::timeout(120)
        ->withToken($loyaltyService->settings->token->access_token)
        ->post($loyaltyService->settings->url."/cards/{$request->cardId}/{$request->memberId}/transactions/purchases",
            [
                "note"                      => $request->note??"",
                "purchase_amount"           => $request->amount,
                "staffId"                   => $loyaltyService->settings->instid,
            ]
        );
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::addPurchase($request);
        }

        $result=$response->object();

        if(!$response->failed() && isset($result->id)){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully add purchase"),
                        "detail"  =>"",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ))->additional([
                "response"=>$result
            ]);
        }
        $msg=__("Can't process your request, please connect help desk to fix problem!");
        if(isset($result->messages)){
            $msg=join("\n",$result->messages);
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[LOYALTY_SERVER]",
                "message"   => [
                    "title"   => $msg,
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_LOYALTY_001",
                    "type"    => "ERROR"
                ]
             ]
        ))->additional([
            "response"=>$result
        ]);
    }

    public static function addPoints($request): GeneralResponseData
    {
        $loyaltyService=new static();
        $status=$loyaltyService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $response = Http::timeout(120)
        ->withToken($loyaltyService->settings->token->access_token)
        ->post($loyaltyService->settings->url."/cards/{$request->cardId}/{$request->memberId}/transactions/points",
            [
                "note"                      => $request->note??"",
                "image"                     => $request->image??"",
                "points"                    => $request->points,
                "staffId"                   => $loyaltyService->settings->instid,
            ]
        );
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::addPoints($request);
        }

        $result=$response->object();

        if(!$response->failed() && isset($result->id)){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully add purchase"),
                        "detail"  =>"",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ))->additional([
                "response"=>$result
            ]);
        }
        $msg=__("Can't process your request, please connect help desk to fix problem!");
        if(isset($result->messages)){
            $msg=join("\n",$result->messages);
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[LOYALTY_SERVER]",
                "message"   => [
                    "title"   => $msg,
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_LOYALTY_001",
                    "type"    => "ERROR"
                ]
             ]
        ))->additional([
            "response"=>$result
        ]);
    }
    /**
     * Get partner cards
     *
     * @param  $request
     * @return mixed
     */
    public static function getCards($request)
    {
        $loyaltyService=new static();
        $status=$loyaltyService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return null;
        }

        $response = Http::timeout(120)
        ->withToken($loyaltyService->settings->token->access_token)
        ->post($loyaltyService->settings->url."/cards",[]);

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::getCards($request);
        }

        $result=$response->object();

        if(!$response->failed() && count($result)){
            return $result;
        }
        return [];
    }

}
