<?php

namespace App\Http\Controllers\Digx\Merchant;

use App\Data\AccountIdData;
use App\Data\GeneralResponseData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\GiftStatusEnum;
use App\Scopes\CustomerScope;
use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\Gift;
use App\Models\GiftTransaction;
use App\Models\InvoiceTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Services\FlexService;
use App\Services\NotificationService;

class InvoiceController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            //'externalReferenceId'=>"required",
            'txnToken'=>"required",
        ]);

        if($validator->fails()){
            return response()->json(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INFO-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }
        $invoice=Invoice::with("transactions")
        ->where("initiator_id",$request->user()->id)
        ->where("initiator_type","sdk")
        // ->where(function($query) use($request){
        //     $query->whereHas('transactions',function($query) use($request){
        //         return $query->where("external_reference_id",$request->externalReferenceId);
        //     })->orWhere("external_reference_id",$request->externalReferenceId);
        // })
        ->where("txn_token",$request->txnToken)
        ->first();

        if(!is_null($invoice)){
             // Get the payment transaction to get reference_id
            $transaction=$invoice->transactions
            ->whereIn("type",[InvoiceTransactionsTypeEnum::Payment,InvoiceTransactionsTypeEnum::Claim])
            ->where("status",TransactionStatusEnum::COMPLETED->value)
            ->first();

            $status='Initiate';
            $externalReferenceId=null;
            if($invoice->status==TransactionStatusEnum::COMPLETED->value && $transaction?->status==TransactionStatusEnum::COMPLETED->value) {
                $status='Complete';
                $externalReferenceId=$transaction?->external_reference_id;
            }

            // Get already excisting reverse transaction.
            $transaction=$invoice->transactions
            ->where("type",InvoiceTransactionsTypeEnum::Reverse)
            ->first();
            if($invoice->status==TransactionStatusEnum::COMPLETED->value && $transaction?->status==TransactionStatusEnum::COMPLETED->value) {
                $status='Refund';
                $externalReferenceId=$transaction?->external_reference_id??$externalReferenceId;
            }

            if($invoice->status==TransactionStatusEnum::ERROR->value) {
                $status='Error';
            }
            $partyId=null;
            if(!is_null($invoice->customer_account_id)){
                $partyId=AccountIdData::from($invoice->customer_account_id)->partyId();
            }
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ])->additional([
                "externalReferenceId"=>$externalReferenceId,
                "internalTxn"=>[
                    "id"            =>$invoice->id,
                    "partyId"       =>$partyId,
                    "debitAccountId"=>$invoice->debit_account_id,
                    "amount"        =>$invoice->amount,
                    "purpose"       =>$invoice->purpose,
                    "remarks"       =>$invoice->remarks,
                    "date"          =>Carbon::parse($invoice->created_at)->toDateTimeString(),
                    "status"        =>$status
                ],
               // "transaction"=>$transaction,
               // "transactions"=>$invoice->transactions,

            ]));

            // $object=new \stdClass();
            // $object->trxId= $request->externalReferenceId;
            // //$object->trxId= substr_replace($request->externalReferenceId, 'BKOP', 3, 4);

            // $result=FlexService::checkTransactionStatus($object);
            // if($result->status->message->code!="0"){
            //     return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            // }else{
            //     return response()->json($result->toArray() + array(
            //         "internalTxn"=>[
            //             "id"=>$invoice->id,
            //             "debitAccountId"=>$invoice->debit_account_id,
            //             "amount"=>$invoice->amount,
            //             "purpose"=>$invoice->purpose,
            //             "remarks"=>$invoice->remarks,
            //         ]
            //     ));
            // }

        }
        return response()->json(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "INFO-INVOICE",
                "message"   => [
                    "title"   => __('Transaction not exist!'),
                    "detail"  => __('Transaction not exist!'),
                    "code"    => "DIGX_SWITCH_INVOICE_200",
                    "type"    => "ERROR"
                ]
             ]
        ));
    }

    /**
     * Initiate
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $merchant=$request->user()->merchant;
        if($merchant->is_collector){
            $validations=[
                'code'=>"required",
                "customerPhone"=> "required",
            ];
        }else{
            $validations=[
                'amount.amount'=>"required|numeric",
                'amount.currency'=>"required",
                "customerPhone"=> "nullable",
                "orderId"=> "nullable",
            ];
        }

        $validator=validator()->make($request->all(),$validations);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }


        if($merchant->is_collector){
            $valid=false;
            $message="";
            $sharing=\App\Models\Sharing::where("txn_token",$request->code)->first();
            if($sharing?->type!='gift'){
                $message="This code is not exist or already expired!";
            }else{
                $transaction=GiftTransaction::with(['gift'=>function($query){
                    return $query->withoutGlobalScope(CustomerScope::class);
                }])
                ->where("id",$sharing->data->id)
                ->where("status",GiftStatusEnum::SUCCESS->value)
                ->first();

                if(is_null($transaction) || $transaction->status!=1){
                    $message="This transaction already expired";
                }else {
                    $currency=$transaction->amount->currency;
                    $creditAccountId=AccountIdData::from([
                        "value"=>$request->user()->merchant->account_id
                    ]);

                    $debitAccountId=AccountIdData::from($transaction->gift->debit_account_id);

                    if($transaction->payee->transferValue != $request->customerPhone){
                        $message="Customer dosen't have permission to claim this gift!";
                    }else if($creditAccountId->currencyId()!=$currency){
                        $message="Merchant account not accept this payment from this currency account!";
                    }else if(!(($creditAccountId->currencyId()==\App\Enums\CurrencyTypeEnum::YER->value && $creditAccountId->isNorth()==$debitAccountId->isNorth()) ||
                        $creditAccountId->currencyId()!=\App\Enums\CurrencyTypeEnum::YER->value)){
                        $message="Merchant account not accept this payment from this currency account!";
                    }else{
                        $valid=true;
                    }
                }
            }
            if(!$valid){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CLAIM-GIFT",
                        "message"   => [
                            "title"   => __($message),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_INVOICE_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }

            $request->request->add([
                'amount'=>[
                    'amount'=>$transaction->amount->amount,
                    'currency'=>$transaction->amount->currency,
                ],
                "purpose"=>'Other',
                "remarks"=>"Claim gift code"
            ]);
            $amount=$request->input("amount.amount");

        }else{
            if($request->filled("orderId")){
                $invoice=Invoice::where("initiator_id",$request->user()->id)
                ->where("initiator_type","sdk")
                ->whereNotNull("order_id")
                ->where("order_id",$request->orderId)
                ->first();


                if(!is_null($invoice)){
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "STORE-INVOICE",
                            "message"   => [
                                "title"   => __("Duplicated order id!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_INVOICE_101",
                                "type"    => "ERROR"
                            ]
                         ]
                    ]));
                }
            }
            $amount=ceil($request->input("amount.amount") * 100) / 100;
        }


        $invoice=Invoice::create([
            "txn_token"=>(string) Str::orderedUuid(),
            "initiator_id"     =>$request->user()->id,
            "initiator_type"   =>"sdk",
            "status"            =>TransactionStatusEnum::INIT->value,
            "debit_account_id"=>[
                "displayValue"=> $request->user()->merchant->name,
                "value"       => $request->user()->merchant->phone,
            ],
            "customer_phone"=>$request->customerPhone??null,
            "order_id"=>$request->orderId??null,
            "amount"=>[
                "amount"=> $amount,
                "currency"=>  $request->input("amount.currency"),
            ],
            "purpose"=>$request->purpose,
            "remarks"=>$request->remarks
        ]);
        LogItem::store($invoice);

        if($merchant->is_collector){
            $result=[
                "txnToken"=>$invoice->txn_token,
                'amount'=>$invoice->amount
            ];
        }else{
            $sharing=\App\Models\Sharing::create([
                "txn_token"  =>$invoice->txn_token,
                "type"       =>"transfer",
                "party_id"   =>$request->user()->id
            ]);
            $result=[
                "txnToken"=>$sharing->txn_token,
            ];
        }


        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-INVOICE-$invoice->id",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional($result));

    }

    /**
     * Initiate
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'txnToken'=>"required",
            'code'=>"required",
        ]);

        $valid=false;
        $message="";
        if($validator->fails()){
            $message=join("\n",$validator->errors()->all());
        }else {
            $invoice=Invoice::where("initiator_id",$request->user()->id)
            ->with("transactions")
            ->where("initiator_type","sdk")
            ->where("txn_token",$request->txnToken)
            ->first();

            LogItem::store($invoice);
            if(is_null($invoice) || count($invoice->transactions)!=0 && $invoice->status!=TransactionStatusEnum::INIT->value){
                $message="Transaction not exist!";
            }else {

                $sharing=\App\Models\Sharing::where("txn_token",$request->code)->first();
                if($sharing?->type!='gift'){
                    $message="This code is not exist or already expired!";
                }else{
                    $transaction=GiftTransaction::with(['gift'=>function($query){
                        return $query->withoutGlobalScope(CustomerScope::class);
                    }])
                    ->where("id",$sharing->data->id)
                    ->where("status",1)->first();

                    if(is_null($transaction) || $transaction->status!=1){
                        $message="This transaction already expired";
                    }else {
                        $currency=$transaction->amount->currency;
                        $creditAccountId=AccountIdData::from([
                            "value"=>$request->user()->merchant->account_id
                        ]);

                        $debitAccountId=AccountIdData::from($transaction->gift->debit_account_id);

                        if($transaction->payee->transferValue != $invoice->customer_phone){
                            $message="You don't have permission to claim this gift!";
                        }else if($creditAccountId->currencyId()!=$currency){
                            $message="Your account not allowed to use this service!";
                        }else if($invoice->amount->amount!=$transaction->amount->amount){
                            $message="Amount of transaction not equal the gift code amount";
                        }else if($invoice->amount->currency!=$currency){
                            $message="Gift currency not match invoice currency!";
                        }else if(!(($creditAccountId->currencyId()==\App\Enums\CurrencyTypeEnum::YER->value && $creditAccountId->isNorth()==$debitAccountId->isNorth()) ||
                            $creditAccountId->currencyId()!=\App\Enums\CurrencyTypeEnum::YER->value)){
                            $message="Merchant account not accept this payment from this currency account!";
                        }else{
                            $valid=true;
                        }
                    }
                }
            }
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CLAIM-GIFT",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        if(is_null($transaction->claim_reference_id)){
            $transaction->claim_reference_id= uniqid();
            $transaction->save();
        }

        $gift=Gift::withoutGlobalScope(CustomerScope::class)
        ->where("id",$transaction->gift_id)->first();
        LogItem::store($gift);

        $object=new \stdClass();
        $object->reference_id   = $transaction->claim_reference_id;
        $object->service_name   = ThirdPartyServiceNameData::gift();
        $object->account_id     = $creditAccountId->value;
        $object->amount         = $transaction->amount->amount;
        $object->currency       = $transaction->amount->currency;
        $object->remarks        = trans("gift_recive")." ".html_entity_decode($gift->party_name, ENT_QUOTES, "UTF-8");


        $result=FlexService::accountToDebit($object);
        if($result->status->message->code=="0"){
            $transaction->claim_external_reference_id= $result->getAdditionalData()["externalReferenceId"];
            $transaction->credit_account_id=$request->input("creditAccountId");
            $transaction->claim_party_id=$creditAccountId->partyId();
            $transaction->status=2;
            $transaction->save();

            $invoice->external_reference_id= $result->getAdditionalData()["externalReferenceId"];
            $invoice->status=TransactionStatusEnum::COMPLETED->value;
            $invoice->save();

            $invoiceTransaction = InvoiceTransaction::create([
                "invoice_id"=>$invoice->id,
                "type"      =>InvoiceTransactionsTypeEnum::Claim,
                "status"    =>TransactionStatusEnum::COMPLETED->value,
                "amount"=>[
                    "amount"=> $invoice->amount->amount,
                    "currency"=>  $invoice->amount->currency,
                ],
                "reference_id"=>  $transaction->claim_reference_id,
                "external_reference_id"=>  $invoice->external_reference_id,
                "remarks"=>$invoice->remarks,
                "gift_id"=>  $transaction->gift_id,
                "gift_transaction_id"=>  $transaction->id,
            ]);

            $user_fullname=$request->user()->merchant->name;
            $smsMessage = sprintf(trans("claim_gift_sms_message_marchent_new"),
                html_entity_decode($gift->party_name, ENT_QUOTES, "UTF-8"),
                html_entity_decode($transaction->payee->nickName, ENT_QUOTES, "UTF-8"),
                html_entity_decode($user_fullname, ENT_QUOTES, "UTF-8"),
                $transaction->amount->amount . " " . $transaction->amount->currency

            );

            if (!is_null($transaction->sender_phone)){
                NotificationService::sendSMS([
                    "mobile" => $transaction->sender_phone,
                    "message" => $smsMessage
                ]);
            }
            return response()->json($result);
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);

    }
    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Invoice  $invoice
     * @return \Illuminate\Http\JsonResponse
     */
    // public function byToken(Request $request,Invoice $_invoice)
    // {
    //     $validator=validator()->make($request->all(),[
    //         'txnToken'=>"required",
    //     ]);

    //     if($validator->fails()){
    //         return response()->json(GeneralResponseData::from([
    //             'status'=>[
    //                 "result"    => "ERROR",
    //                 "contextID" => "INFO-INVOICE",
    //                 "message"   => [
    //                     "title"   => join("\n",$validator->errors()->all()),
    //                     "detail"  => join("\n",$validator->errors()->all()),
    //                     "code"    => "DIGX_SWITCH_INVOICE_100",
    //                     "type"    => "ERROR"
    //                 ]
    //              ]
    //         ]));
    //     }
    //     $invoice=Invoice::where("txn_token",$request->txnToken)->first();
    //     if(!is_null($invoice)){
    //         return response()->json(GeneralResponseData::from([
    //             'status'=>[
    //                 "result"    => "SUCCESSFUL",
    //                 "contextID" => "INFO-INVOICE-$invoice->id",
    //                 "message"   => [
    //                     "title"   => "",
    //                     "detail"  => "",
    //                     "code"    => "0",
    //                     "type"    => "INFO"
    //                 ]
    //             ]
    //         ])->additional([
    //             "transferDetails"=>
    //             [
    //                 "id"=>$invoice->id,
    //                 "debitAccountId"=>$invoice->debit_account_id,
    //                 "amount"=>$invoice->amount,
    //                 "purpose"=>$invoice->purpose,
    //                 "remarks"=>$invoice->remarks
    //             ],
    //         ]));
    //     }
    //     return response()->json(GeneralResponseData::from([
    //         'status'=>[
    //             "result"    => "ERROR",
    //             "contextID" => "INFO-INVOICE",
    //             "message"   => [
    //                 "title"   => __('Transaction not exist!'),
    //                 "detail"  => __('Transaction not exist!'),
    //                 "code"    => "DIGX_SWITCH_INVOICE_200",
    //                 "type"    => "ERROR"
    //             ]
    //          ]
    //     ]));
    // }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $externalReferenceId
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $externalReferenceId)
    {
        $validator=validator()->make($request->all(),[
            //'externalReferenceId'=>"required",
            'txnToken'=>"required",
        ]);

        if($validator->fails()){
            return response()->json(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INFO-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $invoice=Invoice::where("initiator_id",$request->user()->id)
        ->with("transactions")
        ->where("initiator_type","sdk")
        ->where("external_reference_id", $externalReferenceId)
        ->where("txn_token",$request->txnToken)
        ->first();

        if(is_null($invoice)){
            return response()->json(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "REFUND-INVOICE",
                    "message"   => [
                        "title"   => __('Transaction not exist!'),
                        "detail"  =>"",
                        "code"    => "DIGX_SWITCH_INVOICE_102",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }
        LogItem::store($invoice);

        // Get the payment transaction to get reference_id
        $invoicePaymentTransaction=$invoice->transactions
        ->where("type",InvoiceTransactionsTypeEnum::Payment)
        ->where("status",TransactionStatusEnum::COMPLETED->value)
        ->first();

        // Get already excisting reverse transaction.
        $invoiceTransaction=$invoice->transactions
        ->where("type",InvoiceTransactionsTypeEnum::Reverse)
        ->first();

        if( $invoice->status!=TransactionStatusEnum::COMPLETED->value ||
            is_null($invoicePaymentTransaction) || (
                !is_null($invoiceTransaction) && (
                    $invoiceTransaction->status != TransactionStatusEnum::INIT->value ||
                    !is_null($invoiceTransaction->external_reference_id)
                )
            )
        ) {
            return response()->json(GeneralResponseData::from([
                'status' => [
                    "result" => "ERROR",
                    "contextID" => "REFUND-INVOICE-$invoice->id",
                    "message" => [
                        "title" => __("This transaction not in the correct status to refund!"),
                        "detail" => "",
                        "code" => "DIGX_SWITCH_INVOICE_101",
                        "type" => "ERROR"
                    ]
                ]
            ]));
        }else if(is_null($invoiceTransaction)){
            $invoiceTransaction = InvoiceTransaction::create([
                "invoice_id"=>$invoice->id,
                "type" =>  InvoiceTransactionsTypeEnum::Reverse,
                "status"=>TransactionStatusEnum::INIT->value,
                "amount"=>[
                    "amount"=> $invoice->amount->amount,
                    "currency"=>  $invoice->amount->currency,
                ],
                "reference_id"=>  $invoicePaymentTransaction->reference_id,//round(microtime(true)),
                "remarks"=>$request->remarks
                ]
            );
        }

        $object=new \stdClass();
        $object->reference_id   = $invoiceTransaction->reference_id;
        $object->service_name   = $request->user()->merchant->service_name;
        $object->account_id     = $invoice->customer_account_id->value;
        // $object->amount         = $invoiceTransaction->amount->amount;
        // $object->currency       = $invoiceTransaction->amount->currency;
        // $object->remarks        = sprintf(trans("Refund for transaction [%s] [%s]"),$invoice->external_reference_id,$invoice->remarks);

        $result=FlexService::reverseToAccount($object);
        if($result->status->message->code=="0"){
            // $invoiceTransaction->external_reference_id= isset($result->getAdditionalData()["externalReferenceId"])?
            //     $result->getAdditionalData()["externalReferenceId"]:
            //     $invoicePaymentTransaction->external_reference_id
            // ;
            $invoiceTransaction->status=TransactionStatusEnum::COMPLETED->value;
            $invoiceTransaction->save();

            return response()->json($result);
        }
        $invoiceTransaction->status=TransactionStatusEnum::ERROR->value; //Reverse fail
        $invoiceTransaction->save();

        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }
}
