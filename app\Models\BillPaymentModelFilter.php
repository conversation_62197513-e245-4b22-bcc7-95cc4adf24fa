<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class BillPaymentModelFilter extends Model
{
    use HasFactory;

    protected $fillable = ['model','model_id','bill_payment_filter_id','parent_filter_id'];
    protected $casts = [
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    public function lists(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, 'model', 'model_id');
    }
}
