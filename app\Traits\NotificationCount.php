<?php

namespace App\Traits;

use App\Services\OBDX\CustomerService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Notification;

trait NotificationCount
{
    /**
     * Count of notification available
     *
     * @return array
     */
    public function getNotificationCount(){
        $request=request();
        $user=auth()->user();
        $notificationCounts=Notification:://where('title','body','image','action','url','created_at')
        where(function ($query) use($user){
            $query->whereHas('messageUserMappings', function ($query) use($user) {
                return $query->where('customer_id',$user->id);
            })
            ->orWhere(function ($query) use($user){
                $query->where('type','brodcast')
                ->where(function ($query) use($user){
                    $query->whereHas('customerType', function ($query) use($user) {
                        return $query->where('id',$user->customerType);
                    })
                    ->orWhere('topic','main');
                });
            });
        })
        ->where(function ($query) use($user,$request){
            return $query->whereHas('service', function ($query) use($user,$request) {
                return $query->whereNotNull('service_id')
                ->where(function ($query)  use($request){
                    return $query->where('status',1)
                    ->orWhere(function ($query) use($request){
                        return $query->where('status',3)
                        ->where('app_version','<=',($request->header('appVersion')??0));
                    });
                })
                ->whereHas('customerTypes', function ($query) use($user) {
                    return $query->where('id',$user->customerType)
                    ->whereNull('page_service_id');
                });
            })
            ->orWhereNull('service_id');
        })
        ->where('status',1)   
        ->whereNotIn('id', function ($query) use ($user) {
            return $query->selectRaw('notification_id')
                ->from('customer_notifications')
                ->where('customer_id', $user->id)
                ->where('customer_type_id', $user->customerType)
                ->where('status','<>','U');
        })
        ->count();

        $result=CustomerService::mailboxCount();
        if(!is_null($result)){
            collect($result->items)
            ->each(function(&$item) use($notificationCounts){
                if($item->messageType=="B"){
                    $item->unReadCount=/*$item->unReadCount+*/$notificationCounts;
                }
            });

            $result->total=collect($result->items)
            ->sum('unReadCount');
        }else{
            $result=[
                "total"=>$notificationCounts,
                "items"=>[
                    [
                        "messageType"=>"B",
                        "unReadCount"=>$notificationCounts,
                    ]
                ],

            ];
        }
        return $result;
    }

}

