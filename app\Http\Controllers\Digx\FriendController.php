<?php

namespace App\Http\Controllers\Digx;
use App\Data\GeneralResponseData;

use App\Http\Controllers\Controller;
use App\Models\Party;
use App\Models\PartyFriend;
use App\Services\OBDX\AdminService;
use Crypt;
use Illuminate\Http\Request;

class FriendController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $friends=Party::select("id", "name", "image")
            ->whereHas('friends'/*,function($query){
                return $query->withoutGlobalScope(UsernameScope::class);
            }*/)
            ->get();
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            'friends'=>$friends,
        ]));

    }
    /**
     * Delete the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {

        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-FRIEND",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_FRIEND_101",
                    "type"    => "ERROR"
                ]
            ]
        ]);
        try {
            $profile=new \stdClass();
            $partyId=Crypt::decrypt($request->token);

            if (strpos($partyId, ':') !== false) {
                $parts = explode(':', $partyId);
                $partyId=$parts[0];
                $username=$parts[1];
                // $parts[1] will be 'value'
            }

            $party=Party::select("id", "name", "image","party_id")
                ->where('party_id',$partyId);
            if (isset($username)) {
                $party = $party->where('username',$username);
            }
            $party=$party->first();

            if(is_null($party)){
                $result=AdminService::sharingProfile($request->token);
                if($result->status->message->code=="0"){
                    $profile=$result->getAdditionalData()['profile'];
                    $party=Party::create([
                        "name"=>$profile->name,
                        "username"=>isset($username)?$username:null,
                        "party_id"=>$partyId,
                    ]);
                }else{
                    $errorResult->status->message->title = __("Profile not found!");
                    return response()->json($errorResult,400);
                }
            }

            if($party->party_id==auth()->user()->id){
                $errorResult->status->message->title = __("You can't add your self!");
                return response()->json($errorResult,400);
            }
            $partyFriend=PartyFriend::where('friend_id',$party->id)
            ->first();

            if(is_null($partyFriend)){
                $partyFriend=PartyFriend::create([
                    "friend_id"=>$party->id
                ]);

                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("Successfully add friend to your friends list"),
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ])->additional([
                    "friend_id"=>$partyFriend->friend_id
                ]));
            }
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            $errorResult->status->message->title = __("Profile not found!");
            return response()->json($errorResult,400);
        }

        $errorResult->status->message->title = __("You already add him to your friends list!");
        return response()->json($errorResult,400);
    }

    /**
     * Delete the specified resource in storage.
     *
     * @param  $friend
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        PartyFriend::where('friend_id',$id)
            ->delete();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully delete device from list"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }

}
