<?php

namespace App\Data\Form;

use App\Data\BaseNonNullableData;
use App\Data\NameData;
use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\DataCollection;

class FormOptionData extends BaseNonNullableData
{
    public function __construct(
        public ?string $id,
        #[MapOutputName('ne'),MapInputName('ne')]
        public ?NameData $name=null,
        #[MapOutputName('dn'),MapInputName('dn')]
        public ?string $description=null,
        #[MapOutputName('st'),MapInputName('st')]
        public ?string $subtitle=null,
        #[MapOutputName('img'),MapInputName('img')]
        public ?string $image=null,
        #[MapOutputName('ul'),MapInputName('ul')]
        public ?string $url=null,
        #[MapOutputName('fs'),MapInputName('fs'),DataCollectionOf(FormBaseData::class)]
        public ?DataCollection $fields=null,
        #[MapOutputName('sd'),MapInputName('sd')]
        public ?bool $selected=null
    ) {
    }

    // public static function prepareForPipeline(array $properties) : array
    // {
    //     $properties['debitAccountId']= data_get($properties,"payload.debitAccountId");
    //     $properties['amount']= data_get($properties,"payload.amount");
    //     $properties['fee']=data_get($properties,"payload.fee");
    //     $properties['receiverNumber']=data_get($properties,"payload.receiverNumber",);
    //     $properties['receiverName']=data_get($properties,"payload.receiverName");
    //     $properties['bankCode']=data_get($properties,key: "payload.bankCode");
    //     $properties['remarks']=data_get($properties,key: "payload.remarks");

    //     return $properties;
    // }
}
