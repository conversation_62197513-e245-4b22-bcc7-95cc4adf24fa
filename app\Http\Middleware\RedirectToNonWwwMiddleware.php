<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use URL;

class RedirectToNonWwwMiddleware

{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        // if (str_contains($request->getHttpHost(),"banky")) {
        //     URL::forceSchema('https');
        // }
        
        // if(env('APP_ENV', 'production')=='production' && !((int)strpos($request->header('host'), '.test')) && ((int)strpos($request->header('host'), 'banky'))){
        //     if (!$request->secure()) {
        //         $request->headers->set('host', str_replace("https://","",str_replace("http://","",env('APP_URL'))));
        //         return redirect()->secure($request->path());
        //     }
        // }

        return $next($request);
    }
}
