<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserInterface;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

use DB;
use Auth;

class RolesController extends Controller
{

    protected $list = ['*' => 'الكل', 'list' => 'عرض القائمة', 'view' => 'عرض التفاصيل', 'create' => 'انشاء', 'edit' => 'تعديل', 'delete' => 'حذف'];
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of Role.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!Auth::user()->canAny(['management.role.*', 'management.role.list']))
            return abort(401);
        $roles = Role::where('name', '<>', 'developer')->with("permissions")->paginate(25); //->get();
        $user_interfaces = UserInterface::orderBy('sort');
        // if(!Auth::user()->hasRole('developer')){
        //     $user_interfaces = $user_interfaces->where('status','!=',3);
        // }
        $user_interfaces =$user_interfaces->pluck('name', 'identifier')->toArray();

        return view('default.admin.roles.index', compact('roles', 'user_interfaces'))
        ->with('list', $this->list);
    }

    /**
     * Show the form for creating new Role.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (!Auth::user()->canAny(['management.role.*', 'management.role.create']))
            return abort(401);

        $permissions =UserInterface::with(['permissions'=>function($quary){
            $quary->with(['subs'=>function($quary){
                $quary->select(DB::raw("max(id) as id"),DB::raw("max(name) as full_name"),"name_level2",DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',3),'.',-1) as name"))
                ->groupBy(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',3),'.',-1)"))->groupBy("name_level2")
                ->orderBy(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',3),'.',-1)"));
            }])
            ->select(DB::raw("max(id) as id"),DB::raw("max(name) as full_name"),'name_level1',"name_level2",DB::raw("max(SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',2),'.',-1)) as name"))
            ->where(function ($query) {
                return $query->whereNotIn('name_level1', function ($query){
                    $query->selectRaw('identifier_level1')
                        ->from('user_interfaces')
                        ->whereRaw("identifier_level1!=SUBSTRING_INDEX(SUBSTRING_INDEX(identifier, '.', 2), '.', -1)");

                    // if(Auth::user()->hasRole('developer')){
                    //     $query->whereIn('status', [1,2,3]);
                    // }else{
                    //     $query->whereIn('status', [1,2]);
                    // }
                    return $query;
                })->orWhereIn('name_level2', function ($query) {
                    $query->selectRaw('identifier_level2')
                        ->from('user_interfaces')
                        ->whereRaw("identifier_level1!=SUBSTRING_INDEX(SUBSTRING_INDEX(identifier, '.', 2), '.', -1)");
                    if(Auth::user()->hasRole('developer')){
                        $query->whereIn('status', [1,2,3]);
                    }else{
                       $query->whereIn('status', [1,2]);
                    }
                    return $query;
                });
            })
            ->groupBy("name_level1")
            ->groupBy("name_level2")
            ->orderBy(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',2),'.',-1)"));

        }])
        ->select(DB::raw("max(id) as id"),DB::raw("max(name) as name"),"identifier_level1")
        ->groupBy("identifier_level1");

        if(env('DB_CONNECTION')!='oracle'){
            $permissions = $permissions->orderBy(DB::raw("max(sort)"));
        }else{
            $permissions = $permissions->orderBy(DB::raw("max(sort) keep (dense_rank first order by IDENTIFIER_LEVEL2 asc)"));
        }

        if(Auth::user()->hasRole('developer')){
            $permissions = $permissions->whereIn('status', [1,2,3]);
        }else{
            $permissions = $permissions->whereIn('status', [1,2]);
        }
        $permissions =$permissions->get();

        $user_interfaces = UserInterface::pluck('name', 'identifier')->toArray();
        return view('default.admin.roles.edit', compact('permissions', 'user_interfaces'))->with('list', $this->list);
    }

    /**
     * Store a newly created Role in storage.
     *
     * @param  \Illuminate\Http\request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!Auth::user()->canAny(['management.role.*', 'management.role.create']))
            return abort(401);
        $role = Role::create($request->except('permission'));
        $permissions = $request->input('permission') ? $request->input('permission') : [];
        $role->givePermissionTo($permissions);
        return redirect()->route('admin.roles.index');
    }


    /**
     * Show the form for editing Role.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Role $role)
    {
        if (!Auth::user()->hasRole('developer')) {
            if (in_array($role->id, [2])) {
                return abort(401);
            }
            if (in_array($role->name, ['developer','تاجر'])) {
                return abort(401);
            }
            if (!Auth::user()->canAny(['management.role.*', 'management.role.edit', 'management.role.view'])) {
                return abort(401);
            }
        }
        $permissions =UserInterface::with(['permissions'=>function($quary){
            $quary->with(['subs'=>function($quary){
                $quary->select(DB::raw("max(id) as id"),DB::raw("max(name) as full_name"),"name_level2",DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',3),'.',-1) as name"))
                ->groupBy(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',3),'.',-1)"))->groupBy("name_level2")
                ->orderBy(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',3),'.',-1)"));
            }])
            ->select(DB::raw("max(id) as id"),DB::raw("max(name) as full_name"),'name_level1',"name_level2",DB::raw("max(SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',2),'.',-1)) as name"))
            ->where(function ($query) {
                return $query->whereNotIn('name_level1', function ($query){
                    $query->selectRaw('identifier_level1')
                        ->from('user_interfaces')
                        ->whereRaw("identifier_level1!=SUBSTRING_INDEX(SUBSTRING_INDEX(identifier, '.', 2), '.', -1)");
                    // if(Auth::user()->hasRole('developer')){
                    //     $query->whereIn('status', [1,2,3]);
                    // }else{
                    //     $query->whereIn('status', [1,2]);
                    // }
                    return $query;
                })->orWhereIn('name_level2', function ($query) {
                    $query->selectRaw('identifier_level2')
                        ->from('user_interfaces')
                        ->whereRaw("identifier_level1!=SUBSTRING_INDEX(SUBSTRING_INDEX(identifier, '.', 2), '.', -1)");
                    if(Auth::user()->hasRole('developer')){
                        $query->whereIn('status', [1,2,3]);
                    }else{
                        $query->whereIn('status', [1,2]);
                    }
                    return $query;
                });
            })
            ->groupBy("name_level1")->groupBy("name_level2")
            ->orderBy(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',2),'.',-1)"));

        }])
        ->select(DB::raw("max(id) as id"),DB::raw("max(name) as name"),"identifier_level1")
        ->groupBy("identifier_level1");

        if(env('DB_CONNECTION')!='oracle'){
            $permissions = $permissions->orderBy(DB::raw("max(sort)"));
        }else{
            $permissions = $permissions->orderBy(DB::raw("max(sort) keep (dense_rank first order by IDENTIFIER_LEVEL2 asc)"));
        }

        if(Auth::user()->hasRole('developer')){
            $permissions = $permissions->whereIn('status', [1,2,3]);
        }else{
            $permissions = $permissions->whereIn('status', [1,2]);
        }
        $permissions =$permissions->get();
       // return response()->json($permissions->get()->toArray());

        $user_interfaces = UserInterface::orderBy('sort');
        // if(!Auth::user()->hasRole('developer')){
        //     $user_interfaces = $user_interfaces->where('status','!=',3);
        // }
        $user_interfaces =$user_interfaces->pluck('name', 'identifier')->toArray();

        //dd(compact('role', 'permissions', 'user_interfaces'));
       // return;
        return view('default.admin.roles.edit', compact('role', 'user_interfaces'))->with('permissions', $permissions)->with('list', $this->list);
    }

    /**
     * Update Role in storage.
     *
     * @param   \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Role $role)
    {
        if (!Auth::user()->hasRole('developer')) {
            if (in_array($role->id, [1, 2])) {
                return abort(401);
            }
            if (!Auth::user()->canAny(['management.role.*', 'management.role.edit'])) {
                return abort(401);
            }
        }

        $permissions = $request->input('permission') ? $request->input('permission') : [];
        if ($request->filled('_type')) {
            $role->givePermissionTo($permissions);
        } else {
            $role->update($request->except('permission'));
            $role->syncPermissions($permissions);
        }
        UserInterface::withCacheUpdate();
        return back()->with('success', __("Operation accomplished successfully"));

    }
    /*public function show(Role $role){
    $role->load('permissions');
    return view('default.admin.roles.show', compact('role'));
    }*/


    /**
     * Remove Role from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Role $role)
    {
        if (!Auth::user()->canAny(['management.role.*', 'management.role.delete']))
            return abort(401);
        $role->delete();
        UserInterface::withCacheUpdate();
        return redirect()->route('admin.roles.index');
    }

    /**
     * Delete all selected Role at once.
     *
     * @param Request $request
     */
    public function massDestroy(Request $request)
    {
        if (!Auth::user()->canAny(['management.role.*', 'management.role.delete']))
            return abort(401);
        Role::whereIn('id', request('ids'))->delete();
        UserInterface::withCacheUpdate();
        return response()->noContent();
    }

}
