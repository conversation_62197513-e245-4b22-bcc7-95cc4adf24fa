<?php

namespace App\Http\Controllers\Digx\Merchant;

use App\Data\GeneralResponseData;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PaymentController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            //'externalReferenceId'=>"required",
            'txnToken'=>"required",
        ]);

        if($validator->fails()){
            return response()->json(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INFO-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }
        $invoice=Invoice::with("transactions")
        ->where("initiator_id",$request->user()->id)
        ->where("initiator_type","sdk")
        // ->where(function($query) use($request){
        //     $query->whereHas('transactions',function($query) use($request){
        //         return $query->where("external_reference_id",$request->externalReferenceId);
        //     })->orWhere("external_reference_id",$request->externalReferenceId);
        // })
        ->where("txn_token",$request->txnToken)
        ->first();

        if(!is_null($invoice)){
             // Get the payment transaction to get reference_id
            $transaction=$invoice->transactions
            ->whereIn("type",[InvoiceTransactionsTypeEnum::Payment,InvoiceTransactionsTypeEnum::Claim])
            ->where("status",TransactionStatusEnum::COMPLETED->value)
            ->first();

            $status='Initiate';
            $externalReferenceId=null;
            if($invoice->status==TransactionStatusEnum::COMPLETED->value && $transaction?->status==TransactionStatusEnum::COMPLETED->value) {
                $status='Complete';
                $externalReferenceId=$transaction?->external_reference_id;
            }

            // Get already excisting reverse transaction.
            $transaction=$invoice->transactions
            ->where("type",InvoiceTransactionsTypeEnum::Reverse)
            ->first();
            if($invoice->status==TransactionStatusEnum::COMPLETED->value && $transaction?->status==TransactionStatusEnum::COMPLETED->value) {
                $status='Refund';
                $externalReferenceId=$transaction?->external_reference_id??$externalReferenceId;
            }

            if($invoice->status==TransactionStatusEnum::ERROR->value) {
                $status='Error';
            }
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ])->additional([
                "externalReferenceId"=>$externalReferenceId,
                "internalTxn"=>[
                    "id"            =>$invoice->id,
                    "debitAccountId"=>$invoice->debit_account_id,
                    "amount"        =>$invoice->amount,
                    "purpose"       =>$invoice->purpose,
                    "remarks"       =>$invoice->remarks,
                    "date"          =>Carbon::parse($invoice->created_at)->toDateTimeString(),
                    "status"        =>$status
                ]
            ]));

            // $object=new \stdClass();
            // $object->trxId= $request->externalReferenceId;
            // //$object->trxId= substr_replace($request->externalReferenceId, 'BKOP', 3, 4);

            // $result=FlexService::checkTransactionStatus($object);
            // if($result->status->message->code!="0"){
            //     return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            // }else{
            //     return response()->json($result->toArray() + array(
            //         "internalTxn"=>[
            //             "id"=>$invoice->id,
            //             "debitAccountId"=>$invoice->debit_account_id,
            //             "amount"=>$invoice->amount,
            //             "purpose"=>$invoice->purpose,
            //             "remarks"=>$invoice->remarks,
            //         ]
            //     ));
            // }

        }
        return response()->json(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "INFO-INVOICE",
                "message"   => [
                    "title"   => __('Transaction not exist!'),
                    "detail"  => __('Transaction not exist!'),
                    "code"    => "DIGX_SWITCH_INVOICE_200",
                    "type"    => "ERROR"
                ]
             ]
        ));
    }

    /**
     * Initiate
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validations=[
            'merchantCode'=>"required|numeric",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required",
            "customerPhone"=> "nullable",
            "orderId"=> "required",
        ];

        $validator=validator()->make($request->all(),$validations);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }


        if($request->filled("orderId")){
            $invoice=Invoice::where("initiator_id",$request->merchantCode)
            ->where("initiator_type","pay")
            ->whereNotNull("order_id")
            ->where("order_id",$request->orderId)
            ->first();

            if(!is_null($invoice)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-INVOICE",
                        "message"   => [
                            "title"   => __("Duplicated order id!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_INVOICE_101",
                            "type"    => "ERROR"
                        ]
                     ]
                ]));
            }
        }

        $response = rescue(function () use($request){
            return Http::withOptions([
                "allow_redirects"=>false,
            ])
            ->withUserAgent("OIC-Authentication")
            ->withHeaders(["accept-encoding"=>"identity"])
            ->asForm()
            ->post("{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1/payments/transfers/merchantTransferData", [
                "transactionAmt"=>$request->input("amount.amount"),
                "serviceCharges"=>0,
                "merchantCode"=>$request->merchantCode,
                "checksumValue"=>0,
                "successStaticFlag"=>"yes",
                "failureStaticFlag"=>"yes",
                "txnCurrency"=>$request->input("amount.currency"),
                "merchantRefNumber"=>$request->orderId??null,
            ]);
        }, function ($e) {
            return $e->getMessage();
        });

        if(is_string($response)){
            abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        if($response->failed()){
            abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }

        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->epiRefId)){
            $invoice=Invoice::create([
                "txn_token"=>(string) Str::orderedUuid(),
                "initiator_id"     =>$request->merchantCode,
                "initiator_type"   =>"ykbPayment",
                "status"            =>TransactionStatusEnum::INIT->value,

                "customer_phone"=>$request->customerPhone??null,
                "order_id"=>$request->orderId??null,
                "amount"=>[
                    "amount"=> $request->input("amount.amount"),
                    "currency"=>  $request->input("amount.currency"),
                ],
                "purpose"=>$result->epiRefId,
                "remarks"=>$request->remarks??("قيمة فاتوره رقم ".($request->orderId??$result->epiRefId)),
            ]);
            LogItem::store($invoice);

            $sharing=\App\Models\Sharing::create([
                "txn_token"  =>$invoice->txn_token,
                "type"       =>"transfer",
                "party_id"   =>$invoice->initiator_id
            ]);
            $result=[
                "txnToken"=>$sharing->txn_token,
            ];

            return redirect("/sharing/$sharing->txn_token");
        }
        return response()->json($result);
    }
}
