<?php

namespace App\Models;

use App\Data\AppConfigData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerNotification extends Model
{
    protected $table = 'customer_notifications';
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['customer_id','customer_type_id','notification_id','status'];
    // protected $appends = ['status'];

    protected $casts = [
        'result' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    // public function getStatusAttribute()
    // {
    //     return "R";
    // }
}
