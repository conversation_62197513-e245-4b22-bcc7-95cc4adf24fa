<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldBeEncrypted;

use Mail;


class SendMailNotification implements ShouldQueue, ShouldBeEncrypted
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @param string $mail
     * @param Mailable $data
     * @return void
     */
    public function __construct(
        public string $mail,
        public Mailable $data
    ){
        //
    }

    /* Get the tags that should be assigned to the job.
    *
    * @return array<int, string>
    */
    public function tags(): array
    {
        $tags=['mail'];
        // if(isset($this->data['user_id'])){
        //     $tags[]='userId:'.$this->data['user_id']??'';
        // }
        // if(isset($this->data['type'])){
        //     $tags[]='type:'.$this->data['type']??'';
        // }
        return $tags;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Mail::to($this->mail)->send($this->data);
    }
}
