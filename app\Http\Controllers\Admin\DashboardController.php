<?php

namespace App\Http\Controllers\Admin;
use App\Enums\CurrencyTypeEnum;
use App\Enums\GiftStatusEnum;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\LimitTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Cardless;
use App\Models\Dual;
use App\Models\Gift;
use App\Models\GiftTransaction;
use App\Models\Gold;
use App\Models\Harvest;
use App\Models\Invoice;
use App\Models\InvoiceTransaction;
use App\Models\OpenAccount;
use App\Models\Pass;
use App\Models\PassTransaction;
use App\Models\Registration;
use App\Models\UserInterface;
use App\Models\WheelTransaction;
use App\Models\YeahMoney;
use App\Scopes\CustomerScope;
use DB;
use Illuminate\Http\Request;
use Auth;

use Symfony\Component\Process\Process;

class DashboardController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function index(Request $request)
    {

        if (! Auth::user()->canAny(['dashboard.*','dashboard.view','dashboard.list'])){
            $items=UserInterface::getSideMenu();
            $items=collect($items)->where("route","<>","header");
            $interface=$items->first();
            if(is_null($interface)){
                return abort(401);
            }else if(count($interface->children)){
                $child=$interface->children->first();
                return redirect($child->route);
            }else{
                return redirect($interface->route);
            }
        }
        $operations=[
            [
                "id"=>"registration",
                "name"=>"Registration",
                "class"=>"info"
            ],
            [
                "id"=>"yeah_money",
                "name"=>"Yeah money",
                "class"=>"danger"
            ],
            [
                "id"=>"gold",
                "name"=>"Gold",
                "class"=>"warning"
            ],
            [
                "id"=>"wasil",
                "name"=>"Wasil",
                "class"=>"primary"
            ],
            // [
            //     "id"=>"harvest",
            //     "name"=>"Harvest",
            //     "class"=>"secondary"
            // ],
            [
                "id"=>"cardless",
                "name"=>"Cardless",
                "class"=>"success"
            ],
            [
                "id"=>"gift",
                "name"=>"Gift",
                "class"=>"info"
            ],
            [
                "id"=>"invoice",
                "name"=>"Invoice",
                "class"=>"danger"
            ],
            [
                "id"=>"open_account",
                "name"=>"Open account",
                "class"=>"primary"
            ],
            [
                "id"=>"wheel",
                "name"=>"Spinning wheel",
                "class"=>"primary"
            ],

        ];
        $periods=[
            [
                "id"=>"day",
                "name"=>"Day",
            ],
            [
                "id"=>"week",
                "name"=>"Week",
            ],
            [
                "id"=>"month",
                "name"=>"Month",
            ],
            [
                "id"=>"year",
                "name"=>"Year",
            ]
        ];
        $filter=$request->all();
        if(!$request->filled('period')){
            $filter['period']='week';
        }
        if(!$request->filled('type')){
            $filter['type']='registration';
        }
        if(!$request->filled('area')){
            $filter['area']='lowAreas';
        }
        if(!$request->filled('chartBalanceType')){
            $filter['chartBalanceType']='normal';
        }
        $charts=new \stdClass();
        $charts->counters=$this->getCountersChart($filter['type'],$filter['period']);
        $charts->performance=$this->getPerformanceChart($filter['type'],$filter['period']);
        $charts->efficient=$this->getEfficientChart($filter['type'],$filter['period']);
        $charts->revenue=$this->getRevenueChart($filter['type'],$filter['period']);
        $charts->balance=$this->getBalanceChart($filter['type'],$filter['period']);

        // dd($charts);
        // return;
        //collect($charts->performance->series)->pluck('*');
      // dd($charts->performance->names);
       //return;
        return view('default.admin.base.dashboard')
        ->with('operations', $operations)
        ->with('periods', $periods)
        ->with('charts', $charts)
        ->with('filter', $filter);
    }
    public function getOperations(Request $request)
    {

        $operations=[
            [
                "id"=>"registration",
                "name"=>"Registration",
                "class"=>"info"
            ],
            [
                "id"=>"yeahMoney",
                "name"=>"Yeah Money",
                "class"=>"danger"
            ],
            [
                "id"=>"gold",
                "name"=>"Gold",
                "class"=>"warning"
            ],
            [
                "id"=>"wasil",
                "name"=>"Wasil",
                "class"=>"primary"
            ],
            // [
            //     "id"=>"harvest",
            //     "name"=>"Harvest",
            //     "class"=>"primary"
            // ],
            [
                "id"=>"cardless",
                "name"=>"Cardless",
                "class"=>"primary"
            ],
            [
                "id"=>"gift",
                "name"=>"Gift",
                "class"=>"secondary"
            ],
            [
                "id"=>"invoice",
                "name"=>"Invoice",
                "class"=>"success"
            ],


            [
                "id"=>"openAccount",
                "name"=>"Open Account",
                "class"=>"light"
            ],

        ];
    }
    public function getCountersChart(string $type,string $period)
    {
        switch ($period) {
            case 'week':
                $start=\Carbon\Carbon::now()->subDays(6)->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();
                break;
            case 'month':
                $start=\Carbon\Carbon::now()->startOfMonth()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfMonth()->endOfDay()->toDateTimeString();
                break;
            case 'year':
                $start=\Carbon\Carbon::now()->startOfYear()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfYear()->endOfDay()->toDateTimeString();
                break;
            default:
                $start=\Carbon\Carbon::now()->startOfDay()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->endOfDay()->toDateTimeString();
                break;
        }
        $whareDate="created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="created_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }

        $chart=[];
        switch($type){
            case 'registration':
                $item=Registration::select(
                    "type as name",
                    DB::raw('count(*) as count'),
                    //DB::raw("'0' as total"),
                   // DB::raw("'' as currancy")
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("type")
                ->orderBy("type","desc")
                ->get();
                $chart=array_merge([[
                    "name"=>"All",
                    "count"=>$item->sum('count'),
                    //"total"=>0
                ]],$item->toArray());
                break;
            case 'yeah_money':
                $item=YeahMoney::withoutGlobalScope(CustomerScope::class)->select(
                        DB::raw("'Send' as name"),
                        DB::raw('count(*) as count'),
                        DB::raw("sum(coalesce(json_value(payin_data,'$.payin_Amount'),'0')) +sum(coalesce(json_value(trx_info,'$.fee_Amount'),'0')) as total"),
                        DB::raw("coalesce(json_value(payin_data,'$.payin_Currency_Code'),'') as currancy")
                    )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("coalesce(json_value(payin_data,'$.payin_Currency_Code'),'')"))
                ->get();

                $chart=array_merge($chart,$item->toArray());
                $item=Harvest::select(
                    DB::raw("'Claim' as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as total"),
                    DB::raw("coalesce(json_value(amount,'$.currency'),'') as currancy")
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("coalesce(json_value(amount,'$.currency'),'')"))
                ->get();
                $chart=array_merge($chart,$item->toArray());

                $charts=collect($chart);
                $chart=array_merge([[
                    "name"=>"All",
                    "count"=>$charts->sum('count'),
                    "total"=>$charts->sum('total'),
                    "currancy"=>'YER'
                ]],$chart);
                break;
            case 'gold':
                $chart=Gold::select(
                    DB::raw("'All' as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(coalesce(json_value(exchange_rate,'$.amount.limit.amount'),'0')) as total"),
                    DB::raw("max(coalesce(json_value(exchange_rate,'$.amount.limit.currancy'),'')) as currancy")
                )
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->whereNull('manual_type')
                ->get();
                break;
            case 'wasil':
                // $items=Pass::with(['transaction'=>function($query){
                //     return $query->select('pass_id','amount->amount as amount','amount->currency as currency','extra->mpt as tracking_code','extra->financial_reference as financial_reference');
                // }])->leftJoinSub(User::select('id as user_id','name'),'user',function($join){
                //     $join->on('user.user_id','=','resolved_by');
                // })->select('id','party_id','type','status','resolved','name as resolved_by','created_at','updated_at')->orderBy("created_at","desc");
                break;
            //case 'harvest':
                //$items=Harvest::select('id','party_id','service_code_id','request_id','amount->amount as amount','amount->currency as currency','status','created_at','updated_at')->orderBy("created_at","desc");
            //    break;
            case 'cardless':
                $item=Cardless::select(
                    DB::raw("'Self' as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(json_value(data,'$.amount.amount')) as total"),
                    DB::raw("json_value(data,'$.amount.currancy') as currancy")
                )
                ->whereNull("data->receiverPhone")
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("json_value(data,'$.amount.currancy')"))
                ->get();
                $chart=array_merge($chart,$item->toArray());

                $item=Cardless::select(
                    DB::raw("'Other' as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(json_value(data,'$.amount.amount')) as total"),
                    DB::raw("json_value(data,'$.amount.currancy') as currancy")
                )
                ->whereNotNull("data->receiverPhone")
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("json_value(data,'$.amount.currancy')"))
                ->get();
                $chart=array_merge($chart,$item->toArray());

                $charts= collect($chart);
                $chart=array_merge([[
                    "name"=>"All",
                    "count"=>$charts->sum('count'),
                    "total"=>$charts->sum('total'),
                    "currancy"=>'YER'
                ]],$chart);


                break;
            case 'gift':
                $chart=Gift::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("coalesce(json_value(amount,'$.currency'),'') as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as total"),
                    DB::raw("coalesce(json_value(amount,'$.currency'),'') as currancy")
                )
                ->where('status',GiftStatusEnum::SUCCESS->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("coalesce(json_value(amount,'$.currency'),'')"))
                ->get();
                break;
            case 'invoice':
                $chart=Invoice::select(
                    DB::raw("coalesce(json_value(amount,'$.currency'),'') as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as total"),
                    DB::raw("coalesce(json_value(amount,'$.currency'),'') as currancy")
                )
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("coalesce(json_value(amount,'$.currency'),'')"))
                ->get();
                break;
            case 'open_account':
                $chart=OpenAccount::select(
                    DB::raw("currency_id as name"),
                    DB::raw('count(*) as count'),
                    //DB::raw("'0' as total"),
                    //DB::raw("'' as currancy")
                )
                ->where('status',1)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy('currency_id')
                ->get();
                break;
            case 'wheel':
                $item=WheelTransaction::withoutGlobalScope(CustomerScope::class)->
                select(
                    DB::raw("max(coalesce(json_value(wheel_item,'$.name'),'')) as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(coalesce(json_value(wheel_item,'$.value'),'0')) as total"),
                    DB::raw("'YER' as currancy")
                )
                ->where('wheel_item->type',"1")
                //->where('status',TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("coalesce(json_value(wheel_item,'$.id'),'')"))
                ->get();

                $chart=array_merge($chart,$item->toArray());
                $item=WheelTransaction::withoutGlobalScope(CustomerScope::class)
                ->select(
                    DB::raw("max(coalesce(json_value(wheel_item,'$.name'),'')) as name"),
                    DB::raw('count(*) as count'),
                    DB::raw("sum(coalesce(json_value(wheel_item,'$.value'),'0')) as total"),
                    DB::raw("'YER' as currancy")
                )
                ->where('wheel_item->type',"0")
                //->where('status',TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->get();
                $chart=array_merge($chart,$item->toArray());

                $charts=collect($chart);
                $chart=array_merge([[
                    "name"=>"All",
                    "count"=>$charts->sum('count'),
                    "total"=>$charts->sum('total'),
                    "currancy"=>'YER'
                ]],$chart);
                break;

        }

        return json_decode(json_encode($chart));

    }

    public function getPerformanceChart(string $type,string $period)
    {
        $lang=session('locale')=="ar"?"arabic":"english";
        switch ($period) {
            case 'week':
                $start=\Carbon\Carbon::now()->subDays(6)->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $startp=\Carbon\Carbon::now()->subDays(13)->startOfDay()->toDateTimeString();
                $endp=\Carbon\Carbon::now()->subDays(7)->endOfDay()->toDateTimeString();

                $periodName='Week';
                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="DAYNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD')";
                    $indentifier="TO_CHAR(created_at,'DAY', 'NLS_DATE_LANGUAGE=$lang')";
                }
                break;
            case 'month':
                $start=\Carbon\Carbon::now()->subMonths(1)
                //->startOfWeek()
                ->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $startp=\Carbon\Carbon::now()->subMonths(2)
                //->startOfWeek()
                //->subMonths(1)
                //->startOfWeek()
                ->startOfDay()->toDateTimeString();
                $endp=\Carbon\Carbon::now()->subMonths(1)
                //->startOfWeek()
                //->subDays(1)
                ->startOfDay()->toDateTimeString();

                $periodName='Month';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="WEEKNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD')";
                    $indentifier="TO_CHAR(created_at - 7/24,'W')";
                }
                break;
            case 'year':
                $start=\Carbon\Carbon::now()->startOfYear()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $startp=\Carbon\Carbon::now()->subYears(1)->startOfYear()->startOfDay()->toDateTimeString();
                $endp=\Carbon\Carbon::now()->subYears(1)->endOfMonth()->endOfDay()->toDateTimeString();

                $periodName='Year';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="MONTHNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM')";
                    $indentifier="TO_CHAR(created_at,'Month', 'NLS_DATE_LANGUAGE=$lang')";
                }
                break;
            default:
                $start=\Carbon\Carbon::now()->startOfDay()->startOfHour()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfHour()->toDateTimeString();

                $startp=\Carbon\Carbon::now()->subDays(1)->startOfDay()->startOfHour()->toDateTimeString();
                $endp=\Carbon\Carbon::now()->subDays(1)->endOfHour()->toDateTimeString();

                $periodName='Day';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="DATE_FORMAT(created_at, '%H')";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD HH24')";
                    $indentifier="TO_CHAR(created_at,'HH AM', 'NLS_DATE_LANGUAGE=$lang')";
                }
                break;
        }
        $whareDate="created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="created_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }

        $chart=new \stdClass();
        $chart->colors=["#727cf5ad","#727cf5"];
        switch($type){
            case 'registration':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    Registration::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    ->where('status',2)
                    ->whereRaw($whareDate,[
                        $startp,
                        $endp
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                    $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Registration::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    ->where('status',2)
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();


                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                break;
            case 'yeah_money':
                $chart->colors=array_merge($chart->colors,["#0acf97ad", "#0acf97"]);

                $item= $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    YeahMoney::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName")." (".__("Send").") ",
                    "data"=> $item->pluck('val')
                ];
                //$chart->colors[]="#727cf5ad";


                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    YeahMoney::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("This $periodName")." (".__("Send").") ",
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');

                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    Harvest::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                //->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Previous $periodName")." (".__("Claim").") ",
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Harvest::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                //->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("This $periodName")." (".__("Claim").") ",
                    "data"=> $item->pluck('val')
                ];
                break;
            case 'gold':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    Gold::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->whereNull('manual_type')
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Gold::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->whereNull('manual_type')
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                break;
            case 'wasil':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    Pass::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Pass::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                // $items=Pass::with(['transaction'=>function($query){
                //     return $query->select('pass_id','amount->amount as amount','amount->currency as currency','extra->mpt as tracking_code','extra->financial_reference as financial_reference');
                // }])->leftJoinSub(User::select('id as user_id','name'),'user',function($join){
                //     $join->on('user.user_id','=','resolved_by');
                // })->select('id','party_id','type','status','resolved','name as resolved_by','created_at','updated_at')->orderBy("created_at","desc");
                break;
            // case 'harvest':
            //     $item=Dual::dualCharts(
            //        $indentifier,
            //         $startp,
            //         $endp
            //     )
            //     ->leftJoinSub(
            //         Harvest::select(
            //         DB::raw("MIN($createdAt) as created_date"),
            //         DB::raw("$indentifier as name"),
            //         DB::raw('count(*) as val'))
            //     //->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
            //     ->whereRaw($whareDate,[
            //         $startp,
            //         $endp
            //     ])
            //     ->groupBy(DB::raw("$indentifier")),'item',function($join) use($createdAt){
            //         $join->on('item.created_date','=',DB::raw("$createdAt"));
            //     })
            //     ->get();
            //     $chart->names=$item->pluck('name');

            //     $chart->series=[];
            //     $chart->series[]=[
            //         "name"=> __("Previous $periodName"),
            //         "data"=> $item->pluck('val')
            //     ];

            //     $item=Dual::dualCharts(
            //        $indentifier,
            //         $start,
            //         $end
            //     )
            //     ->leftJoinSub(
            //         Harvest::select(
            //         DB::raw("MIN($createdAt) as created_date"),
            //         DB::raw("$indentifier as name"),
            //         DB::raw('count(*) as val'))
            //     //->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
            //     ->whereRaw($whareDate,[
            //         $start,
            //         $end
            //     ])
            //     ->groupBy(DB::raw("$indentifier")),'item',function($join) use($createdAt){
            //         $join->on('item.created_date','=',DB::raw("$createdAt"));
            //     })
            //     ->get();

            //     $chart->series[]=[
            //         "name"=> __("This $periodName"),
            //         "data"=> $item->pluck('val')
            //     ];
            //     break;
            case 'cardless':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    Cardless::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Cardless::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                break;
            case 'gift':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    Gift::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',GiftStatusEnum::SUCCESS->value)
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Gift::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',GiftStatusEnum::SUCCESS->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                break;
            case 'invoice':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    Invoice::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Invoice::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',\App\Enums\TransactionStatusEnum::COMPLETED->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                break;
            case 'open_account':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    OpenAccount::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',1)
                ->whereRaw($whareDate,[
                    $startp,
                    $endp
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];


                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    OpenAccount::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw('count(*) as val'))
                ->where('status',1)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();
                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                break;
            case 'wheel':
                $item=Dual::dualCharts(
                    $indentifier,
                    $startp,
                    $endp
                )
                ->leftJoinSub(
                    WheelTransaction::withoutGlobalScope(CustomerScope::class)
                    ->select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    //->where('status',2)
                    ->whereRaw($whareDate,[
                        $startp,
                        $endp
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                    $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Previous $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    WheelTransaction::withoutGlobalScope(CustomerScope::class)
                    ->select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    //->where('status',2)
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();


                $chart->series[]=[
                    "name"=> __("This $periodName"),
                    "data"=> $item->pluck('val')
                ];
                $chart->names=$item->pluck('name');
                break;
        }

        return json_decode(json_encode($chart));

    }

    protected $names = [
        '-1'=>'ERROR',
        '0'=>'INIT',
        '1'=>'COMPLETED'
    ];
    protected $colors = [
        '-2'=>'#ff5b5b',
        '-1'=>'#ff5b5b',
        '0'=>'#6c757d',
        '1'=>'#536de6',
        '2'=>'#10c469',
        '3'=>'#f9c851',
        '4'=>'#35b8e0'
    ];
    public function getEfficientChart(string $type,string $period)
    {
        switch ($period) {
            case 'week':
                $start=\Carbon\Carbon::now()->subDays(6)->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();
                break;
            case 'month':
                $start=\Carbon\Carbon::now()->subMonths(1)->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();
                break;
            case 'year':
                $start=\Carbon\Carbon::now()->startOfYear()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfYear()->endOfDay()->toDateTimeString();
                break;
            default:
                $start=\Carbon\Carbon::now()->startOfDay()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->endOfDay()->toDateTimeString();
                break;
        }
        $whareDate="created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="created_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }

        $chart=new \stdClass();
        //=TransactionStatusEnum::ERROR->value
        switch($type){
            case 'registration':
                $item=Registration::select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__(TransactionStatusEnum::findByValue($element->name)->name),
                        //'name'  =>__($element->name),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>$this->colors[$element->name],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
            case 'yeah_money':
                $item=Harvest::select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $yeahMoney=YeahMoney::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("'1' as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->get();

               // $chart->colors=$item->pluck('color')->toArray();

                // if($yeahMoney->isNotEmpty() && !collect($chart->colors)->contains('#0acf97')){
                //     $chart->colors[]='#0acf97';
                // }

                $item=collect(array_merge($yeahMoney->toArray(),$item->toArray()));

                //$chart->colors=collect($item->pluck('color'))->unique()->toArray();


                $sum=collect(array_merge($yeahMoney->toArray(),$item->toArray()))->sum('count');
                $groupBy=collect(array_merge($yeahMoney->toArray(),$item->toArray()))->groupBy('name');

                $map=collect($groupBy)->map(function($element,$key) use($sum){
                    return [
                        //'name'  =>__(TransactionStatusEnum::findByValue($key)->name),
                        'name'  =>__($this->names[$key]),
                        'count'  =>(collect($element)->sum('count')/$sum)*100,
                        'color'  =>$this->colors[$key],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');

                break;
            case 'gold':
                $item=Gold::select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->whereNull('manual_type')
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__(TransactionStatusEnum::findByValue($element->name)->name),
                        //'name'  =>__($element->name),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>$this->colors[$element->name],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
            case 'wasil':
                $item=Pass::select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__(TransactionStatusEnum::findByValue($element->name)->name),
                        //'name'  =>__($element->name),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>$this->colors[$element->name],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
            //case 'harvest':
                //$items=Harvest::select('id','party_id','service_code_id','request_id','amount->amount as amount','amount->currency as currency','status','created_at','updated_at')->orderBy("created_at","desc");
                //break;
            case 'cardless':
                $item=Cardless::select(
                    DB::raw("'1' as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->get();
                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__($this->names[$element->name]),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>$this->colors[$element->name],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
            case 'gift':
                $item=GiftTransaction::select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__(GiftStatusEnum::findByValue($element->name)->name),
                        //'name'  =>__($element->name),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>GiftStatusEnum::color($element->name),
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
            case 'invoice':
                $item=InvoiceTransaction::select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__(TransactionStatusEnum::findByValue($element->name)->name),
                        //'name'  =>__($element->name),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>$this->colors[$element->name],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
            case 'open_account':
                $item=OpenAccount::select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__($this->names[$element->name]),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>$this->colors[$element->name],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
            case 'wheel':
                $item=WheelTransaction::withoutGlobalScope(CustomerScope::class)
                ->select(
                    DB::raw("status as name"),
                    DB::raw('count(*) as count')
                )
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy("status")
                ->orderBy("status","desc")
                ->get();

                $sum=$item->sum('count');
                $map=collect($item)->map(function($element,$key) use($sum){
                    return [
                        'name'  =>__(TransactionStatusEnum::findByValue($element->name)->name),
                        //'name'  =>__($element->name),
                        'count'  =>($element->count/$sum)*100,
                        'color'  =>$this->colors[$element->name],
                    ];
                });
                $chart->colors=$map->pluck('color');
                $chart->names=$map->pluck('name');
                $chart->series=$map->pluck('count');
                break;
        }

        return json_decode(json_encode($chart));

    }

    public function getRevenueChart(string $type,string $period)
    {
        $lang=session('locale')=="ar"?"arabic":"english";
        switch ($period) {
            case 'week':
                $start=\Carbon\Carbon::now()->subDays(6)->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $periodName='Week';
                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="DAYNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD')";
                    $indentifier="TO_CHAR(created_at,'DAY', 'NLS_DATE_LANGUAGE=$lang')";
                }
                break;
            case 'month':
                $start=\Carbon\Carbon::now()->subMonths(1)->startOfWeek()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $periodName='Month';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="WEEKNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD')";
                    $indentifier="TO_CHAR(created_at - 7/24,'W')";
                }
                break;
            case 'year':
                $start=\Carbon\Carbon::now()->startOfYear()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $periodName='Year';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="MONTHNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM')";
                    $indentifier="TO_CHAR(created_at,'Month', 'NLS_DATE_LANGUAGE=$lang')";
                }
                break;
            default:
                $start=\Carbon\Carbon::now()->startOfDay()->startOfHour()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfHour()->toDateTimeString();

                $periodName='Day';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="DATE_FORMAT(created_at, '%H')";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD HH24')";
                    $indentifier="TO_CHAR(created_at,'HH AM', 'NLS_DATE_LANGUAGE=$lang')";
                }
                break;
        }
        $whareDate="created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="created_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }

        $chart=new \stdClass();
        $chart->colors=["#0acf97","#727cf5"];
        switch($type){
            case 'registration':
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Registration::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    ->where('status',2)
                    ->where('type','registration')
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();


                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Registration"),
                    "data"=> $item->pluck('val')
                ];
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Registration::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    ->where('status',2)
                    ->where('type','activate')
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();


                $chart->series[]=[
                    "name"=> __("Activate"),
                    "data"=> $item->pluck('val')
                ];

                break;

            case 'yeah_money':
                //$chart->colors=array_merge($chart->colors,["#0acf97ad", "#0acf97"]);

                $item= $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    YeahMoney::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(payin_data,'$.payin_Amount'),'0')) as val"))
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();
                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Yeah Money")." (".__("Send").") ",
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Harvest::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as val"))
                ->where('status',1)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();
                $chart->names=$item->pluck('name');

                $chart->series[]=[
                    "name"=> __("Yeah Money")." (".__("Claim").") ",
                    "data"=> $item->pluck('val')
                ];

                $chart->colors[]="#f9c851";
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    YeahMoney::withoutGlobalScope(CustomerScope::class)->select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(trx_info,'$.fee_Amount'),'0')) as val"))
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Yeah Money")." (".__("Fee").") ",
                    "data"=> $item->pluck('val')
                ];

                break;
            case 'gold':
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Gold::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw("sum(coalesce(json_value(exchange_rate,'$.amount.limit.amount'),'0')) as val"))
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->whereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_SELL->value])
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->whereNull('manual_type')
                    ->whereRaw("(json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%21G%' or json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%24G%')")
                    ->groupBy(DB::raw("$indentifier"))
                    ,'item',function($join){
                        $join->on('item.name','=','indentifier');
                })

                ->get();
                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Gold Sell"),
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Gold::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(exchange_rate,'$.amount.limit.amount'),'0')) as val"))
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->whereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value])
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->whereRaw("(json_value(\"CREDIT_ACCOUNT_ID\", '$.\"value\"') like '%21G%' or json_value(\"CREDIT_ACCOUNT_ID\", '$.\"value\"') like '%24G%')")
                ->whereNull('manual_type')
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Gold Buy"),
                    "data"=> $item->pluck('val')
                ];

                $chart->colors[]="#f9c851";
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Gold::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(fee,'$.amount'),'0')) as val"))
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->whereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value])
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->whereNull('manual_type')
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Gold Fee"),
                    "data"=> $item->pluck('val')
                ];
                break;
            case 'wasil':
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    PassTransaction::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw("sum(json_value(amount,'$.amount')) as val"))
                    // ->leftJoinSub(
                    //     PassTransaction::select(
                    //         DB::raw("MIN($createdAt) as created_date"),
                    //         DB::raw("$indentifier as name"),
                    //         DB::raw("sum(json_value(amount,'$.amount')) as total"))
                    //     ->where('status',TransactionStatusEnum::COMPLETED->value)
                    //     ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                    //     ->whereRaw($whareDate,[
                    //         $start,
                    //         $end
                    //     ])
                    // ->groupBy(DB::raw("$indentifier")),'transaction',function($join) use($createdAt){
                    //     $join->on('transaction.created_date','=',DB::raw("$createdAt"));
                    // })
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();
                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Wasil")." (".__("Send").") ",
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    PassTransaction::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw("sum(json_value(amount,'$.amount')) as val"))
                    // ->leftJoinSub(
                    //     PassTransaction::select(
                    //         DB::raw("MIN($createdAt) as created_date"),
                    //         DB::raw("$indentifier as name"),
                    //         DB::raw("sum(json_value(amount,'$.amount')) as total"))
                    //     ->where('status',TransactionStatusEnum::COMPLETED->value)
                    //     ->where('type',InvoiceTransactionsTypeEnum::Claim->value)
                    //     ->whereRaw($whareDate,[
                    //         $start,
                    //         $end
                    //     ])
                    // ->groupBy(DB::raw("$indentifier")),'transaction',function($join) use($createdAt){
                    //     $join->on('transaction.created_date','=',DB::raw("$createdAt"));
                    // })
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->where('type',InvoiceTransactionsTypeEnum::Claim->value)
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Wasil")." (".__("Claim").") ",
                    "data"=> $item->pluck('val')
                ];

                $chart->colors[]="#f9c851";
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    PassTransaction::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw("sum(json_value(fee,'$.amount')) as val"))
                    // ->leftJoinSub(
                    //     PassTransaction::select(
                    //         DB::raw("MIN($createdAt) as created_date"),
                    //         DB::raw("$indentifier as name"),
                    //         DB::raw("sum(json_value(fee,'$.amount')) as total"))
                    //     ->where('status',TransactionStatusEnum::COMPLETED->value)
                    //     ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                    //     ->whereRaw($whareDate,[
                    //         $start,
                    //         $end
                    //     ])
                    // ->groupBy(DB::raw("$indentifier")),'transaction',function($join) use($createdAt){
                    //     $join->on('transaction.created_date','=',DB::raw("$createdAt"));
                    // })
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Wasil")." (".__("Fee").") ",
                    "data"=> $item->pluck('val')
                ];
            break;
            // case 'harvest':
            //     break;
            case 'cardless':
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Cardless::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(json_value(data,'$.amount.amount')) as val"))
                ->whereNull("data->receiverPhone")
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();
                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Self"),
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    Cardless::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(json_value(data,'$.amount.amount')) as val"))
                ->whereNotNull("data->receiverPhone")
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Other"),
                    "data"=> $item->pluck('val')
                ];

                break;
            case 'gift':
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    GiftTransaction::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as val"))
                ->where('status',GiftStatusEnum::SUCCESS->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();
                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Gift")." (".__("Not Claimed").") ",
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    GiftTransaction::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as val"))
                ->where('status',GiftStatusEnum::CLAIM->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Gift")." (".__("Claimed").") ",
                    "data"=> $item->pluck('val')
                ];
                break;
            case 'invoice':
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    InvoiceTransaction::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as val"))
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Invoice")." (".__("Payment").") ",
                    "data"=> $item->pluck('val')
                ];

                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    InvoiceTransaction::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as val"))
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->where('type',InvoiceTransactionsTypeEnum::Reverse->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Invoice")." (".__("Reverse").") ",
                    "data"=> $item->pluck('val')
                ];

                $chart->colors[]="#f9c851";
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    InvoiceTransaction::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(coalesce(json_value(amount,'$.amount'),'0')) as val"))
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->where('type',InvoiceTransactionsTypeEnum::Refund->value)
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series[]=[
                    "name"=> __("Invoice")." (".__("Refund").") ",
                    "data"=> $item->pluck('val')
                ];
                break;
            case 'open_account':
                $chart->colors=[];//[]="#f9c851";
                $chart->series=[];

                $currencies=[CurrencyTypeEnum::YER,CurrencyTypeEnum::USD,CurrencyTypeEnum::SAR,CurrencyTypeEnum::G21,CurrencyTypeEnum::G24];
                foreach ($currencies as $key => $currency) {
                    $chart->colors[]=$this->colors[$key];
                    $item=Dual::dualCharts(
                    $indentifier,
                        $start,
                        $end
                    )
                    ->leftJoinSub(
                        OpenAccount::select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    ->where('status',1)
                    ->where('currency_id',$currency->value)

                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join) use($createdAt){
                        $join->on('item.created_date','=',DB::raw("$createdAt"));
                    })
                    ->get();
                    $chart->names=$item->pluck('name');

                    $chart->series[]=[
                        "name"=> $currency->value,
                        "data"=> $item->pluck('val')
                    ];
                }
                break;
            case 'wheel':
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    WheelTransaction::withoutGlobalScope(CustomerScope::class)
                    ->select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    ->where('wheel_item->type','1')
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();


                $chart->names=$item->pluck('name');

                $chart->series=[];
                $chart->series[]=[
                    "name"=> __("Win"),
                    "data"=> $item->pluck('val')
                ];
                $item=Dual::dualCharts(
                    $indentifier,
                    $start,
                    $end
                )
                ->leftJoinSub(
                    WheelTransaction::withoutGlobalScope(CustomerScope::class)
                    ->select(
                        DB::raw("MIN($createdAt) as created_date"),
                        DB::raw("$indentifier as name"),
                        DB::raw('count(*) as val'))
                    ->where('wheel_item->type','0')
                    ->whereRaw($whareDate,[
                        $start,
                        $end
                    ])
                    ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();


                $chart->series[]=[
                    "name"=> __("Lose"),
                    "data"=> $item->pluck('val')
                ];

                break;
        }

        return json_decode(json_encode($chart));

    }
    public function getBalanceChart(string $type,string $period)
    {
        $lang=session('locale')=="ar"?"arabic":"english";
        switch ($period) {
            case 'week':
                $start=\Carbon\Carbon::now()->subDays(6)->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $periodName='Week';
                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="DAYNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD HH24')";
                    $indentifier="TO_CHAR(created_at,'YYYY-MM-DD HH24')";
                }
                break;
            case 'month':
                $start=\Carbon\Carbon::now()->subMonths(1)->startOfWeek()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $periodName='Month';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="WEEKNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD')";
                    $indentifier="TO_CHAR(created_at,'YYYY-MM-DD')";
                }
                break;
            case 'year':
                $start=\Carbon\Carbon::now()->startOfYear()->startOfDay()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfDay()->toDateTimeString();

                $periodName='Year';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="MONTHNAME(created_at)";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD')";
                    $indentifier="TO_CHAR(created_at,'YYYY-MM-DD')";
                }
                break;
            default:
                $start=\Carbon\Carbon::now()->startOfDay()->startOfHour()->toDateTimeString();
                $end=\Carbon\Carbon::now()->endOfHour()->toDateTimeString();

                $periodName='Day';

                if(env('DB_CONNECTION')!='oracle'){
                    $createdAt="MIN(created_at)";
                    $indentifier="DATE_FORMAT(created_at, '%H')";
                }else{
                    $createdAt="TO_CHAR(created_at,'YYYY-MM-DD HH24')";
                    $indentifier="TO_CHAR(created_at,'YYYY-MM-DD HH24')";
                }
                break;
        }
        $whareDate="created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="created_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }

        $chart=new \stdClass();
        $chart->colors=["#0acf97","#727cf5"];
        switch($type){
            case 'registration':
                break;
            case 'yeah_money':
                break;
            case 'gold':
                if(request()->input('chartBalanceType')=="cumulative"){
                    $item=Dual::dualBalanceCharts(
                        $indentifier,
                        $start,
                        $end
                    );
                }else{
                    $item=Dual::dualCharts(
                        $indentifier,
                        $start,
                        $end
                    )
                    ->addSelect(
                        DB::raw("to_number(created_at - to_date('01-JAN-1970','DD-MON-YYYY')) * (24 * 60 * 60 * 1000) as name"))
                    ;
                }

                $item=$item->leftJoinSub(
                    Gold::select(
                    DB::raw("MIN($createdAt) as created_date"),
                    DB::raw("$indentifier as name"),
                    DB::raw("sum(case WHEN json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%21G%'
                                THEN (-1*cast(coalesce(json_value(amount,'$.amount'),'0') as number))
                                WHEN json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%24G%'
                                THEN (-1*cast(coalesce(json_value(amount,'$.amount'),'0') as number))
                                ELSE cast(coalesce(json_value(amount,'$.amount'),'0') as number) END) as val"))
                ->where('status',TransactionStatusEnum::COMPLETED->value)
                ->whereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value])
                ->whereRaw($whareDate,[
                    $start,
                    $end
                ])
                ->whereNull('manual_type')
                ->groupBy(DB::raw("$indentifier")),'item',function($join){
                        $join->on('item.name','=','indentifier');
                })
                ->get();

                $chart->series= [[
                    "name"=> __("Gold Balance"),
                    "data"=>$item->map(function($e){
                        return [
                            $e->name,
                            $e->val
                        ];
                    })
                ]];

                break;
            case 'wasil':
            break;
            // case 'harvest':
            //     break;
            case 'cardless':
                break;
            case 'gift':
                break;
            case 'invoice':
                break;
            case 'open_account':
                break;
        }

        return json_decode(json_encode($chart));

    }
}
