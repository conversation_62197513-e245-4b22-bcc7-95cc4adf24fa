<?php

namespace App\Jobs;

use App\Data\Pass\PassCreateRequestData;
use App\Data\PaymentResultData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\Gift;
use App\Models\Pass;
use App\Models\PassTransaction;
use App\Scopes\CustomerScope;
use App\Services\FlexService;
use App\Services\NotificationService;
use App\Services\PassService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessClaimedTimeoutWasil implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

       /**
     * Create a new job instance.
     * @param mixed $user
     * @param PassCreateRequestData $requestData
     * @param Pass $pass
     * @param Pass $passTransaction
     * @return void
     */
    public function __construct(
        public $user,
        public PassCreateRequestData $requestData,
        public Pass $pass,
        public PassTransaction $passTransaction
    ){
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if($this->pass->status!=TransactionStatusEnum::ERROR->value || $this->pass->type!=InvoiceTransactionsTypeEnum::Claim->value ||
          $this->passTransaction->status!=TransactionStatusEnum::ERROR->value || $this->passTransaction->type!=InvoiceTransactionsTypeEnum::Claim->value||
          $this->pass->resolved==1
        ){
            return;
        }

        $response=PassService::findByReference($this->requestData);
        $result=$response->getAdditionalData()['extra']??null;
        if($response->status->message->code=="0" && isset($result->collected_by)){
            LogItem::store($this->pass);
            $this->pass->refresh();
            if($this->pass->status!=TransactionStatusEnum::ERROR->value ||  $this->pass->resolved==1){
                return;
            }

            if(!is_null($this->user)){
                $this->pass->resolved_by=$this->user->id;
            }
            $this->pass->status=TransactionStatusEnum::COMPLETED->value;
            $this->pass->resolved=1;
            $this->pass->save();


            $this->passTransaction->extra= collect($this->passTransaction->extra)
            ->merge($result);//$result->getAdditionalData()["extra"];

            $this->passTransaction->external_reference_id= $this->passTransaction->extra->financial_reference??$this->passTransaction->extra->transaction_reference;
            $this->passTransaction->status=$this->pass->status;
            $this->passTransaction->save();

            /** Credit to customer account **/
            $object=new \stdClass();
            $object->reference_id   = $this->passTransaction->reference_id;
            $object->service_name   = ThirdPartyServiceNameData::wasil();
            $object->account_id     = $this->passTransaction->account_id->value;
            $object->amount         = $this->passTransaction->amount->amount;
            $object->currency       = $this->passTransaction->amount->currency;
            $object->remarks        = $this->passTransaction->remarks;

            $result=FlexService::accountToDebit($object);
            if($result->status->message->code=="0"){
                $this->passTransaction->payment_result= [
                    'amount'=>new PaymentResultData(
                        $result->getAdditionalData()["referenceId"],
                        $result->getAdditionalData()["externalReferenceId"],
                        InvoiceTransactionsTypeEnum::Claim->value
                    ),
                ];
                $this->passTransaction->save();
            }
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Collect money"),
                    'body'=>sprintf(__("Successfully collect remittance #[%s] sended by [%s] service"),
                        ($this->passTransaction->extra->mpt??""),
                        __("Wasil")
                    ),
                    'type'=>'operation',
                ]
            ],$this->pass->party_id);
            return;
        }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
            $this->pass->refresh();
            if($this->pass->status!=TransactionStatusEnum::ERROR->value || $this->pass->resolved==1){
                return;
            }

            if(!is_null($this->user)){
                $this->pass->resolved_by=$this->user->id;
            }
            $this->pass->resolved=1;
            $this->pass->save();
        }

    }
}
