<?php

namespace App\Http\Controllers;

use App\Data\ReceiptData;
use App\Enums\ServiceTagEnum;
use App\Jobs\ProcessLoyaltyAddTransaction;
use App\Models\Service;
use App\Traits\DatabaseCompatibility;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use PDF;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests,DatabaseCompatibility;

    // protected $tag;
    // protected ?Service $service;
    // public function __construct(){
    //     if(!is_null($this->tag) && $this->tag instanceof ServiceTagEnum){
    //         $this->service = Service::getServiceWithTag($this->tag->value);

    //     }
    // }
    public function __construct()
    {
        $this->bootTraits();
    }

    protected function bootTraits(): void
    {
        // Loop through used traits and call their boot methods if they exist
        foreach (class_uses_recursive($this) as $trait) {
            $method = 'boot' . class_basename($trait);
            if (method_exists($this, $method)) {
                $this->{$method}();
            }
        }
    }
    // public function loyalty($amount,$note="")
    // {
    //     if(auth()?->user()?->id=="0183415"){
    //         if(!is_null($this->tag) && $this->tag instanceof ServiceTagEnum){
    //             $this->service = Service::getServiceWithTag($this->tag->value);
    //             if(!is_null($this->service) && !empty($this->service->loyalty_card_id??"")){
    //                 $user=auth()->user();
    //                 $object=new \stdClass();
    //                 $object->id=$user->id;
    //                 $object->name=$user->name;
    //                 $object->email= $user->email;
    //                 $object->phone= $user->phone;

    //                 ProcessLoyaltyAddTransaction::dispatch( $object,$amount,$note??"",$this->service->loyalty_card_id)
    //                 ->onQueue('critical');
    //             }
    //         }
    //     }


    // }
     /**
     * Hide the given parameters.
     *
     * @param  array  $data
     * @param  array  $hidden
     * @return mixed
     */
    public function hideParameters($data, $hidden)
    {
        foreach ($hidden as $parameter) {
            if (data_get($data, $parameter)) {
                data_set($data, $parameter, '********');
            }
        }

        return $data;
    }
    /**
     * Generate a random, secure password.
     *
     * @param  int  $length
     * @param  bool  $letters
     * @param  bool  $numbers
     * @param  bool  $symbols
     * @param  bool  $spaces
     * @return string
     */
    public static function password($length = 32, $letters = true, $numbers = true, $symbols = true, $spaces = false)
    {
        $password = new Collection();

        $options = (new Collection([
            'letters' => $letters === true ? [
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
                'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
                'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G',
                'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
                'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            ] : null,
            'numbers' => $numbers === true ? [
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            ] : null,
            'symbols' => $symbols === true ? [
                '~', '!', '#', '$', '%', '^', '&', '*', '(', ')', '-',
                '_', '.', ',', '<', '>', '?', '/', '\\', '{', '}', '[',
                ']', '|', ':', ';',
            ] : null,
            'spaces' => $spaces === true ? [' '] : null,
        ]))->filter()->each(fn ($c) => $password->push($c[random_int(0, count($c) - 1)])
        )->flatten();

        $length = $length - $password->count();

        return $password->merge($options->pipe(
            fn ($c) => Collection::times($length, fn () => $c[random_int(0, $c->count() - 1)])
        ))->shuffle()->implode('');
    }
    public static function maskString(?string $string = NULL,int $start = 0,int $end = 0)
    {
        if (!$string) {
            return NULL;
        }
        $length = strlen($string);
        //$visibleCount = 4;
        $hiddenCount = $length - ($start+$end);
        return mb_convert_encoding(substr($string, 0, $start) . str_repeat('X', $hiddenCount) . substr($string, ($end * -1), $end),"UTF-8","UTF-8") ;
    }
    public static function generateNumericOTP($n) {

        // Take a generator string which consist of
        // all numeric digits
        $generator = "135792468";

        // Iterate for n-times and pick a single character
        // from generator and append it to $result

        // Login for generating a random character from generator
        //     ---generate a random number
        //     ---take modulus of same with length of generator (say i)
        //     ---append the character at place (i) from generator to result

        $result = "";

        for ($i = 1; $i <= $n; $i++) {
            $result .= substr($generator, (rand()%(strlen($generator))), 1);
        }

        // Return result
        return $result;
    }

    protected function _flatImageInput(string $key)
    {
        $result=[];
        if(str_contains($key,'*')){
            $start=substr($key,0,strpos($key,".*."));

            $end=substr($key,strpos($key,".*.")+3,strlen($key));
            if(request()->filled($start)){
                foreach (request()->input($start) as $k => $value) {
                    $baseKey="{$start}.$k.{$end}";
                    $result=array_merge($result,$this->_flatImageInput($baseKey));
                }
            }

        }else{
            $result=[$key];
        }
        return $result;
    }
    public function setImage(string $key,?string $module)
    {
        if(is_null($module)){
            $module="generics";
        }

        $data=request()->all();
        $requiredUpdate=false;
        $imageInputs=$this->_flatImageInput($key);
        foreach ($imageInputs as $baseKey) {
            if($this->_setImage($module,$baseKey,$data)){
                $requiredUpdate=true;
            };
        }

        // if(str_contains($key,'*')){
        //     $keys = collect(explode('.', $key));
        //     if($keys->first()!="*" && $keys->last()!="*")
        //     foreach (request()->input($keys->first()) as $k => $value) {
        //         $baseKey="{$keys->first()}.$k.{$keys->last()}";
        //         if($this->_setImage($module,$baseKey,$data)){
        //             $requiredUpdate=true;
        //         };
        //     }
        // }else{
        //     if($this->_setImage($module,$key,$data)){
        //         $requiredUpdate=true;
        //     };
        // }
        if($requiredUpdate){
            request()->request->replace($data); //add request
        };

    }

    protected function _setImage(string $module,string $baseKey,&$data)
    {
        $_key="{$baseKey}_file_image";
        if (request()->hasFile($_key)) {
            $image=request()->file($_key);
            $fileName= "" .uniqid().'.'.$image->extension();
            $image->storeAs($module, $fileName,'public');
           // $image->move(public_path("/storage/$module/"), $fileName);
            if(request()->filled("{$baseKey}_org_image")){
                $old_image = explode ('/',request()->input("{$baseKey}_org_image"));
                $old_image= $old_image[count($old_image)-1];
                if($old_image!="placeholder.jpg" && File::exists(public_path("/storage/$module/$old_image"))) {
                    File::delete(public_path("/storage/$module/$old_image"));
                }
            }
            data_set($data,"$baseKey",Storage::url("$module/$fileName"));
            Log::info("file with {key} logged with {image}",['key' => $baseKey,'image' => Storage::url("$module/$fileName")]);

            return true;
        }else if(request()->filled("{$baseKey}_delete_image")){
            $old_image = explode ('/',request()->input("{$baseKey}_delete_image"));
            $old_image= $old_image[count($old_image)-1];
            if($old_image!="placeholder.jpg" && File::exists(public_path("/storage/$module/$old_image"))) {
                File::delete(public_path("/storage/$module/$old_image"));
            }
            if(!request()->filled("{$baseKey}_image_type")){
                data_set($data,"$baseKey",null);
                return true;
            }
        } else if(!request()->filled("{$baseKey}_image_type") && request()->filled("{$baseKey}_org_image")){
            data_set($data,"$baseKey",request()->input("{$baseKey}_org_image"));
            return true;
        } else if(request()->filled("{$baseKey}_image_type") && request()->filled("{$baseKey}_org_image")){
            $old_image = explode ('/',request()->input("{$baseKey}_org_image"));
            $old_image= $old_image[count($old_image)-1];
            if($old_image!="placeholder.jpg" && File::exists(public_path("/storage/$module/$old_image"))) {
                File::delete(public_path("/storage/$module/$old_image"));
            }
        }
        return false;
    }

    /**
     * Generate a receipt PDF.
     *
     * This method generates a receipt PDF using the provided receipt data.
     * It sets up the PDF document with a title, margins, header, footer, and content.
     * The content includes the receipt title, beneficiary, date, receipt number, statement, amount, fee, and total.
     *
     * @param ReceiptData $receipt The receipt data object containing all necessary information for generating the receipt.
     *
     * @return void
     */
    public function generateReceipt(ReceiptData $receipt)
    {

        //dd(file_get_contents('./storage/header.png'));
        // Set the title of the PDF document
        PDF::SetTitle(($receipt->title??__("Receipt"))." " . $receipt->id);

        // Set the margins for the PDF document
        PDF::SetMargins(10, 38, 10);

        // Enable automatic page breaks
        PDF::SetAutoPageBreak(TRUE, 26);

        $isRtl = false;
        if (app()->getLocale()=='ar'){
            $isRtl = true;
        }
        // Set the text direction to right-to-left
        PDF::setRTL($isRtl);

        // Set the font for the PDF document
        PDF::SetFont('bein', 'B', 12);

        // Set the header callback function
        PDF::setHeaderCallback(function ($pdf) use($receipt,$isRtl) {
            // Set the text direction to left-to-right for the header
            $pdf->setRTL(false);

            // Set the position for the header image
            $pdf->SetXY(0, 0);

            // Add the header image to the PDF
            $pdf->Image($receipt->header, '', '', 210, 51, 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);

            $pdf->setRTL($isRtl);
            // Set the font for the PDF document
            PDF::SetFont('bein', 'B', 50);
            // Set the starting Y position for the content
            $y_start =  7;
            $pdf->SetY($y_start);
            $pdf->SetX(100);
            // Set the fill color to white and add the receipt title
            $pdf->SetFillColor(255, 255, 255);
            $pdf->SetTextColor(0, 120, 174);
            $pdf->MultiCell(100, 0, __("Receipt"), 0, 'C', 1);

            // Set the font for the PDF document
            PDF::SetFont('bein', 'B', 16);

            $y_start = PDF::GetY() ;
            $pdf->SetY($y_start);
            $pdf->SetX(100);
            $pdf->SetTextColor(0);

           // $pdf->SetTextColor(13, 150, 211);
            $pdf->MultiCell(100, 0, $receipt->title, 0, 'C', 1);
            // Set the text direction back to right-to-left
            $pdf->setRTL($isRtl);
        });

        // Set the footer callback function
        PDF::setFooterCallback(function ($pdf) use($receipt,$isRtl) {
            // Set the text direction to left-to-right for the footer
            $pdf->setRTL(false);

            // Set the position for the footer image
            $pdf->SetXY(0, -56);

            // Add the footer image to the PDF
            $pdf->Image($receipt->footer, '', '', 210, 56, 'JPG', '', 'T', false, 300, '', false, false, 0, false, false, false);

            // Set the text direction back to right-to-left
            $pdf->setRTL($isRtl);
        });

        // Add a new page to the PDF document
        PDF::AddPage();

        // Set the font for the PDF document
        PDF::SetFont('bein', 'B', 16);

        // Set the text color to black
        PDF::SetTextColor(0);

        // Set the cell paddings
        PDF::setCellPaddings(2, 2, 2, 2);

        // // Set the starting Y position for the content
        // $y_start = PDF::GetY() + 15;
        // PDF::SetY($y_start);
        // PDF::SetX(0);
        // // Set the fill color to white and add the receipt title
        // PDF::SetFillColor(255, 255, 255);
        // PDF::MultiCell(210, 0, $receipt->title, 0, 'C', 1);

        // Set the font for the beneficiary and date section
        PDF::SetFont('bein', 'B', 13);

        // Set the starting Y position for the beneficiary and date section
        $y_start = PDF::GetY() + 20;
        PDF::SetY($y_start);

        if(!is_null( auth()?->user()) && (request()->showAccountInfo??0)){
            PDF::Ln(10);
            $y_start = PDF::GetY();
            // Set the draw color and fill color for the beneficiary label
            PDF::SetDrawColor(91, 91, 91);
            PDF::SetFillColor(245, 245, 245);
            PDF::MultiCell(70, 0, __("Customer"), 0, 'S', 1);

            if(!is_null( $receipt->details->debitAccountId->value) && (request()->showAccountNumber??0)){
                // Set the fill color for the date label and add the date label
                PDF::SetY($y_start);
                PDF::SetX(135);
                PDF::SetFillColor(245, 245, 245);
                PDF::MultiCell(60, 0, __("Account #"), 0, 'C', 1);
            }
            $customerName=auth()?->user()?->name??"";
            // Set the starting Y position for the beneficiary and date section
            $y_start = PDF::GetY() + 2;
            PDF::SetY($y_start);
            PDF::SetX(10);
            PDF::MultiCell(100, 0, $customerName, 0, 'S', 0);

            if(!is_null( $receipt->details->debitAccountId->value) && (request()->showAccountNumber??0)){
                PDF::SetY($y_start);
                PDF::SetX(135);
                PDF::SetFillColor(222, 234, 246);
                PDF::MultiCell(60, 0, Controller::maskString($receipt->details->debitAccountId->value,3,10), 0, 'C', 0);
            }

            if(!is_null( auth()?->user()) && (request()->showMyAddress??0)){
                $customerAddress=html_entity_decode(join(" - ",collect(auth()?->user()->userProfile->address)->only(['line1','line2','line3','line4'])->values()->all()));

                $y_start = PDF::GetY() + 2;
                PDF::SetY($y_start);
                PDF::SetDrawColor(91, 91, 91);
                PDF::SetFillColor(245, 245, 245);
                PDF::MultiCell(70, 0, __("Address"), 0, 'S', 1);

                $y_start = PDF::GetY() + 2;
                PDF::SetY($y_start);
                PDF::SetX(10);
                PDF::writeHTML($customerAddress, true, false, false, false, '');
                //PDF::MultiCell(100, 0, str_replace(",", "<br>",html_entity_decode(join(",",collect(auth()?->user()->userProfile->address)->only(['line1','line2','line3','line4','city','state'])->values()->all()))), 0, 'S', 0);

            }
        }

        $headLebel="";
        $headBody="";
        if(!is_null($receipt->beneficiary)){
            $headLebel=__("Beneficiary");
            $headBody=$receipt->beneficiary;
        }else if(!is_null($receipt->sender)){
            $headLebel=__("Sender");
            $headBody=$receipt->sender;
        }else if(!is_null( $receipt->details->remarks)){
            $headLebel=__("Note");
            $headBody=$receipt->details->remarks;
        }
        $headBody=str_replace('*', '.', $headBody);

        PDF::Ln(10);
        $y_start = PDF::GetY();
        // Set the draw color and fill color for the beneficiary label
        if(!empty($headLebel)){
            PDF::SetDrawColor(91, 91, 91);
            PDF::SetFillColor(245, 245, 245);
        }else{
            PDF::SetFillColor(255, 255, 255);
        }
        PDF::MultiCell(70, 0,  $headLebel, 0, 'S', 1);

        // Set the fill color for the date label and add the date label
        PDF::SetY($y_start);
        PDF::SetX(135);
        PDF::SetFillColor(245, 245, 245);
        PDF::MultiCell(30, 0, __("Date"), 0, 'L', 1);

        // Set the fill color for the date value and add the date value
        PDF::SetY($y_start);
        PDF::SetX(160);
        PDF::SetFillColor(222, 234, 246);
        PDF::MultiCell(40, 0, $receipt->date, 0, 'C', 0);

        // Set the starting Y position for the beneficiary value
        $y_start = PDF::GetY();
        PDF::SetY($y_start + 2);
        PDF::SetX(10);
        PDF::MultiCell(100, 0, $headBody, 0, 'S', 0);

        // Set the fill color for the receipt number label and add the receipt number label
        PDF::SetY($y_start+ 2);
        PDF::SetX(135);
        PDF::SetFillColor(245, 245, 245);
        PDF::MultiCell(30, 0, __("Receipt #"), 0, 'L', 1);

        // Set the fill color for the receipt number value and add the receipt number value
        PDF::SetY($y_start+ 2);
        PDF::SetX(160);
        PDF::SetFillColor(222, 234, 246);
        PDF::MultiCell(40, 0, $receipt->id, 0, 'C', 0);



        if(!is_null($receipt->details->remittanceId)){
            // Set the starting Y position for the remittiance value
            $y_start = PDF::GetY();
            PDF::SetY($y_start + 2);
            PDF::SetX(135);
            PDF::SetFillColor(245, 245, 245);
            PDF::MultiCell(30, 0, __("Remittiance #"), 0, 'L', 1);

            // Set the fill color for the receipt remittiance value and add the remittiance number value
            PDF::SetY($y_start+ 2);
            PDF::SetX(160);
            PDF::SetFillColor(222, 234, 246);
            PDF::MultiCell(40, 0, $receipt->details->remittanceId, 0, 'C', 0);
        }

        if(!is_null($receipt->details->referenceId)){
            // Set the starting Y position for the remittiance value
            // $y_start = PDF::GetY();
            // PDF::SetY($y_start+ 2);
            // PDF::SetX(135);
            // PDF::SetFillColor(245, 245, 245);
            // PDF::MultiCell(30, 0, __("Reference #"), 0, 'L', 1);

            // Set the fill color for the receipt remittiance value and add the remittiance number value
            // PDF::SetY($y_start+ 2);
            // PDF::SetX(160);
            // PDF::SetFillColor(222, 234, 246);
            // PDF::MultiCell(40, 0, $receipt->details->referenceId, 0, 'C', 0);


            // Set the fill color for the date label and add the date label
            $y_start = PDF::GetY();
            PDF::SetY($y_start+ 2);
            PDF::SetX(135);
            PDF::SetFillColor(245, 245, 245);
            PDF::MultiCell(60, 0, __("Reference #"), 0, 'C', 1);

            // Set the starting Y position for the beneficiary and date section
            $y_start = PDF::GetY() + 2;
            PDF::SetY($y_start);
            PDF::SetX(135);
            PDF::SetFillColor(222, 234, 246);
            PDF::MultiCell(60, 0, $receipt->details->referenceId, 0, 'C', 0);

        }

        PDF::Ln(20);
        PDF::SetFont('bein', 'B', 11);

        $receipt->statement=!is_null($receipt->details->remarks)?($receipt->statement.'<br>'.$receipt->details->remarks):$receipt->statement;
        //$remarks=__("Transfer money through").' ('.__($transfer->payload->bankCode). ')';
                    // .'<br>'.__("Name").': '.$transfer->payload->receiverName.'<br>'
                    // .__("Account Number").': '.$transfer->payload->receiverNumber;
        $total=0;
        $tbl = '<table class="first" cellspacing="0.5" cellpadding="6" border="0.5"  >';
        $tbl .= '<tr style="background-color:#f5f5f5;color:#000;">
					<td colspan="2" width="433" align="center"><b>'.__("Statement").'</b></td>
					<td width="100" align="center"><b>'.__("Amount").'</b></td>
				</tr>';

        if(!is_null($receipt->details->items)){
            foreach ($receipt->details->items as $key=>$item) {
                $total+=$item->details->amount->amount;
                $tbl .= '<tr nobr="true" style="background-color: #fafbfc">
                <td style="background-color:#f5f5f5;" align="center" width="30">' .($key+1). '</td>
                <td width="403">' .$item->statement. '</td>
                <td align="center">' . number_format($item->details->amount->amount ,2) .' <span>' .__($item->details->amount->currency).'</span></td>
                </tr>';
            }

        }else{
            $total+=$receipt->details->amount->amount;
            $tbl .= '<tr  nobr="true" style="background-color: #fafbfc">
            <td colspan="2">' .$receipt->statement. '</td>
            <td align="center">' . number_format($receipt->details->amount->amount ,2) .' <span>' .__($receipt->details->amount->currency).'</span></td>
            </tr>';
        }

        // $tbl .= '<tr  nobr="true" style="background-color: #fafbfc">
        //     <td width="160">' .__("Name"). '</td>
        //     <td>' . $transfer->payload->receiverName. '</td>
        //     </tr>';
        // $tbl .= '<tr  nobr="true" style="background-color: #fafbfc">
        //     <td width="160">' .__("Account Number"). '</td>
        //     <td>' . $transfer->payload->receiverNumber. '</td>
        //     </tr>';
        if(!is_null($receipt->details->fee)){
            if($receipt->details->fee->currency==$receipt->details->amount->currency){
                $total+=$receipt->details->fee->amount;
            }
            $tbl .= '<tr >
                <td colspan="2" align="left"><b>'.__("Fee").'</b></td>
                <td style="background-color:#f5f5f5;" align="center"><b>' . number_format($receipt->details->fee->amount, 2) .' <span>' .__($receipt->details->fee->currency).'</span></b></td>
            </tr>';
        }
        if(!(!is_null($receipt->details->fee) && $receipt->details->fee->currency!=$receipt->details->amount->currency)){
            $tbl .= '<tr >
                <td colspan="2" align="left"><b>'.__("Total").'</b></td>
                <td style="background-color:#f5f5f5;" align="center"><b>'. number_format($total, 2) .' <span>' .__($receipt->details->amount->currency).'</span></b></td>
            </tr>';
        }
        $tbl .= '</table>';
        PDF::writeHTML($tbl, true, false, false, false, '');

        //set response content type
        header('content-type: application/pdf');

        PDF::Output(($receipt->title??__("Receipt"))."-" . $receipt->id.'.pdf');

    }
}
