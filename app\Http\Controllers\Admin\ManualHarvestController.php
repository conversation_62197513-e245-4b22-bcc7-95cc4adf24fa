<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Harvest;
use App\Models\RemittanceNetwork;
use App\Enums\HarvestStatusEnum;
use App\Data\ManualHarvestData;
use App\Data\ManualProcessingStageData;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ManualHarvestController extends Controller
{
    /**
     * Display a listing of manual harvest requests
     */
    public function index(Request $request)
    {
        $query = Harvest::manual()
            ->with(['service', 'processedBy'])
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by stage
        if ($request->has('stage')) {
            $query->where('manual_stage', $request->stage);
        }

        // Search by tracking code
        if ($request->has('search')) {
            $search = $request->search;
            $query->whereRaw("JSON_EXTRACT(data, '$.trackingCode') LIKE ?", ["%{$search}%"]);
        }

        $harvests = $query->paginate(20);

        $statistics = [
            'total' => Harvest::manual()->count(),
            'pending' => Harvest::pendingManual()->count(),
            'completed' => Harvest::manual()->where('status', HarvestStatusEnum::MANUAL_COMPLETED->value)->count(),
            'rejected' => Harvest::manual()->where('status', HarvestStatusEnum::MANUAL_REJECTED->value)->count(),
            'by_stage' => [
                1 => Harvest::inManualStage(1)->count(),
                2 => Harvest::inManualStage(2)->count(),
                3 => Harvest::inManualStage(3)->count(),
                4 => Harvest::inManualStage(4)->count(),
                5 => Harvest::manual()->where('status', HarvestStatusEnum::MANUAL_COMPLETED->value)->count(),
            ]
        ];

        return view('admin.manual-harvest.index', compact('harvests', 'statistics'));
    }

    /**
     * Show the details of a specific manual harvest request
     */
    public function show(Harvest $harvest)
    {
        if (!$harvest->is_manual) {
            abort(404, 'This is not a manual harvest request');
        }

        $harvest->load(['service', 'processedBy', 'customer']);
        $manualHarvestData = ManualHarvestData::fromHarvest($harvest);
        $stages = ManualProcessingStageData::getStagesForHarvest($harvest);

        return view('admin.manual-harvest.show', compact('harvest', 'manualHarvestData', 'stages'));
    }

    /**
     * Stage 1: Customer Verification
     */
    public function verifyCustomer(Request $request, Harvest $harvest)
    {
        try {
            \Log::info("Verifying customer for harvest {$harvest->id}", [
                'current_stage' => $harvest->manual_stage,
                'status' => $harvest->status,
                'is_manual' => $harvest->is_manual
            ]);

            $this->validateManualHarvest($harvest, 1);

            $request->validate([
                'verification_status' => 'required|in:approved,rejected',
                'notes' => 'nullable|string|max:1000'
            ]);

            if ($request->verification_status === 'rejected') {
                return $this->rejectHarvest($harvest, $request->notes ?? 'Customer verification failed');
            }

            // Advance to stage 2
            $harvest->advanceManualStage(2, $request->notes);
            
            \Log::info("Customer verification completed for harvest {$harvest->id}", [
                'new_stage' => $harvest->manual_stage,
                'new_status' => $harvest->status
            ]);

            $this->sendNotification($harvest, 'Customer verification completed. Processing continues.');

            // Handle AJAX vs normal requests
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Customer verification completed successfully',
                    'next_stage' => 2,
                    'current_stage' => $harvest->manual_stage,
                    'status' => $harvest->status
                ]);
            } else {
                return redirect()->route('admin.manual-harvest.show', $harvest)
                    ->with('success', 'Customer verification completed successfully. Moved to stage 2.');
            }
        } catch (\Exception $e) {
            \Log::error("Error in verifyCustomer for harvest {$harvest->id}: " . $e->getMessage());
            
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 400);
            } else {
                return redirect()->route('admin.manual-harvest.show', $harvest)
                    ->with('error', 'Error: ' . $e->getMessage());
            }
        }
    }

    /**
     * Stage 2: Input Remittance Details
     */
    public function inputRemittanceDetails(Request $request, Harvest $harvest)
    {
        $this->validateManualHarvest($harvest, 2);

        $request->validate([
            'sender_name' => 'required|string|max:255',
            'sender_phone' => 'nullable|string|max:20',
            'sender_country' => 'nullable|string|max:100',
            'agent_name' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'purpose' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000'
        ]);

        // Update sender info
        $harvest->sender_info = (object)[
            'Sender_Full_Name' => $request->sender_name,
            'Sender_Mobile' => $request->sender_phone,
            'Sender_Country' => $request->sender_country,
            'Agent_Name' => $request->agent_name,
        ];

        // Update amount
        $harvest->amount = [
            'amount' => $request->amount,
            'currency' => $request->currency
        ];

        $harvest->advanceManualStage(3, $request->notes);
        $this->sendNotification($harvest, 'Remittance details have been entered. Under review.');

        // Handle AJAX vs normal requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Remittance details saved successfully',
                'next_stage' => 3
            ]);
        } else {
            return redirect()->route('admin.manual-harvest.show', $harvest)
                ->with('success', 'Remittance details saved successfully. Moved to stage 3.');
        }
    }

    /**
     * Stage 3: Review and Approve
     */
    public function reviewAndApprove(Request $request, Harvest $harvest)
    {
        $this->validateManualHarvest($harvest, 3);

        $request->validate([
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($request->action === 'reject') {
            return $this->rejectHarvest($harvest, $request->notes ?? 'Failed review process');
        }

        $harvest->advanceManualStage(4, $request->notes);
        $this->sendNotification($harvest, 'Your remittance has been approved and will be processed shortly.');

        // Handle AJAX vs normal requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Harvest approved successfully',
                'next_stage' => 4
            ]);
        } else {
            return redirect()->route('admin.manual-harvest.show', $harvest)
                ->with('success', 'Harvest approved successfully. Moved to stage 4.');
        }
    }

    /**
     * Stage 4: Execute Transfer
     */
    public function executeTransfer(Request $request, Harvest $harvest)
    {
        $this->validateManualHarvest($harvest, 4);

        $request->validate([
            'execution_notes' => 'nullable|string|max:1000',
            'reference_id' => 'nullable|string|max:100'
        ]);

        DB::transaction(function () use ($harvest, $request) {
            // Update received_data with manual processing info
            $harvest->received_data = (object)[
                'Result_Code' => 0,
                'Result_Desc' => 'Manual processing completed successfully',
                'Payout_Trx_Id' => $request->reference_id ?? 'MANUAL_' . time(),
                'Transaction_Date' => now()->toISOString(),
                'Settlement_Amount' => $harvest->amount->amount ?? 0,
                'Settlement_Currency' => $harvest->amount->currency ?? 'YER',
                'Processing_Method' => 'Manual'
            ];

            $harvest->completeManualProcessing(
                auth()->id(),
                $request->execution_notes
            );
        });

        $this->sendNotification($harvest, 'Your remittance has been successfully processed and deposited to your account.');

        // Handle AJAX vs normal requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Transfer executed successfully',
                'harvest_id' => $harvest->id,
                'status' => 'completed'
            ]);
        } else {
            return redirect()->route('admin.manual-harvest.show', $harvest)
                ->with('success', 'Transfer executed successfully. Harvest completed.');
        }
    }

    /**
     * Reject a manual harvest request
     */
    protected function rejectHarvest(Harvest $harvest, string $reason)
    {
        $harvest->rejectManualProcessing(auth()->id(), $reason);
        $this->sendNotification($harvest, "Your remittance request has been rejected. Reason: {$reason}");

        // Return appropriate response based on request type
        if (request()->wantsJson() || request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Harvest rejected',
                'status' => 'rejected'
            ]);
        } else {
            return redirect()->route('admin.manual-harvest.show', $harvest)
                ->with('success', 'Harvest has been rejected.');
        }
    }

    /**
     * Validate manual harvest state (improved)
     */
    protected function validateManualHarvest(Harvest $harvest, int $expectedStage)
    {
        if (!$harvest->is_manual) {
            abort(400, 'This is not a manual harvest request');
        }

        // Allow processing if harvest is in the expected stage OR the previous stage
        // This handles cases where the stage hasn't been updated yet
        $allowedStages = [$expectedStage];
        if ($expectedStage > 1) {
            $allowedStages[] = $expectedStage - 1;
        }

        if (!in_array($harvest->manual_stage, $allowedStages)) {
            abort(400, "Harvest is not in the correct stage. Current: {$harvest->manual_stage}, Expected: {$expectedStage}");
        }

        if (!$harvest->isPending()) {
            abort(400, 'Harvest is not in a pending state. Current status: ' . $harvest->status);
        }
    }

    /**
     * Send notification to customer
     */
    protected function sendNotification(Harvest $harvest, string $message)
    {
        NotificationService::sendMessagesToParty([
            [
                'title' => 'Remittance Update',
                'body' => $message,
                'type' => 'operation',
            ]
        ], $harvest->party_id);
    }

    /**
     * Get manual harvest statistics
     */
    public function statistics()
    {
        $stats = [
            'overview' => [
                'total_requests' => Harvest::manual()->count(),
                'pending_requests' => Harvest::pendingManual()->count(),
                'completed_today' => Harvest::manual()
                    ->where('status', HarvestStatusEnum::MANUAL_COMPLETED->value)
                    ->whereDate('manual_completed_at', today())
                    ->count(),
                'average_processing_time' => $this->getAverageProcessingTime()
            ],
            'by_stage' => [
                'verification' => Harvest::inManualStage(1)->count(),
                'data_input' => Harvest::inManualStage(2)->count(),
                'review' => Harvest::inManualStage(3)->count(),
                'approved' => Harvest::inManualStage(4)->count(),
                'completed' => Harvest::manual()->where('status', HarvestStatusEnum::MANUAL_COMPLETED->value)->count(),
                'rejected' => Harvest::manual()->where('status', HarvestStatusEnum::MANUAL_REJECTED->value)->count(),
            ],
            'by_network' => $this->getStatsByNetwork(),
            'processing_times' => $this->getProcessingTimeStats()
        ];

        return response()->json($stats);
    }

    /**
     * Get average processing time in hours
     */
    protected function getAverageProcessingTime()
    {
        $completed = Harvest::manual()
            ->where('status', HarvestStatusEnum::MANUAL_COMPLETED->value)
            ->whereNotNull('manual_started_at')
            ->whereNotNull('manual_completed_at')
            ->get();

        if ($completed->isEmpty()) {
            return 0;
        }

        $totalHours = $completed->sum(function ($harvest) {
            return $harvest->manual_started_at->diffInHours($harvest->manual_completed_at);
        });

        return round($totalHours / $completed->count(), 2);
    }

    /**
     * Get statistics by network
     */
    protected function getStatsByNetwork()
    {
        return Harvest::manual()
            ->join('harvest_service_codes', 'harvests.service_code_id', '=', 'harvest_service_codes.id')
            ->join('remittance_networks', 'harvest_service_codes.id', '=', 'remittance_networks.service_code_id')
            ->select('remittance_networks.name', DB::raw('count(*) as total'))
            ->groupBy('remittance_networks.name')
            ->get()
            ->pluck('total', 'name')
            ->toArray();
    }

    /**
     * Get processing time statistics
     */
    protected function getProcessingTimeStats()
    {
        $ranges = [
            '0-6 hours' => 0,
            '6-12 hours' => 0,
            '12-24 hours' => 0,
            '24-48 hours' => 0,
            '48+ hours' => 0
        ];

        $completed = Harvest::manual()
            ->where('status', HarvestStatusEnum::MANUAL_COMPLETED->value)
            ->whereNotNull('manual_started_at')
            ->whereNotNull('manual_completed_at')
            ->get();

        foreach ($completed as $harvest) {
            $hours = $harvest->manual_started_at->diffInHours($harvest->manual_completed_at);
            
            if ($hours <= 6) {
                $ranges['0-6 hours']++;
            } elseif ($hours <= 12) {
                $ranges['6-12 hours']++;
            } elseif ($hours <= 24) {
                $ranges['12-24 hours']++;
            } elseif ($hours <= 48) {
                $ranges['24-48 hours']++;
            } else {
                $ranges['48+ hours']++;
            }
        }

        return $ranges;
    }
}
