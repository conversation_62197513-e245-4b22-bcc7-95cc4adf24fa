<?php

namespace App\Enums;
enum YeahMoneyReceivedStatusEnum:int {
    case NEW=1;
    case ONHOLD=2;
    case AVAILABLE=3;
    case SUSPENDED=4;
    case CANCELED=5;
    case AGED=6;
    case PAID=7;
    case RETURNED=8;
    case ROUTED=9;

    public static function values():array {
        return collect(YeahMoneyReceivedStatusEnum::cases())->pluck("value")->toArray();
    }
    public static function keys():array {
        return collect(YeahMoneyReceivedStatusEnum::cases())->pluck("name")->toArray();
    }
    public static function findByValue(string $value):YeahMoneyReceivedStatusEnum|null {
        return collect(YeahMoneyReceivedStatusEnum::cases())->filter(function($item) use($value){
            return $item->value==$value;
        })->first();
    }
    public static function findByKey(string $key):YeahMoneyReceivedStatusEnum|null {
        $key=strtoupper(str_replace("-", "", $key));
        return collect(YeahMoneyReceivedStatusEnum::cases())->filter(function($item) use($key){
            return $item->name==$key;
        })->first();
    }
    public static function disabledCheck():array {
        return collect(YeahMoneyReceivedStatusEnum::cases())->filter(function($item) {
            return in_array($item,[YeahMoneyReceivedStatusEnum::CANCELED,YeahMoneyReceivedStatusEnum::ROUTED]);
        })->toArray();
    }

    public static function enabledCheck():array {
        return collect(YeahMoneyReceivedStatusEnum::cases())->filter(function($item) {
            return !in_array($item,[YeahMoneyReceivedStatusEnum::CANCELED,YeahMoneyReceivedStatusEnum::ROUTED]);
        })->toArray();
    }

}
