<?php

namespace App\Services\YeahMoney;
use App\Data\AccountIdData;
use App\Data\Agent\AgentGeneralItemData;
use App\Data\Agent\CustomerAccountData;
use App\Data\Agent\DepositRequestBaseData;
use App\Data\Agent\DepositRequestConfirmData;
use App\Data\Agent\DepositRequestData;
use App\Data\CreditCardData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\GoldWithdrawData;
use App\Data\TokenData;

use App\Models\Harvest;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;

use App;
use App\Models\User;
use Spatie\LaravelData\DataCollection;

/**
 * Service to create and update orders
 */
class AgentService
{
    protected $settings;
    protected $isTest=false;

    /**
     * Service to create and update orders
     */
    public function __construct()
    {
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->agent;
        if($this->settings->is_test){
            $this->isTest=true;
            $this->settings=$this->settings->test_data;
        }
        //$this->updateInstid();
    }

    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }
        if(!isset($token->access_token)|| is_null($token->access_token)||empty($token->access_token)){
            $response = Http::timeout(10)
            ->withBasicAuth($this->settings->client_id, $this->settings->client_secret)

            ->asForm()
            ->post("{$this->settings->url}/oauth/token",[
                "grant_type"=>"client_credentials"
            ]);

            if ($response->failed()) {
                return $response;
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->access_token)){
                $this->settings->token=TokenData::from($response);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $agent=$settings->agent;
                if($agent->is_test){
                    $test_data=$agent->test_data;
                    $test_data->token=$this->settings->token;
                    $agent->test_data=$test_data;
                }else{
                    $agent->token=$this->settings->token;
                }
                $settings->agent=$agent;
                $settings->save();
            }
           // dd($response);

        }
        return $status;
    }
    public function doLogin(&$request)
    {
        $status=$this->getAccessToken(($request->retry??-1)>=0);
        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ]);
        }

        $timestamp=round(microtime(true)*1000);

        $agent=$this->settings->agent_info;
        $agent['User_Info']['Agent_User_Name']=$request->agentName;
        $agent['Agent_Code']=$request->agentCode;
        $agent['Branch_Code']=$request->agentBranch;

        //Start login process
        $response = Http::withToken($this->settings->token->access_token)
        ->withHeaders([
            "Trx_Ref_Id"=>$timestamp
        ])
        ->post(
            "{$this->settings->url}/mt/v1/login",
            [
                "Agent_Info" => $agent
            ]
        );
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return $this->doLogin($request);
        }

        $authResult=$response->object();
        if($response->failed() || $response->status()!=200 || ($authResult->Result_Code??-1)!=0){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YEAH_MONEY_SERVER_LOGIN] - ".($authResult->Result_Code??-1),
                    "message"   => [
                        "title"   => "Something wrong please connect customer support for help!",
                        "detail"  => "Something wrong please connect customer support for help!",
                        "code"    => "DIGX_SWITCH_AGENT_003",
                        "type"    => "ERROR"
                    ]
                 ]
            ]);
        }
        return GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "[YEAH_MONEY_SERVER_LOGIN]",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
             ]
            ])->additional([
                'timestamp'=>$timestamp,
                'token'=>$response->header("Trx_Token"),
            ]);
    }
    public static function getHome(DepositRequestBaseData $request) {

        $agentService=new AgentService();
        $loginResult=$agentService->doLogin($request);
        if($loginResult->status->message->code!="0"){
            return $loginResult;
        }
        $timestamp=$loginResult->getAdditionalData()['timestamp'];
        $token=$loginResult->getAdditionalData()['token'];

        $finalResult=[];

        if(is_null($request->serviceCode)){
            $response = Http::
            fake([
                "{$agentService->settings->url}/mt/v1/payinServices" => Http::response( [
                    "Result_Code"=> 0,
                    "Result_Desc"=>"Success",
                    "Services"=>[
                        [
                            "Code" => "YMNY",
                            "Name" => "Yeah Money Services"
                        ],
                        [
                            "Code" => "SHIFT",
                            "Name" => "SHIFT Financial Services Ltd."
                        ]
                    ]
                ], 200, []),
            ])->
            withToken($agentService->settings->token->access_token)
            ->withHeaders([
                "Model_Code"    =>"AGBK",
                "Trx_Ref_Id"    =>$timestamp
            ])
            ->get("{$agentService->settings->url}/mt/v1/payinServices");

            if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
                $request->retry=($request->retry??-1)+1;
                return static::getHome($request);
            }
            $result=$response->object();

            if(!empty($result->Services??[])){
                $services=collect($result->Services??[]);
                $service=$services->first();
                $request->serviceCode=$service->Code;
                $finalResult['services']=AgentGeneralItemData::collect($services,DataCollection::class);
            }
        }


        if(is_null($request->countryCode)){
            $response = Http::
            fake([
                "{$agentService->settings->url}/mt/v1/payoutCountries" => Http::response( [
                    "Result_Code"=> 0,
                    "Result_Desc"=>"Success",
                    "Countries"=>[
                        [
                            "Code" => "YE",
                            "Name" => "Yemen"
                        ]
                    ]
                ], 200, []),
            ])->
            withToken($agentService->settings->token->access_token)
            ->withHeaders([
                "Model_Code"    =>"AGBK",
                "Service_Code"  =>$service->id??"YMNY",
                "Trx_Ref_Id"    =>$timestamp
            ])
            ->post("{$agentService->settings->url}/mt/v1/payoutCountries",[
                "Product_Code"=> "CASH_TO_ACB"
            ]);

            if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
                $request->retry=($request->retry??-1)+1;
                return static::getHome($request);
            }
            $result=$response->object();

            if(!empty($result->Countries??[])){
                $countries=collect($result->Countries??[]);
                $country=$countries->first();
                $request->countryCode=$country->Code;
                $finalResult['countries']=AgentGeneralItemData::collect($countries,DataCollection::class);

            }
        }

        if(is_null($request->inCurrencyCode)){
            $responses = Http::
            fake([
                "{$agentService->settings->url}/mt/v1/payinCurrencies" => Http::response( [
                    "Result_Code"=> 0,
                    "Result_Desc"=>"Success",
                    "Currencies"=>[
                        [
                            "Code" => "SAR",
                            "Name" => "Saudi Riyal"
                        ],
                        [
                            "Code" => "YER",
                            "Name" => "Rial Yemeni"
                        ],
                        [
                            "Code" => "USD",
                            "Name" => "United States Dollar"
                        ]
                    ]
                ], 200, []),
                "{$agentService->settings->url}/mt/v1/payoutCurrencies" => Http::response( [
                    "Result_Code"=> 0,
                    "Result_Desc"=>"Success",
                    "Currencies"=>[
                        [
                            "Code" => "YER",
                            "Name" => "Rial Yemeni"
                        ],
                        [
                            "Code" => "USD",
                            "Name" => "United States Dollar"
                        ]
                    ]
                ], 200, []),
            ])->
            pool(fn (Pool $pool) => [
                $pool->as('inCurrencies')
                    ->withToken($agentService->settings->token->access_token)
                    ->withHeaders([
                        "Model_Code"    =>"AGBK",
                        "Service_Code"  =>$request->serviceCode??"YMNY",
                        "Trx_Ref_Id"    =>$timestamp
                    ])
                    ->post("{$agentService->settings->url}/mt/v1/payinCurrencies",[
                        "Product_Code"=> "CASH_TO_ACB",
                        "Country_Code"=> $request->countryCode??"YE"
                    ]),
                $pool->as('outCurrencies')
                    ->withToken($agentService->settings->token->access_token)
                    ->withHeaders([
                        "Model_Code"    =>"AGBK",
                        "Service_Code"  =>$request->serviceCode??"YMNY",
                        "Trx_Ref_Id"    =>$timestamp
                    ])
                    ->post("{$agentService->settings->url}/mt/v1/payoutCurrencies",[
                        "Product_Code"=> "CASH_TO_ACB",
                        "Country_Code"=> $request->countryCode??"YE"
                    ]),
            ]);
            if(in_array($responses['inCurrencies']->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
                $request->retry=($request->retry??-1)+1;
                return static::getHome($request);
            }
            $result=$responses['inCurrencies']->object();
            $inCurrencies=$result->Currencies??[];

            $result=$responses['outCurrencies']->object();
            $outCurrencies=$result->Currencies??[];

            if(!empty($inCurrencies) && !empty($outCurrencies)){
                $inCurrency=collect($inCurrencies)->first();
                $outCurrency=collect($outCurrencies)->first();
                $request->inCurrencyCode=$inCurrency->Code;
                $request->amount=CurrencyAmountData::from([
                    "amount"    =>0,
                    "currency"  =>$outCurrency->Code,
                ]);
                $finalResult['inCurrencies']=AgentGeneralItemData::collect($inCurrencies,DataCollection::class);
                $finalResult['outCurrencies']=AgentGeneralItemData::collect($outCurrencies,DataCollection::class);

            }
        }


        if(is_null($request->partyCode) && !is_null($request->inCurrencyCode) && !is_null($request->amount)){
            $response = Http::
            fake([
                "{$agentService->settings->url}/mt/v1/payoutParties" => Http::response( [
                    "Result_Code"=> 0,
                    "Result_Desc"=>"Success",
                    "Parties"=>[
                        [
                            "Code" => "YKB",
                            "Name" => "Yemen Kuwait Bank"
                        ],
                        [
                            "Code" => "YKIB",
                            "Name" => "Yemen kuwait Islamic Bank"
                        ]
                    ]
                ], 200, []),
            ])->
            withToken($agentService->settings->token->access_token)
            ->withHeaders([
                "Model_Code"    =>"AGBK",
                "Service_Code"  =>$request->serviceCode??"YMNY",
                "Trx_Ref_Id"    =>$timestamp
            ])
            ->post("{$agentService->settings->url}/mt/v1/payoutParties",[
                "Product_Code"=> "CASH_TO_ACB",
                "Payout_Country_Code"=> $request->countryCode??"YE",
                "Payin_Currency_Code"=> $request->inCurrencyCode,
                "Payout_Currency_Code"=> $request->amount->currency,
            ]);

            if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
                $request->retry=($request->retry??-1)+1;
                return static::getHome($request);
            }
            $result=$response->object();
            if(!empty($result->Parties??[])){
                $parties=collect($result->Parties??[]);
                $party=$parties->first();
                $request->partyCode=$party->Code;
                $finalResult['parties']=AgentGeneralItemData::collect($parties,DataCollection::class);

                //$country=$parties->first();
               // $request->countryCode=$country->id;
            }
        }

        if(!is_null($request->customerAccountId)){
            $response = Http::
            fake([
                "{$agentService->settings->url}/mt/v1/customerPayoutBranch" => Http::response( [
                    "Result_Code"=> 0,
                    "Result_Desc"=>"Success",
                    "Accounts"=>[
                        [
                            "Branch_Code" => "YKBSATA01",
                            "Name" => "خالد عبد الله على شرف الدين",
                            "Customer_Account_No" => "4020178236YERCUCS018",
                        ]
                    ]
                ], 200, []),
            ])->
            withToken($agentService->settings->token->access_token)
            ->withHeaders([
                "Model_Code"    =>"AGBK",
                "Service_Code"  =>$request->serviceCode??"YMNY",
                "Trx_Ref_Id"    =>$timestamp
            ])
            ->post("{$agentService->settings->url}/mt/v1/customerPayoutBranch",[
                "Product_Code"=> "CASH_TO_ACB",
                "Payout_Country_Code"=> $request->countryCode,
                "Payin_Currency_Code"=> $request->inCurrencyCode,
                "Payout_Currency_Code"=> $request->amount->currency,
                "Party_Code"=> $request->partyCode,
                "Account_Info"=>[
                    "Customer_Account_No"=>$request->customerAccountId->value
                ]
            ]);

            if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
                $request->retry=($request->retry??-1)+1;
                return static::getHome($request);
            }
            $result=$response->object();
            if(!empty($result->Accounts??[])){
                $accounts=collect($result->Accounts??[]);
                $finalResult['accounts']=CustomerAccountData::collect($accounts,DataCollection::class);
            }
        }


        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional($finalResult);

        // $responses = Http::
        // fake([
        //     "{$agentService->settings->url}/api/v1/GetPayoutCountries" => Http::response( [
        //         [
        //             "activity_id" => *********,
        //             "activity_name" => "سداد فواتير"
        //         ],
        //         [
        //             "activity_id" => *********,
        //             "activity_name" => "زارعي"
        //         ]
        //      ], 200, []),
        //      "{$agentService->settings->url}/api/v1/sectorTypes" => Http::response( [
        //         [
        //             "sector_id" => 1,
        //             "sector_name" => "تجاري"
        //         ],
        //         [
        //             "sector_id" => 2,
        //             "sector_name" => "خدمي"
        //         ]
        //      ], 200, []),
        //      "{$agentService->settings->url}/api/v1/products" => Http::response( [
        //         [
        //             "product_id" => 100000,
        //             "product_name" => "قرض تجاري",
        //             "maximum_amount" => 100000,
        //             "minimum_amount" => 10000,
        //             "acceptable_amount_for_new_loan" => 10000,
        //             "payment_method" => "FULL",
        //             "currency_code" => [
        //                 [
        //                     "id"=> *********,
        //                     "currency_code"=> "YER"
        //                 ],
        //                 [
        //                     "id"=> 100000021,
        //                     "currency_code"=> "SAR"
        //                 ]
        //             ],
        //             "payment_period" => 139,
        //             "notes" => "no Notes"
        //         ]
        //     ], 200, [])
        // ])->
        // pool(fn (Pool $pool) => [
        //     $pool->as('countries')
        //         ->withToken($agentService->settings->token->access_token)
        //         ->withHeaders([
        //             "Model_Code"    =>"AGBK",
        //             "Service_Code"  =>$request->serviceCode??"YMNY",
        //             "Trx_Ref_Id"    =>$timestamp
        //         ])
        //         ->post("{$agentService->settings->url}/mt/v1/GetPayoutCountries",[
        //             "Product_Code"=> "CASH_TO_ACB"
        //         ]),
        //     $pool->as('inCurrencies')
        //         ->withToken($agentService->settings->token->access_token)
        //         ->withHeaders([
        //             "Model_Code"    =>"AGBK",
        //             "Service_Code"  =>$request->serviceCode??"YMNY",
        //             "Trx_Ref_Id"    =>$timestamp
        //         ])
        //         ->post("{$agentService->settings->url}/mt/v1/GetPayinCurrencies",[
        //             "Product_Code"=> "CASH_TO_ACB",
        //             "Country_Code"=> $request->countryCode??"YE"
        //         ]),
        //     $pool->as('outCurrencies')
        //         ->withToken($agentService->settings->token->access_token)
        //         ->withHeaders([
        //             "Model_Code"    =>"AGBK",
        //             "Service_Code"  =>$request->serviceCode??"YMNY",
        //             "Trx_Ref_Id"    =>$timestamp
        //         ])
        //         ->post("{$agentService->settings->url}/mt/v1/GetPayoutCurrencies",[
        //             "Product_Code"=> "CASH_TO_ACB",
        //             "Country_Code"=> $request->countryCode??"YE"
        //         ]),
        // ]);
        // if(in_array($responses['countries']->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
        //     $request->retry=($request->retry??-1)+1;
        //     return static::getHome($request);
        // }


        // $countries=$responses['countries']->object();
        // $inCurrencies=$responses['inCurrencies']->object();
        // $outCurrencies=$responses['outCurrencies']->object();

        // return GeneralResponseData::from(array(
        //     'status'=>[
        //         "result"    => "SUCCESSFUL",
        //         "contextID" => "",
        //         "message"   => [
        //             "code"    => "0",
        //             "type"    => "INFO"
        //         ]
        //     ]
        // ))->additional([
        //     'countries'=>AgentGeneralItemData::collect($countries->Countries??[],DataCollection::class),
        //     'inCurrencies'=>AgentGeneralItemData::collect($inCurrencies->Currencies??[],DataCollection::class),
        //     'outCurrencies'=>AgentGeneralItemData::collect($outCurrencies->Currencies??[],DataCollection::class),
        // ]);

    }
    public static function getCountries(DepositRequestBaseData $request) {

        $agentService=new AgentService();
        $loginResult=$agentService->doLogin($request);
        if($loginResult->status->message->code!="0"){
            return $loginResult;
        }
        $timestamp=$loginResult->getAdditionalData()['timestamp'];
        $token=$loginResult->getAdditionalData()['token'];

        $response = Http::withToken($agentService->settings->token->access_token)
        ->withHeaders([
            "Model_Code"    =>"AGBK",
            "Service_Code"  =>$request->serviceCode??"YMNY",
            "Trx_Ref_Id"    =>$timestamp
        ])
        ->post("{$agentService->settings->url}/mt/v1/payoutCountries",[
            "Product_Code"=> "CASH_TO_ACB"
        ]);

        if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
            $request->retry=($request->retry??-1)+1;
            return static::getCountries($request);
        }
        $result=$response->object();

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'countries'=>AgentGeneralItemData::collect($result->Countries??[],DataCollection::class),
        ]);
    }
    public static function getCurrencies(DepositRequestBaseData $request) {

        $agentService=new AgentService();
        $loginResult=$agentService->doLogin($request);
        if($loginResult->status->message->code!="0"){
            return $loginResult;
        }
        $timestamp=$loginResult->getAdditionalData()['timestamp'];
        $token=$loginResult->getAdditionalData()['token'];

        $responses = Http::

        pool(fn (Pool $pool) => [
            $pool->as('inCurrencies')
                ->withToken($agentService->settings->token->access_token)
                ->withHeaders([
                    "Model_Code"    =>"AGBK",
                    "Service_Code"  =>$request->serviceCode??"YMNY",
                    "Trx_Ref_Id"    =>$timestamp
                ])
                ->post("{$agentService->settings->url}/mt/v1/payinCurrencies",[
                    "Product_Code"=> "CASH_TO_ACB",
                    "Country_Code"=> $request->countryCode??"YE"
                ]),
            $pool->as('outCurrencies')
                ->withToken($agentService->settings->token->access_token)
                ->withHeaders([
                    "Model_Code"    =>"AGBK",
                    "Service_Code"  =>$request->serviceCode??"YMNY",
                    "Trx_Ref_Id"    =>$timestamp
                ])
                ->post("{$agentService->settings->url}/mt/v1/payoutCurrencies",[
                    "Product_Code"=> "CASH_TO_ACB",
                    "Country_Code"=> $request->countryCode??"YE"
                ]),
        ]);
        if(in_array($responses['countries']->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
            $request->retry=($request->retry??-1)+1;
            return static::getHome($request);
        }


        $inCurrencies=$responses['inCurrencies']->object();
        $outCurrencies=$responses['outCurrencies']->object();

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'inCurrencies'=>AgentGeneralItemData::collect($inCurrencies->Currencies??[],DataCollection::class),
            'outCurrencies'=>AgentGeneralItemData::collect($outCurrencies->Currencies??[],DataCollection::class),
        ]);

    }
    public static function getParties(DepositRequestBaseData $request) {

        $agentService=new AgentService();
        $loginResult=$agentService->doLogin($request);
        if($loginResult->status->message->code!="0"){
            return $loginResult;
        }
        $timestamp=$loginResult->getAdditionalData()['timestamp'];
        $token=$loginResult->getAdditionalData()['token'];

        $response = Http::withToken($agentService->settings->token->access_token)
        ->withHeaders([
            "Model_Code"    =>"AGBK",
            "Service_Code"  =>$request->serviceCode??"YMNY",
            "Trx_Ref_Id"    =>$timestamp
        ])
        ->post("{$agentService->settings->url}/mt/v1/payoutParties",[
            "Product_Code"=> "CASH_TO_ACB",
            "Payout_Country_Code"=> $request->countryCode,
            "Payin_Currency_Code"=> $request->inCurrencyCode,
            "Payout_Currency_Code"=> $request->amount->currency,
        ]);

        if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
            $request->retry=($request->retry??-1)+1;
            return static::getParties($request);
        }


        $parties=$response->object();
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'parties'=>AgentGeneralItemData::collect($parties->Parties??[],DataCollection::class),
        ]);

    }
    public static function getCustomerAccounts(DepositRequestBaseData $request) {

        $agentService=new AgentService();
        $loginResult=$agentService->doLogin($request);
        if($loginResult->status->message->code!="0"){
            return $loginResult;
        }
        $timestamp=$loginResult->getAdditionalData()['timestamp'];
        $token=$loginResult->getAdditionalData()['token'];

        $response = Http::withToken($agentService->settings->token->access_token)
        ->withHeaders([
            "Model_Code"    =>"AGBK",
            "Service_Code"  =>$request->serviceCode??"YMNY",
            "Trx_Ref_Id"    =>$timestamp
        ])
        ->post("{$agentService->settings->url}/mt/v1/customerPayoutBranch",[
            "Product_Code"=> "CASH_TO_ACB",
            "Payout_Country_Code"=> $request->countryCode,
            "Payin_Currency_Code"=> $request->inCurrencyCode,
            "Payout_Currency_Code"=> $request->amount->currency,
            "Party_Code"=> $request->partyCode,
            "Account_Info"=>[
                "Customer_Account_No"=>$request->customerAccountId->value
            ]
        ]);

        if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
            $request->retry=($request->retry??-1)+1;
            return static::getCustomerAccounts($request);
        }

        $result=$response->object();
        if($response->failed() || $response->status()!=200 || ($result->Result_Code??-1)!=0 || empty($result->Account_Info??[])){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YEAH_MONEY_SERVER_ENQUIRY] - ".($result->Result_Code??-1),
                    "message"   => [
                        "title"   => $result->Result_Desc??"Something wrong please connect customer support for help!",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_AGENT_004",
                        "type"    => "ERROR"
                    ]
                 ]
            ]);
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'accounts'=>CustomerAccountData::collect($result->Accounts??[],DataCollection::class),
        ]);

    }

    public static function initC2A(DepositRequestBaseData $request)
    {

        $agentService=new AgentService();
        $loginResult=$agentService->doLogin($request);
        if($loginResult->status->message->code!="0"){
            return $loginResult;
        }
        $timestamp=$loginResult->getAdditionalData()['timestamp'];
        $token=$loginResult->getAdditionalData()['token'];

        // Start getting Payout Info.
        $response = Http::
        fake([
            "{$agentService->settings->url}/mt/v1/payinInfo" => Http::response( [
                "Result_Code"=> 0,
                "Result_Desc"=> "Success",
                "Payin_Info"=> [
                    "Payin_Amount"=> 1000,
                    "Payin_Currency"=> "YER",
                    "Fee_Amount"=> 300,
                    "Payin_Fee_Amount"=> 100,
                    "Fee_Currency_Code"=> "YER",
                    "Tax_Type"=> "F",
                    "Tax_Amount"=> 0,
                    "Total_Fee_Amount"=> 300,
                    "Total_Amount"=> 1300,
                    "Payout_Amount"=> 1000,
                    "Payout_Currency_Code"=> "YER",
                    "Payin_to_Payout_Rate"=> 1,
                    "Payout_to_Payin_Rate"=> 1
                ],
                "Settllement_Info"=> [
                    "Settlement_Amount"=> 1000,
                    "Settlement_Currency_Code"=> "YER",
                    "Settlement_Rate"=> 1,
                    "Settlement_Fee_Amount"=> 200,
                    "Settlement_Fee_Currency_Code"=> "YER",
                    "Settlement_Fee_Rate"=> 1,
                    "Settlement_Tax_Amount"=> 0,
                    "Settlement_Total_Fee_Amount"=> 200
                ],
                "Fields_Options"=> [
                    "Sender_Fields"=> [
                        [
                            "Field_Code"=> "Sender_First_Name",
                            "Field_Name"=> "First Name",
                            "Field_Required"=> "Y",
                            "Field_Enabled"=> "Y"
                        ],
                        [
                            "Field_Code"=> "Sender_Second_Name",
                            "Field_Name"=> "Second Name",
                            "Field_Required"=> "Y",
                            "Field_Enabled"=> "Y"
                        ],
                        [
                            "Field_Code"=> "Sender_Third_Name",
                            "Field_Name"=> "Third Name",
                            "Field_Required"=> "Y",
                            "Field_Enabled"=> "Y"
                        ],
                        [
                            "Field_Code"=> "Sender_Surname",
                            "Field_Name"=> "Last Name",
                            "Field_Required"=> "Y",
                            "Field_Enabled"=> "Y"
                        ],
                        [
                            "Field_Code"=> "Sender_Full_Name",
                            "Field_Name"=> "Name",
                            "Field_Required"=> "Y",
                            "Field_Enabled"=> "Y"
                        ],
                        [
                            "Field_Code"=> "Sender_Phone",
                            "Field_Required"=> "N",
                            "Field_Enabled"=> "Y"
                        ]
                    ],
                    "Sender_Version"=> 1,
                    "Receiver_Fields"=> [
                        [
                            "Field_Code"=> "Receiver_Account_No",
                            "Field_Name"=> "Account Number",
                            "Field_Required"=> "Y",
                            "Field_Enabled"=> "N"
                        ]
                    ],
                    "Receiver_Version"=> 1,
                    "Payin_Fields"=> [],
                    "Payin_Version"=> 1
                ]
            ], 200, []),
        ])
        ->withToken($agentService->settings->token->access_token)
        ->withHeaders([
            "Trx_Token"     =>str_replace("Bearer ","",$token),
            "Model_Code"    =>"AGBK",
            "Service_Code"  =>$request->serviceCode??"YMNY",
            "Trx_Ref_Id"    =>$timestamp
        ])
        ->post(
            "{$agentService->settings->url}/mt/v1/payinInfo",
            [
                "Product_Code"          => "CASH_TO_ACB",
                "Payin_Data" => [
                    "Country_Code"=> $request->countryCode,
                    "City_Code"=> "",
                    "Location_Code"=> "",
                    "Amount_Type"=> "I",
                    "Payin_Currency_Code"=> $request->inCurrencyCode,
                    "Payin_Amount"=> $request->amount->amount,
                    "Payout_Currency_Code"=> $request->amount->currency,
                    //"Payout_Amount"=> null,
                    "Party_Code"=> $request->partyCode,
                    //"Agent_Code"=> "YKB",
                    "Branch_Code"=> $request->branchCode,
                    "Customer_Account_No"=> $request->customerAccountId->value,
                    "Account_Id"=> null
                ]
            ]
        );

        if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
            $request->retry=($request->retry??-1)+1;
            return static::initC2A($request);
        }

        $result=$response->object();
        if($response->failed() || $response->status()!=200 || ($result->Result_Code??-1)!=0){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YEAH_MONEY_SERVER_ENQUIRY] - ".($result->Result_Code??-1),
                    "message"   => [
                        "title"   => $result->Result_Desc??"Something wrong please connect customer support for help!",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_AGENT_004",
                        "type"    => "ERROR"
                    ]
                 ]
            ]);
        }

        $token=$response->header("Trx_Token");

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'timestamp'=>$timestamp,
            'token'=>$token,
            'result'=>$result,
        ]);
    }

    public static function confirmC2A(DepositRequestConfirmData $request)
    {

        $agentService=new AgentService();
        $loginResult=$agentService->doLogin($request);
        if($loginResult->status->message->code!="0"){
            return $loginResult;
        }
        $timestamp=$loginResult->getAdditionalData()['timestamp'];
        $token=$loginResult->getAdditionalData()['token'];

        // Start getting Payout Info.
        $response = Http::
        fake([
            "{$agentService->settings->url}/mt/v1/payin" => Http::response( [
                "Result_Code"=> 0,
                "Result_Desc"=> "Success",
                "Payin_Trx_Id"=> 100000000048088,
                "Unique_Tracking_Code"=> "420030114",
                "Payin_Agent_Number"=> "1694523584",
                "Payin_Reference_Number "=> "7"
            ], 200, []),
        ])
        ->withToken($agentService->settings->token->access_token)
        ->withHeaders([
            "Trx_Token"     =>str_replace("Bearer ","",$token),
            "Model_Code"    =>"AGBK",
            "Service_Code"  =>$request->serviceCode??"YMNY",
            "Trx_Ref_Id"    =>$timestamp
        ])
        ->post(
            "{$agentService->settings->url}/mt/v1/payin",
            [
                "Product_Code"          => "CASH_TO_ACB",
                "Payin_Data" => [
                    "Country_Code"=> $request->countryCode,
                    "City_Code"=> "",
                    "Location_Code"=> "",
                    "Amount_Type"=> "I",
                    "Payin_Currency_Code"=> $request->inCurrencyCode,
                    "Payin_Amount"=> $request->amount->amount,
                    "Payout_Currency_Code"=> $request->amount->currency,
                    //"Payout_Amount"=> null,
                    "Party_Code"=> $request->partyCode,
                    //"Agent_Code"=> "YKB",
                    "Branch_Code"=> $request->branchCode,
                    "Customer_Account_No"=> $request->customerAccountId->value,
                    "Account_Id"=> null
                ],
                "Trx_Info" => [
                    "Fee_Amount"=> $request->fee->amount,
                    "Fee_Currency_Code"=>  $request->fee->currency,
                ],
                "Payin_Info" => [
                    "Puropose_Of_Remittance"=> "",
                    "Purpose"=> "",
                    "Payin_Notes"=> "",
                    "Message_to_Beneficiary"=> "",
                    "Agent_Unique_Tracking_Code"=> "",
                ],
                "Sender_Info" => [
                    "Sender_Type"=> "I",
                    "Sender_First_Name"=> $request->firstName,
                    "Sender_Second_Name"=> $request->secondName,
                    "Sender_Third_Name"=> $request->thirdName,
                    "Sender_Fourth_Name"=> "",
                    "Sender_Surname"=> $request->lastName,
                ],
                "Receiver_Info" => [
                    "Receiver_Type"=> "I",
                    "Receiver_Account_No"=> $request->customerAccountId->value
                ]
            ]
        );

        if(in_array($response->status(),[\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED,\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN])){
            $request->retry=($request->retry??-1)+1;
            return static::confirmC2A($request);
        }

        $result=$response->object();
        if($response->failed() || $response->status()!=200 || ($result->Result_Code??-1)!=0){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YEAH_MONEY_SERVER_ENQUIRY] - ".($result->Result_Code??-1),
                    "message"   => [
                        "title"   => $result->Result_Desc??"Something wrong please connect customer support for help!",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_AGENT_004",
                        "type"    => "ERROR"
                    ]
                 ]
            ])->additional([
                'result'=>$result,
            ]);
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'externalReferenceId' => $result->Payin_Trx_Id??"",
            'result'=>$result,
        ]);
    }

}
