<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference_id',
        'party_id',
        'service',
        'item',
        'bundle',
        'subscriber_number',
        'debit_account_id',
        'amount',
        'fee',
        'remarks',
        'status',
        'payload',
        'extra',
        'request',
        'payment_result'
    ];

    protected $casts = [
        'debit_account_id' => 'object',
        'amount' => 'object',
        'fee' => 'object',
        'payload' => 'object',
        'extra' => 'object',
        'request' => 'object',
        'payment_result' => 'object',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            //if(is_null($query->party_id))
                $query->party_id = auth()->user()->id;
        });
    }
    public function service()
    {
        return $this->belongsTo(BillPaymentService::class, 'service');
    }

    public function bundle()
    {
        return $this->belongsTo(BillPaymentBundle::class, 'bundle');
    }
    public function item()
    {
        return $this->belongsTo(BillPaymentItem::class, 'item');
    }



    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', 'model_id')->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}
