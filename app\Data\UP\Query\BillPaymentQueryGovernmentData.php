<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\BaseNonNullableDataCollection;
use App\Data\CurrencyAmountData;
use App\Data\Form\FormBaseData;
use App\Data\Form\FormFieldData;
use App\Data\Form\FormOptionData;
use App\Data\Form\FormValidationData;
use App\Data\NameData;
use App\Data\StatusData;
use Carbon\Carbon;
use Lang;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\DataCollection;
use function PHPUnit\Framework\returnValue;


class BillPaymentQueryGovernmentData extends BillPaymentQueryBaseData
{
    const dataToShow=[
        "subscriberName",
        "packageBalance",
        "billingNo",
        "issueDate",
        "expiryDate",
        "message",
        "billNo",
        "dueAmount",
        "lowestToPay"
    ];
    public function __construct(
        public ?StatusData $status=null,
        #[MapInputName('ExtData'),DataCollectionOf(BillPaymentQueryExtData::class)]
        public ?DataCollection $extData=null,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        if(!is_null(data_get($properties,"SD.ExtData")) && !is_null(data_get($properties,"SD.ExtData.afmis_details"))){
            $properties['ExtData']= [data_get($properties,"SD.ExtData.afmis_details")];
        }

        $properties=parent::prepareForPipeline($properties);
        return $properties;
    }
    public function getAmountbyBillNo($id)
    {
        $item= $this->extData->filter(function($item) use($id){
            return $item->billNo==$id;
        })->first();
        return $item?->dueAmount??null;
    }
    public function toAppResponse(): BillPaymentQueryResponseData
    {
        $parent=parent::toAppResponse();
        if(!is_null($parent)){
            return $parent;
        }
        $filled=null;
        $items=[];
        $firstItem=$this->extData->first();
        $sum=0;
        $this->extData->each(function($item)use(&$sum){
            $sum=$sum+$item->dueAmount;
        });
        $fields=null;
        if(count($this->extData) && in_array(request()->item,[1244])){
            $field=new FormFieldData(
                label: new NameData(
                    ar:Lang::get("Violations", locale: 'ar'),
                    en:"Violations"
                ),
                name:'extra:billno',
                element:"radio",
                validations:FormValidationData::collect([
                    FormValidationData::requiredValidation(),
                ],BaseNonNullableDataCollection::class),
                options:FormOptionData::collect($this->extData->map(function($item){
                    if(str_contains($item->message,'/')){
                        $msg=str_replace('/','',strstr($item->message, '/', false));
                    }else{
                        $msg=$item->message;
                    }

                    return new FormOptionData(
                        id:$item->billNo,
                        name:new NameData(
                            ar:$msg,
                            en:$msg
                        ),
                        subtitle:$item->issueDate->format("Y-m-d"),
                        description:"{$item->dueAmount} YER",
                    );
                }),BaseNonNullableDataCollection::class)
            );
            $fields=FormBaseData::collect([
                $field
            ],BaseNonNullableDataCollection::class);
        }
        $items=[
            new BillPaymentQueryResponseItemData(
                foregroundColor: "#ffffff",
                backgroundColor: "#0069A7",
                item:new BillPaymentQueryResponseSubitemItemData(
                    foregroundColor: "#ffffff",
                    title:new NameData(
                        ar:Lang::get('Subscriber', locale: 'ar'),
                        en:"Subscriber"
                    ),
                ),
                subitem:new BillPaymentQueryResponseSubitemItemData(
                    foregroundColor: "#ffffff",
                    title:new NameData(
                        ar:$firstItem->subscriberName,
                        en:$firstItem->subscriberName
                    ),
                )

            ),
            new BillPaymentQueryResponseItemData(
                item:new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Amount', locale: 'ar'),
                        en:"Amount"
                    ),
                ),
                subitem:new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:$sum,
                        en:$sum
                    ),
                )

            ),


        ];
        if(!in_array(request()->item,[1244])){
             $filled=[
                "amount"=>[
                    "amount"=>$firstItem->dueAmount
                ]
            ];
            $items[]=new BillPaymentQueryResponseItemData(
                        // item:new BillPaymentQueryResponseSubitemItemData(
                        //     title:new NameData(
                        //         ar:Lang::get('You have several violations. Please choose one of the violations to pay from.', locale: 'ar'),
                        //         en:"You have several violations. Please choose one of the violations to pay from."
                        //     ),
                        // ),
                        subitem:new BillPaymentQueryResponseSubitemItemData(
                            type:'button',
                            title:new NameData(
                                ar:Lang::get('Bill Details', locale: 'ar'),
                                en:"Bill Details"
                            ),
                            items:BillPaymentQueryResponseSubitemItemData::collect($this->extData->map(function($item,$key){
                                return new BillPaymentQueryResponseSubitemItemData(
                                    foregroundColor: "#ffffff",
                                    backgroundColor: "#0069A7",
                                    title:new NameData(
                                        ar:Lang::get('Bill :item',['item'=>$key+1], locale: 'ar'),
                                        en:Lang::get('Bill :item',['item'=>$key+1], locale: 'en')
                                    ),
                                    // subtitle:new NameData(
                                    //     ar:Lang::get('Select', locale: 'ar'),
                                    //     en:'Select'
                                    // ),

                                    items:BillPaymentQueryResponseSubitemItemData::collect(collect($item)->filter(function($value,$key){
                                        return in_array($key,self::dataToShow);
                                    })->map(function($value,$key){
                                        // if($value instanceof Carbon){
                                        //     $value=$value?->toDateTimeString();
                                        // }
                                        return new BillPaymentQueryResponseSubitemItemData(
                                            title:new NameData(
                                                ar:Lang::get("$key", locale: 'ar'),
                                                en:Lang::get("$key", locale: 'en')
                                            ),
                                            subtitle:new NameData(
                                                ar:$value instanceof Carbon?$value->toDateTimeString():"$value",
                                                en:"$value"
                                            ),
                                        );
                                    })->values(),BaseNonNullableDataCollection::class)
                                );
                            }),BaseNonNullableDataCollection::class)
                        )

                    );
        }
        return new BillPaymentQueryResponseData(
            status: $this->status,
            fields:$fields,
            filled:$filled,
            items: BillPaymentQueryResponseItemData::collect($items, BaseNonNullableDataCollection::class)
        );

    }
}
