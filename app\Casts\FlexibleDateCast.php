<?php

namespace App\Casts;

use Carbon\Carbon;
use <PERSON><PERSON>\LaravelData\Support\Creation\CreationContext;
use Spatie\LaravelData\Support\DataProperty;
use Spatie\LaravelData\Support\Transformation\TransformationContext;
use Spatie\LaravelData\Casts\Cast;
use DateTimeInterface;

class FlexibleDateCast implements Cast
{
    public function cast(DataProperty $property, mixed $value, array $properties, CreationContext $context): ?DateTimeInterface
    {
        if ($value === null) {
            return null;
        }

          $formats = ['YmdHis', 'Y-m-d\TH:i:s'];
        $timezone = config('app.timezone') ?? 'UTC';

        foreach ($formats as $format) {
            try {
                $parsed = Carbon::createFromFormat($format, $value, $timezone);
                if ($parsed !== false) {
                    return $parsed->setTimezone($timezone);
                }
            } catch (\Throwable $e) {
                continue; // Try next format
            }
        }

        try {
            return Carbon::parse($value, $timezone)->setTimezone($timezone);
        } catch (\Throwable $e) {
            throw new \InvalidArgumentException("Unable to parse date: {$value}");
        }
    }

    public function transform(DateTimeInterface $value, TransformationContext $context): string
    {
        // You can customize the output format here
        return $value->format('Y-m-d H:i:s');
    }
}
