<?php

namespace App\Data;

use App\Helpers\BaseHelper;
use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class AccountData extends Data
{

    public AccountIdData $id;
    #[MapName('partyId')]
    public ?AccountIdData $_partyId;
    #[MapName('availableBalance')]
    public ?CurrencyAmountData $balance;
    public ?string $accountNickName;
    public ?string $status;
    public ?string $type;
    #[MapName('currencyCode')]
    public ?string $currencyId;
    #[MapName('branchCode')]
    public ?string $branchId;

    public ?array $openCurrencies;
    public ?array $excludeCurrencies;

    //#[MapInputName('productDTO.productId')]
    //public ?string $productId;

    public function partyId():?string {
        return $this->_partyId->value;
    }
    public function currencyName():?string {
        return __("{$this->currencyId}");
    }
    public function currencyFullName():?string {
        return __("{$this->currencyId}-Full");
    }
    public function branchName():?string {
        return __("{$this->branchId}");
    }
    public function productId():?string {
        return $this->id->productId();
    }
    public function productName():?string {
        return __("{$this->productId()}");
    }

    public function fullName():?string {
        return $this->branchName()." ".$this->productName();
    }

    public function isNorth():bool {
        return in_array($this->branchId??"",BranchData::north());
    }
    public function isSouth():bool {
        return in_array($this->branchId??"",BranchData::south());
    }
    public function isCurrent():bool {
        return in_array($this->productId(),ProductData::current());
    }

    public function isSaving():bool {
        return in_array($this->productId(),ProductData::saving());
    }
    // سلف
    public function isAd():bool {
        return in_array($this->productId(),ProductData::ad());
    }
    //عهد
    public function isSt():bool {
        return in_array($this->productId(),ProductData::st());
    }

    public function isCorporation():bool {
        return in_array($this->productId(),ProductData::corporation());
    }

    public function isIndividual():bool {
        return in_array($this->productId(),ProductData::individual());
    }
    public function isCreditCard():bool {
        return in_array($this->productId(),ProductData::creditCard());
    }
    public function isCash():bool {
        return $this->isAllowCashAccountClass();
    }
    public function isCashOut():bool {
        return $this->isAllowCashAccountClassOut();
    }
    public function isNonCash():bool {
        return !$this->isAllowCashAccountClass();
    }
    protected function isAllowCashAccountClass():bool {
        return in_array($this->productId(),ProductData::cash()) ||
        (in_array($this->productId(),ProductData::cashNoneYER()) && $this->currencyId!="YER");
    }


    protected function isAllowCashAccountClassOut():bool {
        return in_array($this->productId(),ProductData::cashOut()) ||
        (in_array($this->productId(),ProductData::cashOutNoneYER()) && $this->currencyId!="YER");
    }
    // public function allowedProductsToOpen():array {
    //     $result=ProductData::allowedCurrenciesToOpen();
    //     $productCurrencies=collect($result[$this->productId()][$this->currencyId]);
    //     return $productCurrencies->keys()->all();
    // }
    public function allowedProductsToOpen($currency):array {
        $result=ProductData::allowedCurrenciesToOpen();
        $productCurrencies=collect($result[$this->productId()][$this->currencyId]);
        $products=$productCurrencies->filter(function ($element, $key) use($currency){
            return in_array($currency,$element);
        })->keys()->all();
        if(!request()->filled("product")){
            $products=collect($products)->filter(function ($element) {
                return !in_array($element,['PNPL']);
            })->all();
        }
        return $products;
    }
    public function allowedCurrenciesToOpen($accounts,$request):array {
        if(($request->header('appVersion')??0)>140){
            $result=ProductData::allowedCurrenciesToOpen();
        }else{
            $result=ProductData::allowedCurrenciesToOpenOld();
        }
        if(!isset($result[$this->productId()][$this->currencyId])){
            return [];
        }
        $productCurrencies=collect($result[$this->productId()][$this->currencyId]);
        $products=$productCurrencies->keys()->all();
        if(empty($products)){
            return [];
        }
        $account=$this;
        $currencies=$accounts
        ->filter(function (AccountData $element,int $key) use($account,$products){
            return $element->branchId==$account->branchId && in_array($element->productId(),$products);
        })->all();
        $currencies=collect($currencies)->map(function ($element,int $key){
            return $element->currencyId;
        })->toArray();
        return collect($productCurrencies->values()->flatten(1)->values()->all())->diff($currencies)->values()->all();
    }

    public function isAllowELoanCompletePayment():bool {
        return false;//in_array($this->productId(),ProductData::eloan());
    }
    public function isGold():bool {
        return in_array($this->productId(),ProductData::gold());
    }
    public function allowedService($code):bool {
        return in_array($code,$this->availableServices());

    }
    public function availableServices():array {
        $items=app(\App\Settings\ConfigSettings::class)->accountConfig;
        $items=collect($items)->filter(function ($element,string $key) {
            return in_array($this->branchId,($element['branches']??[])) &&
            in_array($this->productId(),($element['products']??[])) &&
            in_array($this->currencyId,($element['currencies']??[]));
        })->keys()->toArray();
        // $ii=new AccountConfigData;
        // $ii->
        return $items;
    }
    public function with():array {
        return [
            //'partyId'=>$this->_partyId,
            'productDTO'=>[
                "productId"=>$this->productId()
            ],
            'isCash'=>$this->isCash(),
            'isNorth'=>$this->isNorth(),
            'isCashOut'=>$this->isCashOut(),
            'isGold'=>$this->isGold(),
            'availableServices'=>$this->availableServices(),

            //'availableCurrencies'=>$this->allowedCurrenciesToOpen(),

        ];
    }

    // $accounts=$accounts->where('status',"ACTIVE")
    // ->filter(function (AccountData $element,int $key) use($accoutId){
    //     //only chosen account.
    //     return $element->id->value ==$accoutId->value;
    // })
    // ->filter(function (AccountData $element,int $key) {
    //     //only cash & !ad & !st accounts.
    //     return $element->isCash() && !$element->isAd() && !$element->isSt();
    // })->filter(function (AccountData $element,int $key) {
    //     //which have currency YER & branch in north side.
    //     return $element->currencyId==\App\Enums\CurrencyTypeEnum::YER->value && $element->isNorth();
    // });
}
