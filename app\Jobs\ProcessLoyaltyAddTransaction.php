<?php

namespace App\Jobs;

use App\Data\Pass\PassCreateRequestData;
use App\Data\PaymentResultData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\Gift;
use App\Models\Pass;
use App\Models\PassTransaction;
use App\Scopes\CustomerScope;
use App\Services\FlexService;
use App\Services\LoyaltyService;
use App\Services\NotificationService;
use App\Services\PassService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessLoyaltyAddTransaction implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

       /**
     * Create a new job instance.
     * @param object $user
     * @param float $amount
     * @param string $note
     * @param string $cardId
     * @return void
     */
    public function __construct(
        public object $user,
        public float $amount,
        public string $note,
        public string $cardId
    ){
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $identifier=LoyaltyService::getCustomerIdentifier($this->user);
        if(!is_null($identifier)){
            $object=new \stdClass();
            $object->note=$this->note;
            $object->amount= $this->amount;
            $object->cardId= $this->cardId;
            $object->memberId= $identifier;
            LoyaltyService::addPurchase( $object);
        }


    }
}
