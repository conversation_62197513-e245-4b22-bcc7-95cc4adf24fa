<?php

namespace App\Services;
use App\Data\GeneralResponseData;
use App\Data\TokenData;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;

use App;
use App\Models\User;

/**
 * Service to create and update orders
 */
class LoanService
{
    protected $settings;

    /**
     * Service to create and update orders
     */
    public function __construct()
    {
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->loan;
        if($this->settings->is_test){
            $this->settings=$this->settings->test_data;
        }
    }
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }
        if(is_null($token->access_token)||empty($token->access_token)){
            $response = Http::
            timeout(10)
            ->withBasicAuth($this->settings->client_id, $this->settings->client_secret)
            ->asForm()
            ->post("{$this->settings->url}/oauth/token",[
                "grant_type"=>"client_credentials"
            ]);

            if ($response->failed()) {
                //dd($response);
                return $response;
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->access_token)){
                $this->settings->token=TokenData::from($response);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $loan=$settings->loan;
                if($loan->is_test){
                    $test_data=$loan->test_data;
                    $test_data->token=$this->settings->token;
                    $loan->test_data=$test_data;
                }else{
                    $loan->token=$this->settings->token;
                }
                $settings->loan=$loan;
                $settings->save();
            }
           // dd($response);

        }
        return $status;
    }

    public static function orders($request)
    {
        //App::setLocale(request()->header("lang")??"ar");
        $loanService=new LoanService();
        $status=$loanService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access loan server, please connect help desk to fix problem!",
                        "detail"  => "Can't access loan server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_000",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $response = Http::
        withToken($loanService->settings->token->access_token)
        ->withHeaders([
            "lang"=>App::getLocale()
        ])
        ->get("{$loanService->settings->url}/api/v1/order",[
            "customer_no"=> $request->partyId
        ]);
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::orders($request);
        }

        $result=$response->object();
        return GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => ($result->resultCode??$response->status()??'')." - ".($result->resultDesc??'') ,
                "message"   => [
                    "code"    => "0",
                    "type"    => "INFO"
                ]
             ],
        ])->additional([
            'orders'=>$result->order??[]
        ]);
    }
    public static function create($request)
    {
        //App::setLocale(request()->header("lang")??"ar");

        $loanService=new LoanService();
        $status=$loanService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access loan server, please connect help desk to fix problem!",
                        "detail"  => "Can't access loan server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_000",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $responses = Http:: pool(fn (Pool $pool) => [
            $pool->as('activities')
                ->withToken($loanService->settings->token->access_token)
                ->withHeaders([
                    "lang"=>App::getLocale()
                ])
                ->get("{$loanService->settings->url}/api/v1/activities"),
            $pool->as('sectors')
                ->withToken($loanService->settings->token->access_token)
                ->withHeaders([
                    "lang"=>App::getLocale()
                ])
                ->get("{$loanService->settings->url}/api/v1/sectorTypes"),
            $pool->as('products')
                ->withToken($loanService->settings->token->access_token)
                ->withHeaders([
                    "lang"=>App::getLocale()
                ])
                ->get("{$loanService->settings->url}/api/v1/products"),
            $pool->as('customer')
                ->withToken($loanService->settings->token->access_token)
                ->withHeaders([
                    "lang"=>App::getLocale()
                ])
                ->get("{$loanService->settings->url}/api/v1/customer?p_cust_no={$request->partyId}"),
        ]);;
        if($responses['activities']->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::create($request);
        }

        $customer=$responses['customer']->object();
        return GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ],
        ])->additional([
            'activities'=>$responses['activities']->object(),
            'sectors'=>$responses['sectors']->object(),
            'products'=>$responses['products']->object(),
            'customer'=> $customer->info??null,
        ]);
    }

    public static function request($request)
    {
        //App::setLocale(request()->header("lang")??"ar");
        $loanService=new LoanService();
        $status=$loanService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access loan server, please connect help desk to fix problem!",
                        "detail"  => "Can't access loan server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_000",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $response = Http::
        withToken($loanService->settings->token->access_token)
        ->withHeaders([
            "lang"=>App::getLocale()
        ])
        ->timeout(120)
        ->post("{$loanService->settings->url}/api/v1/order",[
            "customer_no" => $request->customer_no,
            "product_code" => $request->product_code,
            "amount" => $request->amount,
            "currency_code" => $request->currency,
            "sector_code" => $request->sector_code,
            "activity_id" => $request->activity_id,
            "activity_name" => $request->activity_name,
            "activity_phone" => $request->activity_phone,
            "activity_address" => $request->activity_address,
            "closest_branch_id" => $request->closest_branch_id,
            "payment_reason" => $request->payment_reason??"",
        ]);
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::request($request);
        }

        $result=$response->object();
        if($response->status()==200 && isset($result->resultCode) && $result->resultCode==0){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => $result->resultDesc??"Your request under review, you will get notification after request reviewed.",
                        "detail"  => $result->resultDesc??"Your request under review, you will get notification after request reviewed.",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ]);
        }
        $status=$result->resultCode??$response->status();
        return GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "{$status}",
                "message"   => [
                    "title"   => $result->resultDesc??"Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->resultDesc??"Can't process your order, please connect help desk to fix problem!",
                    "code"    => "{$status}",
                    "type"    => "ERROR"
                ]
             ]
        ]);
    }
}
