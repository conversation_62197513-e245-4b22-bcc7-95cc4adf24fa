<?php

namespace App\Http\Controllers\Admin;
use App;
use App\Http\Controllers\Controller;
use App\Models\CustomerType;
use App\Models\Service;
use App\Models\ServiceCustomerRole;
use App\Models\ServiceCustomerType;
use Illuminate\Http\Request;
use Auth;
use Illuminate\Validation\Rule;
use Session;
class ServiceController extends Controller
{

    protected $types=[
        "item"=>"Item",
        "banner"=>"Banner",
        "header"=>"Header",
        "help"=>"Help",
        "quickSection"=>"Quick section",
        "section"=>"Section",
        "offerWithCards"=>"Offer with cards",
        "offer"=>"Offer",
        "transaction"=>"Transaction",
        "fx"=>"Foreign Exchange",
    ];

    protected $osTypes=[
        "android",
        "ios"
    ];
    public function __construct(Request $request){$this->middleware('auth');}
    public function getTypes(){
        return $this->types;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['service.item.*','service.item.list'])) return abort(401);

        $filter=$request->all();

        $items=Service::with(['items'=>function($query){
            return $query->whereNotNull('service_id');
        }])
        //->whereNull('service_id')
        ->orderBy('id','asc');

        if($request->filled('customer_type'))
            $items=$items->whereHas('customerTypes',function($query) use($filter){
                return $query->where('id',$filter['customer_type']);
            });

        if($request->filled('type') ){
            if($request->type=='item' ){
                $items=$items->whereNull('type');
            }else{
                $items=$items->where('type',$request->type);
            }
        }

        if($request->filled('parent') ){
            $items=$items->whereNotNull('service_id')->where('service_id',$request->parent);
        }

        if($request->filled('searchname'))
            $items=$items->where(function($query) use($filter){
                return $query->where('title->ar','like','%'.$filter['searchname'].'%')
                ->orWhere('title->en','like','%'.$filter['searchname'].'%');
            });

        $items=$items->paginate(15);

        $customer_types=CustomerType::get();//pluck('name','id');
        $customer_types=collect($customer_types)->map(function ($element, $key){
            return [
                "id"=>$element->id,
                "name"=>__("$element->name")
            ];
        })->pluck('name','id')->toArray();//->collapse()->toArray();

        $lang=App::getLocale();
        $parents=Service::select("id","title->$lang as main_name")->whereNull('service_id')
        ->where(function($query) use($filter){
            return $query->whereNotNull('type')
            ->whereIn('type',["help","section","offerWithCards"]);
        })
        ->whereNotNull('type')
        ->orderBy('id','asc')
        ->pluck('main_name','id')
        ->toArray();

        return view('default.admin.service.index')
        ->with('items', $items)
        ->with('parents', $parents)
        ->with('customer_types', $customer_types)
        ->with('types', $this->types)
        ->with('filter', $filter);
    }

    /**
     * Copy.
     *
     * @param  \App\Models\Service  $service
     * @return \Illuminate\Contracts\View\View
     */
    public function copy(Service $service)
    {
        //if (! Auth::user()->canAny(['service.item.*','service.item.create'])) return abort(401);
        return redirect("/admin/service/create")
        ->with('item',$service);
    }
    public function dashboard(Request $request)
    {
        if (! Auth::user()->canAny(['service.dashboard.*','service.dashboard.list','service.dashboard.view'])) return abort(401);

        $items=Service::with(['items'=>function($query){
            $query
            ->whereNotNull('service_id');
            // if(env('APP_ENV', 'production')=='production'){
            //     $query->where('status','<>',0);
            // }
            return $query;
        }])
        ->whereNull('service_id')
        ->orderBy('sort','asc');
        // if(env('APP_ENV', 'production')=='production'){
        //     $items= $items->where('status','<>',0);
        // }
        //$serviceItemData=ServiceItemData::collect($items->get());
        //$serviceItemData[0]->title = 'Giving Up on Love';
        // $sections=collect($items->get())->whereNotNull('type');
        // dd($sections);
        //in_array($item->type,['header','help','offer','transaction'])

        // $sections=collect($items->get())->whereNotNull('type');
        // $sections_containers=$sections->map(function ($item) {
        //     return "{$item->type}-{$item->id}";
        // });
        // dd($sections_containers);
        //return response()->json($items->get());

        return view('default.admin.service.dashboard')->with('items',$items->get());
    }

    public function autocomplete(Request $request){
        $data=$request->all();
        $result= Service::select('title','id')
        ->whereNull('service_id')
        ->whereNull('type')
        ->where(function($query) use($data){
            return $query->where('title->ar','like','%'.$data['query'].'%')
            ->orWhere('title->en','like','%'.$data['query'].'%');
        })
        ->limit(10)
        ->get();
        return response()->json($result);
    }

    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        if (! Auth::user()->canAny(['service.item.*','service.item.create'])) return abort(401);

        $services=Service::select("id","title","tag")->whereNull('service_id')
        ->whereNotNull('type')
        ->orderBy('sort','asc')->get();

        $page_services=Service::select("id","title","tag")
        ->whereNull('type')
        ->orderBy('sort','asc')->get();

        $page_services=collect($page_services)->pluck('name','id');
        $customer_types=CustomerType::select('id','name','roles')->get();

        if(Session::has('item')){
            $service=Session::get('item');
            // if(!ctype_xdigit($services->icon)){
            //     $base64 = base64_encode(file_get_contents($services->icon));
            //     $mime = mime_content_type($services->icon);
            //     $services->icon="data:$mime;base64,$base64";
            // }

            //remove id from service
            unset($service->id);
            return view('default.admin.service.view')
            ->with('services', $services)
            ->with('item', $service)
            ->with('page_services', $page_services)
            ->with('customer_types', $customer_types)
            ->with('os_types', $this->osTypes)
            ->with('types', $this->types);
        }

        return view('default.admin.service.view')
        ->with('services', $services)
        ->with('page_services', $page_services)
        ->with('customer_types', $customer_types)
        ->with('os_types', $this->osTypes)
        ->with('types', $this->types);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['service.item.*','service.item.create'])) return abort(401);


        $rules = [
            //'title.en' => 'required|max:191',
           // 'title.ar' => 'required|max:191',
           // 'title.en' => 'required_if:type,!offer,!help|max:191',
           // 'title.ar' => 'required_if:type,!offer,!help|max:191',
            "quick_access"  => "required",
            'icon' => 'nullable',
            'banner.title.ar' => 'nullable|string',
            'banner.title.en' => 'nullable|string',
            'banner.backgroundColor' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'banner.foregroundColor' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'banner.image.ar' => 'nullable',
            'banner.image.en' => 'nullable',
            'banner.imageWithTitle' => 'nullable|in:0,1',
            'banner.imageColor.background' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'banner.imageColor.foreground' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'url' => 'nullable',
            'help_url' => 'nullable',
            'sort' => 'required|integer',
            'app_version' => 'nullable|integer',
            'tag' => 'nullable|string|max:191',
            'type' => 'required|in:'.join(",",collect($this->types)->keys()->toArray()),
            'description.ar' => 'nullable|string',
            'description.en' => 'nullable|string',
            'service_id' => 'nullable|exists:services,id',
            'trending_service' => 'nullable|exists:services,id',
            'loyalty_card_id' => 'nullable',
            'customer_services' => 'array',
            'customer_services.*.customer_type_id' => 'nullable|exists:customer_types,id',
            'customer_services.*.page_service_id' => 'nullable|exists:services,id',
            'customer_services.*.status' => 'nullable|in:0,1,2,3',
            'customer_services.*.status_type' => 'nullable|in:U',
            'customer_services.*.roles' => 'required_if:customer_services.*.customer_type_id,'.join(',',[CustomerType::RETAIL,CustomerType::CORPORATE,CustomerType::BUSINESS,CustomerType::AGENT]).'|array',
            'customer_services.*.roles.*' => 'in:checker,maker,full,basic',
            'services'=>'nullable|array',
            'allow_more' => 'required|in:0,1',
            'os_types' => 'nullable|array',
            'os_types.*' => 'in:android,ios',
        ];

        if (!in_array($request->type, ["offer", "help"])) {
            $rules['title.en'] = 'required|max:191';
            $rules['title.ar'] = 'required|max:191';
        }
        $this->validate($request, $rules );
        return $this->save($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Service  $service
     * @return \Illuminate\Contracts\View\View
     */
    public function show(Service $service){
        if (! Auth::user()->canAny(['service.item.*','service.item.view','service.item.edit'])) return abort(401);

        $service->load('customerTypes');
        $services=Service::select("id","title","tag")
        ->whereNull('service_id')
        ->whereNotNull('type')
        ->orderBy('sort','asc')->get();

        $page_services=Service::select("id","title","tag")
        ->where(function($query){
            $query->whereNull('type')
            ->orWhere('type','banner');
        })
        ->orderBy('sort','asc')->get();

        $page_services=collect($page_services)->pluck('name','id');



        $customer_types=CustomerType::select('id','name','roles')->get();
        //return response()->json( $service->customerTypes);

        // $assigned_customer_roles_array =  [];
        //     foreach ($service->customerTypes as $type) {
        //         if($type->id==CustomerType::CORPORATE){
        //            // $type->pivot->customerRoles();
        //           // $type->pivot->load('customerRoles');
        //             return response()->json( $type->pivot->customerRoles?->pluck('role_identifer')?->toArray()??[]);

        //         }
        //         //$assigned_customer_roles_array[$type->id]=$type->pivot?->customerRoles()?->pluck('role_identifer')?->toArray()??[];
        //     }
        //   //  return;
        // dd($assigned_customer_roles_array);
        // return;
      // return response()->json($assigned_customer_roles_array);
        return view('default.admin.service.view')
        ->with('item', $service)
        ->with('services', $services)
        ->with('page_services', $page_services)
        ->with('customer_types', $customer_types)
        ->with('os_types', $this->osTypes)
        ->with('types', $this->types);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Service  $service
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Service $service)
    {
        if (! Auth::user()->canAny(['service.item.*','service.item.edit'])) return abort(401);

        $rules = [
            //'title.en' => 'required|max:191',
           // 'title.ar' => 'required|max:191',
           // 'title.en' => 'required_if:type,!offer,!help|max:191',
           // 'title.ar' => 'required_if:type,!offer,!help|max:191',
            "quick_access"  => "required",
            'icon' => 'nullable',
            'banner.title.ar' => 'nullable|string',
            'banner.title.en' => 'nullable|string',
            'banner.backgroundColor' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'banner.foregroundColor' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'banner.image.ar' => 'nullable',
            'banner.image.en' => 'nullable',
            'banner.imageWithTitle' => 'nullable|in:0,1',
            'banner.imageColor.background' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'banner.imageColor.foreground' => ['nullable', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
            'url' => 'nullable',
            'help_url' => 'nullable',
            'sort' => 'required|integer',
            'app_version' => 'nullable|integer',
            'tag' => 'nullable|string|max:191',
            'type' => 'required|in:'.join(",",collect($this->types)->keys()->toArray()),
            'description.ar' => 'nullable|string',
            'description.en' => 'nullable|string',
            'service_id' => 'nullable|exists:services,id',
            'trending_service' => 'nullable|exists:services,id',
            'loyalty_card_id' => 'nullable',
            'customer_services' => 'array',
            'customer_services.*.customer_type_id' => 'nullable|exists:customer_types,id',
            'customer_services.*.page_service_id' => 'nullable|exists:services,id',
            'customer_services.*.status' => 'nullable|in:0,1,2,3',
            'customer_services.*.status_type' => 'nullable|in:U',
            'customer_services.*.roles' => 'required_if:customer_services.*.customer_type_id,'.join(',',[CustomerType::RETAIL,CustomerType::CORPORATE,CustomerType::BUSINESS,CustomerType::AGENT]).'|array',
            'customer_services.*.roles.*' => 'in:checker,maker,full,basic',
            'services'=>'nullable|array',
            'allow_more' => 'required|in:0,1',
            'os_types' => 'nullable|array',
            'os_types.*' => 'in:android,ios',
        ];

        if (!in_array($request->type, ["offer", "help"])) {
            $rules['title.en'] = 'required|max:191';
            $rules['title.ar'] = 'required|max:191';
        }

        $this->validate($request, $rules );
        return $this->save($request,$service);
    }

    public function save(Request $request, ?Service $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new Service;
        }

        $this->setImage('icon',"services");
        $this->setImage('banner.image.ar',"services");
        $this->setImage('banner.image.en',"services");

        // $this->validate($request,[
        //     "icon" => 'required',
        //     "banner.image.ar" => 'required',
        //     "banner.image.en" => 'required',
        // ]);
        // return response()->json($request->all());
        // dd($request->all());
        // return;


        $banner=null;
        if($request->filled("banner.title.ar") && $request->filled("banner.title.en")){
            $banner=[];
            $banner["title"]=$request->input("banner.title");
            if($request->filled("banner.backgroundColor") && $request->input("banner.backgroundColor")!="#000000"){
                $banner["backgroundColor"]=$request->input("banner.backgroundColor");
                if($request->filled("banner.foregroundColor")){
                    $banner["foregroundColor"]=$request->input("banner.foregroundColor");
                }
            }
        }
        if(!is_null($request->input("banner.image.ar")) && !is_null($request->input("banner.image.en"))){
            $banner??=[];
            $banner["imageWithTitle"]=$request->input("banner.imageWithTitle");
            $banner["image"]["ar"]=$request->input("banner.image.ar");
            $banner["image"]["en"]=$request->input("banner.image.en");
            if($request->filled("banner.imageColor.background") && $request->input("banner.imageColor.background")!="#000000"){
                $banner["imageColor"]["background"]=$request->input("banner.imageColor.background");
            }
            if($request->filled("banner.imageColor.foreground")){
                $banner["imageColor"]["foreground"]=$request->input("banner.imageColor.foreground");
            }
        }
        // $banner??=[];
        // $banner["banner"]["image"]=$request->input("banner.image");

        if($request->filled("os_types")){
            $item->os_types =$request->os_types;
        }else{
            $item->os_types =null;
        }
        $allowEdit =auth()->user()->hasRole('developer') || is_null($item->id);
        if($allowEdit){
            $item->tag = $request->tag;
            $item->type = $request->type=="item"?null:$request->type;
            $item->url = $request->url;
            $item->app_version = $request->app_version;
        }
        $item->icon = $request->icon;
        $item->title = $request->title;
        $item->description = $request->description;
        $item->banner = $banner;
        $item->help_url = $request->help_url;
        $item->service_id = $request->service_id;
        $item->trending_service = $request->trending_service??null;
        $item->loyalty_card_id = $request->loyalty_card_id??null;
        $item->quick_access = $request->quick_access;
        $item->allow_more = $request->allow_more;
        $item->sort = $request->sort;

        //return response()->json($item);

        $item->save();

        if(in_array($item->type,array_keys($this->types))){
            $item->service_id=null;
            Service::where('service_id',$item->id)
            ->whereNotIn('id',$request->services??[])
            ->update([
                "service_id"=>null
            ]);

            Service::where(function ($query) use ($item) {
                return $query->where('service_id', '<>',$item->id)
                    ->orWhereNull('service_id');
            })
            ->whereIn('id',$request->services??[])
            ->update([
                "service_id"=>$item->id
            ]);
        }else{
            Service::where('service_id',$item->id)
            ->update([
                "service_id"=>null
            ]);
            $item->service_id= $request->service_id;
        }

        if($allowEdit){
            ServiceCustomerType::where('service_id',$item->id)
            ->delete();
            ServiceCustomerRole::where('service_id',$item->id)
            ->delete();

            $service_pages=[];
            $roles=[];
            foreach($request->customer_services as $key=>$value){
                if($request->filled("customer_services.$key.customer_type_id")){
                    $service_pages[]=[
                        "service_id"=> $item->id,
                        "customer_type_id"=> $value['customer_type_id'],
                        "page_service_id"=> $value['page_service_id'],
                        "status"=> $value['status'],
                        "status_type"=> $value['status_type']??null,
                    ];
                    if(isset($value['roles'])){
                        foreach ($value['roles'] as $role) {
                            $roles[]=[
                                "service_id"=> $item->id,
                                "customer_type_id"=> $value['customer_type_id'],
                                "role_identifer"=> $role
                            ];
                        }

                    }
                }
            }
            if(!empty($service_pages)){
                ServiceCustomerType::insert($service_pages);
            }
            if(!empty($roles)){
                ServiceCustomerRole::insert($roles);
            }
        }

      //  return abort(501);
        if(isset($isNew)){
            return redirect("/admin/service/$item->id")
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['service.item.*','service.item.delete'])) return abort(401);

    }
}
