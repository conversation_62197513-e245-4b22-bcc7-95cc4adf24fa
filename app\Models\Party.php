<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Party extends Model
{
    use HasFactory;
    protected $fillable = [
        "id",
        "party_id",
        "username",
        "name",
        "image",
        'offline'
    ];

    public function friends()
    {
        return $this->hasMany(PartyFriend::class,'friend_id','id')
        ->select('id','name','image');
    }
}
