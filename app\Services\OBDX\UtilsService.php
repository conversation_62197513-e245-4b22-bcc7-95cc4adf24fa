<?php

namespace App\Services\OBDX;
use App\Data\AccountData;
use App\Data\Classes\BranchData;
use App\Data\GeneralResponseData;
use App\Data\TokenData;
use App\Models\AgentUser;
use App\Models\CustomerType;
use Illuminate\Auth\RequestGuard;
use Illuminate\Http\Request;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Facades\Http;
use App\Helpers\BaseHelper;

use App;
use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\OBDX\ExchangeRateData;
use App\Enums\CurrencyTypeEnum;
use App\Models\User;
use App\Services\FlexService;

/**
 * Service to create and update orders
 */
class UtilsService
{
    protected $url;

    protected $cookies;
    protected $headers;

    protected $retry=0;

    /**
     * Service to handle customer requestes
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function __construct(Request $request){
        $this->url="{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1";
        $this->cookies=CookieJar::fromArray($request->cookies->all(),"{$_ENV['OBDX_URL']}");
        $this->headers =  collect($request->headers->all())->except([
            'host',
            'cookie',
            'content-length',
            "user-agent",
            "postman-token",
            "content-type",
            "token",
            "x-original-url"
        ])->all();
        $this->headers+=["Accept-Encoding"=>"identity"];
    }
    /**
     * Register a stub callable that will intercept requests and be able to return stub responses.
     *
     * @param  string  $method
     * @param  string  $url
     * @param  callable|array $params
     * @param  callable|array $fake
     * @return $response
     */
    function getHttpRequest(string $method,string $url,array $params=[],array $fake=[]){
        if(!empty($fake)){
            $request=Http::fake($fake);
        }else{
            $request=Http::withOptions([
                'cookies' => $this->cookies,
            ]);
        }
        //$request=$request->withHeaders($this->headers);
        if($method=='GET' && !empty($params)){
            $params=[
                'query' => $params,
            ];
        }
        $response=$request->withHeaders($this->headers)->send( $method,"{$this->url}/$url", $params);
        return $response;

    }



    /**
     * Get user details info
     *
     * @param  CurrencyAmountData  $amount
     * @param  AccountIdData  $fromAccount
     * @param  AccountIdData  $toAccount
     * @return ?array $exchangeRate
     */
    protected function exchangeParams(AccountIdData $fromAccount,AccountIdData $toAccount)
    {
        if($this->retry==2){
            $this->retry=0;
            return null;
        }
        $this->retry++;
        $response=$this->getHttpRequest('GET','forex/currencyPairs',[
            "branchCode"=>$fromAccount->branchId(),
            "ccy1Code"=> $fromAccount->currencyId()
        ]);
        $result=$response->object();
        if(isset($result->exchangeRateCurrency)){
            $allowedCurrency=collect($result->exchangeRateCurrency)->where('ccy1',$fromAccount->currencyId())->first();
            if(!is_null($allowedCurrency)){
                if(in_array($toAccount->currencyId(),$allowedCurrency->ccy2)){
                    return [
                        "branchCode"=>$fromAccount->branchId(),
                        "ccy1Code"=> $fromAccount->currencyId(),
                        "ccy2Code"=> $toAccount->currencyId(),
                    ];
                }
            }
        }
        return $this->exchangeParams($toAccount,$fromAccount);
    }
    public static function branchId(AccountIdData $account,AccountIdData $fromAccount,AccountIdData $toAccount)
    {
        if((in_array($fromAccount->branchId()??"",BranchData::south()) && $fromAccount->currencyId()==CurrencyTypeEnum::YER->value) ||
             (in_array($toAccount->branchId()??"",BranchData::south()) && $toAccount->currencyId()==CurrencyTypeEnum::YER->value) ||
             ($fromAccount->currencyId()!=CurrencyTypeEnum::YER->value && $toAccount->currencyId()!=CurrencyTypeEnum::YER->value &&
                (in_array($fromAccount->branchId()??"",BranchData::south()) || in_array($toAccount->branchId()??"",BranchData::south())))
             ){
            return in_array($account->branchId()??"",BranchData::south())?$account->branchId():
            (
                in_array($fromAccount->branchId()??"",BranchData::south())?$fromAccount->branchId():
                (
                    in_array($toAccount->branchId()??"",BranchData::south())?$toAccount->branchId():$account->branchId()
                )
            );
        }
        return $account->branchId();

    }
     /**
     * Get user details info
     *
     * @param  CurrencyAmountData  $amount
     * @param  AccountIdData  $fromAccount
     * @param  AccountIdData  $toAccount
     * @return ?ExchangeRateData $exchangeRate
     */
    public static function exchange(CurrencyAmountData $amount,AccountIdData $fromAccount,AccountIdData $toAccount)
    {
        $service=new UtilsService(request()->instance());

        //if ($fromAccount->currencyId() != $toAccount->currencyId()) {

            $exchangeRate=new ExchangeRateData;

            $rate=0;
            if($fromAccount->currencyId()==CurrencyTypeEnum::YER->value){
                $rate=1;
            }else{
                $equlivent= $fromAccount->currencyId()==CurrencyTypeEnum::G24->value?
                    31.1:1;
                $response=$service->getHttpRequest('GET','forex/rates',[
                    "branchCode"=>static::branchId($fromAccount,$fromAccount,$toAccount),
                    "ccy1Code"=> $fromAccount->currencyId(),
                    "ccy2Code"=> CurrencyTypeEnum::YER->value,
                ]);
                $result=$response->object();
                if(!isset($result->exchangeRateDetails[0])){
                    return $exchangeRate;
                }
                $rate=$result->exchangeRateDetails[0]->buyRate*$equlivent;
            }

            $exchangeRate->debitRate=CurrencyAmountData::from([
                "amount"=>$rate,
                "currency"=>CurrencyTypeEnum::YER->value
            ]);

            $rate=0;
            if($toAccount->currencyId()==CurrencyTypeEnum::YER->value){
                $rate=1;
            }else{
                $equlivent= $toAccount->currencyId()==CurrencyTypeEnum::G24->value?
                31.1:1;
                $response=$service->getHttpRequest('GET','forex/rates',[
                    "branchCode"=>static::branchId($toAccount,$fromAccount,$toAccount),
                    "ccy1Code"=> $toAccount->currencyId(),
                    "ccy2Code"=> CurrencyTypeEnum::YER->value,
                ]);
                $result=$response->object();
                if(!isset($result->exchangeRateDetails[0])){
                    return $exchangeRate;
                }
                $rate=$result->exchangeRateDetails[0]->sellRate*$equlivent;
            }

            $exchangeRate->creditRate=CurrencyAmountData::from([
                "amount"=>$rate,
                "currency"=>CurrencyTypeEnum::YER->value
            ]);

            $exchangeRate->rate=CurrencyAmountData::from([
                "amount"=> round($exchangeRate->creditRate->amount/ $exchangeRate->debitRate->amount, 12),
                "currency"=>$fromAccount->currencyId()
            ]);

            // $different=$exchangeRate->creditRate->amount==1 || $exchangeRate->debitRate->amount==1?
            //     max([$exchangeRate->creditRate->amount,$exchangeRate->debitRate->amount]):
            //     abs($exchangeRate->creditRate->amount-$exchangeRate->debitRate->amount);
            // $exchangeRate->different=CurrencyAmountData::from([
            //     "amount"=>$different,
            //     "currency"=>$fromAccount->currencyId()
            // ]);

            $total=new CurrencyAmountData;
            $rateNet=new CurrencyAmountData;

            if($amount->currency==$toAccount->currencyId()){
                $total->amount=$amount->amount*$exchangeRate->rate->amount;
                $total->currency=$fromAccount->currencyId();

                $rateNet->amount=1*$exchangeRate->rate->amount;
                $rateNet->currency=$fromAccount->currencyId();

                $exchangeRate->limit=CurrencyAmountData::from([
                    "amount"=>$total->amount*$exchangeRate->debitRate->amount,
                    "currency"=>CurrencyTypeEnum::YER->value
                ]);
            }else{
                $total->amount=$amount->amount/$exchangeRate->rate->amount;
                $total->currency=$toAccount->currencyId();

                $rateNet->amount=1/$exchangeRate->rate->amount;
                $rateNet->currency=$toAccount->currencyId();

                $exchangeRate->limit=CurrencyAmountData::from([
                    "amount"=>$total->amount*$exchangeRate->creditRate->amount,
                    "currency"=>CurrencyTypeEnum::YER->value
                ]);
            }

            $exchangeRate->total=$total;
            $exchangeRate->rateNet=$rateNet;

            return $exchangeRate;
        // }else{
        //     return ExchangeRateData::from([
        //         "rate"=>[
        //             "amount"=>1.0,
        //             "currency"=>$amount->currency
        //         ],
        //         "total"=>$amount
        //     ]);;
        // }

    }

}
