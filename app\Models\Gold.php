<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
class Gold extends Model
{
    use HasFactory;
    protected $table = 'golds';

    protected $fillable = ['party_id','status','limit_type','debit_account_id','credit_account_id','fee_account_id','amount','fee','exchange_rate','remarks','manual_type'];
    protected $casts = [
        'credit_account_id' => 'object',
        'debit_account_id'  => 'object',
        'fee_account_id'    => 'object',
        'amount'            => 'object',
        'fee'               => 'object',
        'exchange_rate'     => 'object',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            $query->party_id = auth()->user()->id;
        });
    }

    protected function serializeDate(DateTimeInterface $date){
        return $date->timezone('Asia/Aden')->format('Y-m-d H:i:s');
    }
    public function transactions()
    {
        return $this->hasMany('App\Models\GoldTransaction', 'gold_id');
    }

    public function transaction()
    {
        return $this->hasOne('App\Models\GoldTransaction', 'gold_id');
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', 'model_id')->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}
