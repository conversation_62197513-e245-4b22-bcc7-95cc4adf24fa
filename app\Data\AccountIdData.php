<?php

namespace App\Data;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class AccountIdData extends Data
{
    #[Nullable]
    public ?string $displayValue;
    #[Required,Min(20),Max(20)]
    public string $value;
    public function userName():?string {
        return substr($this->value,4,6);
    }
    public function partyId():?string {
        return substr($this->value,3,7);
    }
    public function currencyId():?string {
        return substr($this->value,10, 3);
    }
    public function currencyName():?string {
        return __($this->currencyId());
    }
    public function currencyFullName():?string {
        return __($this->currencyId()."-Full");
    }
    public function branchId():?string {
        return substr($this->value,0,3);
    }

    public function branchName():?string {
        return __($this->branchId()."-Full");
    }
    public function productId():?string {
        return substr($this->value,13, 4);
    }
    public function productName():?string {
        return __($this->productId());
    }

    public function fullName():?string {
        return $this->branchName()." ".$this->productName();
    }

    public function isGold():bool {
        return in_array($this->productId(),ProductData::gold());
    }

    public function isNorth():bool {
        return in_array($this->branchId(),BranchData::north());
    }
    public function allowedService($code):bool {
        return in_array($code,$this->availableServices());
    }

    // protected function isAllowCashAccountClass():bool {
    //     return in_array($this->productId(),ProductData::cash()) ||
    //     (in_array($this->productId(),ProductData::cashNoneYER()) && $this->currencyId()!="YER");
    // }
    // public function isCash():bool {
    //     return $this->isAllowCashAccountClass();
    // }
    protected function isAllowCashAccountClassOut():bool {
        return in_array($this->productId(),ProductData::cashOut()) ||
        (in_array($this->productId(),ProductData::cashOutNoneYER()) && $this->currencyId()!="YER");
    }
    public function isCashOut():bool {
        return $this->isAllowCashAccountClassOut();
    }
    public function availableServices():array {
        $items=app(\App\Settings\ConfigSettings::class)->accountConfig;
        $items=collect($items)->filter(function ($element,string $key) {
            return in_array($this->branchId(),$element['branches']) &&
            in_array($this->productId(),$element['products']) &&
            in_array($this->currencyId(),$element['currencies']);
        })->keys()->toArray();
        // $ii=new AccountConfigData;
        // $ii->
        return $items;
    }
}
