<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cardless extends Model
{
   use HasFactory;
   /**
    * The list of hidden request headers.
    *
    * @var array
    */
   public static $hiddenParameters = [
      'reference_id',
      'otp',
   ];
   protected $casts = [
      'data' => 'object',
      'created_at' => 'datetime:Y-m-d H:i:s',
      'updated_at' => 'datetime:Y-m-d H:i:s',
   ];
   protected $hidden = [
      'rn'
   ];

    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }

    public const fakeMe=[
        "status" => [
              "result" => "SUCCESSFUL",
              "contextID" => "005vGt9MG5O5uXS6yF2jMG0003NK003KPN,0&#x3a;1",
              "message" => [
                 "code" => "0",
                 "type" => "Test"
              ]
           ],
        "userProfile" => [
                    "userName" => "183415",
                    "firstName" => "&#x645;&#x62d;&#x645;&#x62f;",
                    "middleName" => "&#x641;&#x64a;&#x635;&#x644; &#x639;&#x628;&#x62f; &#x627;&#x644;&#x633;&#x644;&#x627;&#x645; ",
                    "lastName" => "&#x642;&#x645;&#x62d;&#x627;&#x646;",
                    "partyId" => [
                       "displayValue" => "***3415",
                       "value" => "0183415"
                    ],
                    "roles" => [
                          "Customer",
                          "RetailUser",
                          "Member"
                       ],
                    "lastLoginTime" => "2022-11-09T22:02:02.000+03:00",
                    "preLastLoggedInDateTime" => "2022-11-09T20:46:05.000+03:00",
                    "emailId" => [
                             "displayValue" => "eng****gmail.com",
                             "value" => "<EMAIL>"
                          ],
                    "phoneNumber" => [
                                "displayValue" => "7715****9",
                                "value" => "*********"
                             ],
                    "forceChangePassword" => false,
                    "pwdExpiryWarningDays" => 0,
                    "dateOfBirth" => "1988-01-01T00:00:00",
                    "address" => [
                                   "line1" => "&#x627;&#x644;&#x62f;&#x627;&#x626;&#x631;&#x64a;",
                                   "line2" => "&#x62c;&#x648;&#x627;&#x631; &#x633;&#x64a;&#x62a;&#x64a; &#x645;&#x627;&#x631;&#x62a;",
                                   "line3" => "&#x645;&#x639;&#x64a;&#x646;",
                                   "line4" => "&#x627;&#x645;&#x627;&#x646;&#x629; &#x627;&#x644;&#x639;&#x627;&#x635;&#x645;&#x647;",
                                   "city" => "&#x627;&#x645;&#x627;&#x646;&#x629; &#x627;&#x644;&#x639;&#x627;&#x635;&#x645;&#x647;",
                                   "state" => "&#x62c;&#x648;&#x627;&#x631; &#x633;&#x64a;&#x62a;&#x64a; &#x645;&#x627;&#x631;&#x62a;",
                                   "country" => "YE",
                                   "zipCode" => "01010128237"
                                ],
                    "homeEntity" => "OBDX_BU",
                    "accessibleEntities" => [
                                      "OBDX_BU"
                                   ],
                    "timeZoneDTO" => [
                                         "id" => "Asia&#x2f;Aden",
                                         "offset" => -180
                                      ],
                    "accessibleEntityDTOs" => [
                                            [
                                               "entityId" => "OBDX_BU",
                                               "entityName" => "Default Business Unit",
                                               "userPartyRelationship" => [
                                                  "id" => "30600",
                                                  "userId" => "183415",
                                                  "partyId" => [
                                                     "displayValue" => "***3415",
                                                     "value" => "0183415"
                                                  ],
                                                  "relationshipType" => "I",
                                                  "determinantValue" => "OBDX_BU"
                                               ],
                                               "partyName" => "&#x645;&#x62d;&#x645;&#x62f; &#x641;&#x64a;&#x635;&#x644; &#x639;&#x628;&#x62f; &#x627;&#x644;&#x633;&#x644;&#x627;&#x645; &#x642;&#x645;&#x62d;&#x627;&#x646;"
                                            ]
                                         ]
                 ],
        "firstLoginFlowDone" => true
    ];
    public const fakeMeError=[
        "result" => "SUCCESSFUL",
        "contextID" => "005vHqYnuOH5uXS6yF2jMG0003NK002dhW,0&#x3a;1",
        "message" => [
              "title" => "System cannot process the request currently. Please try later.",
              "detail" => "fakeMeError",
              "code" => "DIGX_PROD_DEF_0000",
              "relatedMessage" => [
                 [
                    "detail" => "Access denied.",
                    "code" => "FC_SM_025"
                 ]
              ],
              "type" => "ERROR"
           ]
     ];

    public const fakeSMS = [
        "ResultCode" => "1",
        "ResultDesc" => '<HTML>
        <HEAD><TITLE>Message Submitted</TITLE></HEAD>
        <BODY>
        <p>
        Message Submitted
        <p>
        <a href="javascript:history.back()">Continue</a>
        <p>
        <pre><small>
        MessageID=632D0EA1.req, Recipient=711271129
        </small></pre>
        <p>
        </BODY>
        </HTML>
        ',
        "HttpStatus" => 200,
        "Success" => true,
        "Balance" => 0,
        "SuccessCount" => 0,
        "FailedCount" => 0
        ];

    public const fakePaymentDetails = [
        "status" => [
              "result" => "SUCCESSFUL",
              "contextID" => "005vGxwD8mE5uXS6yF2jMG0003NK000vBm,0&#x3a;1",
              "message" => [
                 "code" => "0",
                 "type" => "INFO"
              ]
           ],
        "paymentId" => "81KIG22I3F",
        "transferDetails" => [
                    "partyId" => [
                       "displayValue" => "***3415",
                       "value" => "0183415"
                    ],
                    "amount" => [
                          "currency" => "YER",
                          "amount" => 1000
                       ],
                    "valueDate" => "2022-11-10T00:00:00",
                    "debitAccountId" => [
                             "displayValue" => "xxxxxxxxxxxxS018",
                             "value" => "4020183415YERCUCS018"
                          ],
                    "status" => "INT",
                    "billerId" => "YEMENMOBILE",
                    "billNumber" => "*********",
                    "billDate" => "2022-11-10T00:00:00",
                    "relationshipNumber" => "*********",
                    "adhocPayment" => false
                 ]
     ];
}
