<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use App\Enums\HarvestStatusEnum;
use DateTimeInterface;
use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Str;

class Harvest extends Model
{
    use HasFactory;
    /**
    * The list of hidden request headers.
    *
    * @var array
    */
    public static $hiddenParameters = [
        'token'
    ];
    protected $dates = ['created_at','updated_at'];

    protected $casts = [
        'data' => 'object',
        'received_data' => 'object',
        'amount' => 'object',
        'credit_account_id' => 'object',
        'sender_info' => 'object',
        'receiver_info' => 'object',
        'is_manual' => 'boolean',
        'manual_started_at' => 'datetime',
        'manual_completed_at' => 'datetime',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $fillable = [
        'party_id',
        'service_code_id',
        'request_id',
        'data',
        'amount',
        'token',
        'sender_info',
        'receiver_info',
        'received_data',
        'status',
        'is_manual',
        'manual_stage',
        'manual_notes',
        'rejection_reason',
        'processed_by',
        'manual_started_at',
        'manual_completed_at'
    ];
    protected $hidden = [
        'rn'
    ];


    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            $query->party_id = auth()->user()->id;
        });
    }
    protected function serializeDate(DateTimeInterface $date){
        return $date->timezone('Asia/Aden')->format('Y-m-d H:i:s');
    }
    public function service()
    {
        return $this->belongsTo('App\Models\HarvestServiceCode',"service_code_id");
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
    public const branchMapping = [
        "401" => "YKBHO",
        "402" => "YKBSATA01",
        "403" => "YKBADCR01",
        "404" => "YKBHUMI01",
        "405" => "YKBTASL01",
        "406" => "YKBHDMU01",
        "407" => "YKBSASA01",
        "408" => "YKBIBDH01",
        "409" => "YKBSAMA01",
        "410" => "YKBSABH01",
        "411" => "YKBSASH01",
        "412" => "YKBTATA01",
        "413" => "YKBNASN01",
        "414" => "YKBADSO01",
        "415" => "YKBSASA02",
        "416" => "YKBSAWA01",
        "417" => "YKBSABH02",
        "418" => "YKBDHDH01",
        "419" => "YKBSASA03",
        "420" => "YKBAMAM01",
        "421" => "YKBHJHC01",
        "422" => "YKBHUBF01",
        "423" => "YKBDHDH02",
        "424" => "YKBDHDH03",
        "425" => "YKBADBU01",
        "426" => "YKBTAMK01",

        "502" => "YKBSAWA02",
        "504" => "YKBSASA04",
        "505" => "YKBSDSD01",
        "506" => "YKBMAMC01",

    ];
    public const list = [
        [
            "Status" => "Paid",
            "firstName" => "علي",
            "thirdName" => "بحيري",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601784426",
            "Party Utn" => "601784426",
            "lastName" => "جنان",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي سعيد بحيري جنان",
            "Utn Frmtd" => "YM601784426",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Paid",
            "firstName" => "بدري",
            "thirdName" => "يحي",
            "secondName" => "قايد",
            "Rcvr Frth Name" => "غالب",
            "senderPhone" => "1",
            "Utn" => "501781685",
            "Party Utn" => "501781685",
            "lastName" => "الشميري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "بدري قايد يحي غالب الشميري",
            "Utn Frmtd" => "YM501781685",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "نادر",
            "thirdName" => "احمد",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901757369",
            "Party Utn" => "901757369",
            "lastName" => "حسين",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "نادر محمد احمد حسين",
            "Utn Frmtd" => "YM901757369",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "حنان",
            "thirdName" => "محمد",
            "secondName" => "اسماعيل",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401707304",
            "Party Utn" => "401707304",
            "lastName" => "عبدالله",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حنان اسماعيل محمد عبدالله",
            "Utn Frmtd" => "YM401707304",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "يحيى",
            "secondName" => "مقبول",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201721612",
            "Party Utn" => "201721612",
            "lastName" => "شريفي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده مقبول يحيى شريفي",
            "Utn Frmtd" => "YM201721612",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "حسن",
            "thirdName" => "عبدالله",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101794781",
            "Party Utn" => "101794781",
            "lastName" => "معصلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حسن محمد عبدالله معصلي",
            "Utn Frmtd" => "YM101794781",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "أمير",
            "thirdName" => "حسان",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "أحمد",
            "senderPhone" => "1",
            "Utn" => "901762729",
            "Party Utn" => "901762729",
            "lastName" => "حساني",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "أمير سعيد حسان أحمد حساني",
            "Utn Frmtd" => "YM901762729",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "سالم",
            "secondName" => "يحيى",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701723497",
            "Party Utn" => "701723497",
            "lastName" => "يأكل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد يحيى سالم يأكل",
            "Utn Frmtd" => "YM701723497",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبد الرحمن",
            "thirdName" => "عمر",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201753672",
            "Party Utn" => "201753672",
            "lastName" => "البشيري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبد الرحمن محمد عمر البشيري",
            "Utn Frmtd" => "YM201753672",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "صالح",
            "secondName" => "ابراهيم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901709670",
            "Party Utn" => "901709670",
            "lastName" => "دحفش",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد ابراهيم صالح دحفش",
            "Utn Frmtd" => "YM901709670",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "خالد",
            "thirdName" => "سالم",
            "secondName" => "عوض",
            "Rcvr Frth Name" => "قايد",
            "senderPhone" => "1",
            "Utn" => "101704041",
            "Party Utn" => "101704041",
            "lastName" => "طليلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "خالد عوض سالم قايد طليلي",
            "Utn Frmtd" => "YM101704041",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سهام",
            "thirdName" => "عبد الله",
            "secondName" => "علي",
            "Rcvr Frth Name" => "حيدرة",
            "senderPhone" => "1",
            "Utn" => "901774550",
            "Party Utn" => "901774550",
            "lastName" => "الميسري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سهام علي عبد الله حيدرة الميسري",
            "Utn Frmtd" => "YM901774550",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عرفات",
            "thirdName" => "عبدالله",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701782437",
            "Party Utn" => "701782437",
            "lastName" => "الاهدل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عرفات محمد عبدالله الاهدل",
            "Utn Frmtd" => "YM701782437",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "إبراهيم",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "سعيد",
            "senderPhone" => "1",
            "Utn" => "801760558",
            "Party Utn" => "801760558",
            "lastName" => "ملحطي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد محمد إبراهيم سعيد ملحطي",
            "Utn Frmtd" => "YM801760558",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "إبراهيم",
            "thirdName" => "علي",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "محمد",
            "senderPhone" => "1",
            "Utn" => "401793314",
            "Party Utn" => "401793314",
            "lastName" => "عدار",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إبراهيم حسن علي محمد عدار",
            "Utn Frmtd" => "YM401793314",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "يعقوب",
            "thirdName" => "عبده",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801744738",
            "Party Utn" => "801744738",
            "lastName" => "واقدي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "يعقوب حسن عبده واقدي",
            "Utn Frmtd" => "YM801744738",
            "Payin Amount" => "88.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "فتيني",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301771213",
            "Party Utn" => "301771213",
            "lastName" => "عليان",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد محمد فتيني عليان",
            "Utn Frmtd" => "YM301771213",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "إبراهيم",
            "thirdName" => "علي",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601796206",
            "Party Utn" => "601796206",
            "lastName" => "الاهدل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إبراهيم محمد علي الاهدل",
            "Utn Frmtd" => "YM601796206",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالله",
            "thirdName" => "احمد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401762874",
            "Party Utn" => "401762874",
            "lastName" => "خضيري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله علي احمد خضيري",
            "Utn Frmtd" => "YM401762874",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبد المعين",
            "thirdName" => "احمد",
            "secondName" => "يحيى",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201764652",
            "Party Utn" => "201764652",
            "lastName" => "الأهدل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبد المعين يحيى احمد الأهدل",
            "Utn Frmtd" => "YM201764652",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "بكيل",
            "thirdName" => "",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701718207",
            "Party Utn" => "701718207",
            "lastName" => "يحيى",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "بكيل علي يحيى",
            "Utn Frmtd" => "YM701718207",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "حاشد",
            "thirdName" => "عبدالرحمن",
            "secondName" => "هاشم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901756869",
            "Party Utn" => "901756869",
            "lastName" => "النهاري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حاشد هاشم عبدالرحمن النهاري",
            "Utn Frmtd" => "YM901756869",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "ناجي",
            "secondName" => "احمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201774852",
            "Party Utn" => "201774852",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد احمد ناجي احمد",
            "Utn Frmtd" => "YM201774852",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Suspended",
            "firstName" => "منى",
            "thirdName" => "علي",
            "secondName" => "فتيني",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801797868",
            "Party Utn" => "801797868",
            "lastName" => "هارون",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "منى فتيني علي هارون",
            "Utn Frmtd" => "YM801797868",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سالم",
            "thirdName" => "عوض",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101765951",
            "Party Utn" => "101765951",
            "lastName" => "عسال",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سالم علي عوض عسال",
            "Utn Frmtd" => "YM101765951",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "محمد",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "قايد",
            "senderPhone" => "1",
            "Utn" => "901718329",
            "Party Utn" => "901718329",
            "lastName" => "يحيى",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد سعيد محمد قايد يحيى",
            "Utn Frmtd" => "YM901718329",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "فتحية",
            "thirdName" => "عمر",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801797128",
            "Party Utn" => "801797128",
            "lastName" => "زهري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "فتحية محمد عمر زهري",
            "Utn Frmtd" => "YM801797128",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "وليد",
            "thirdName" => "حسن",
            "secondName" => "عبد الرقيب",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201758062",
            "Party Utn" => "201758062",
            "lastName" => "البحري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "وليد عبد الرقيب حسن البحري",
            "Utn Frmtd" => "YM201758062",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "محمد",
            "secondName" => "صالح",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801717348",
            "Party Utn" => "801717348",
            "lastName" => "عنتري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي صالح محمد عنتري",
            "Utn Frmtd" => "YM801717348",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "نعمان",
            "thirdName" => "احمد",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "راجح",
            "senderPhone" => "1",
            "Utn" => "601723716",
            "Party Utn" => "601723716",
            "lastName" => "هبيت",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "نعمان سعيد احمد راجح هبيت",
            "Utn Frmtd" => "YM601723716",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "نبيل",
            "thirdName" => "محمد",
            "secondName" => "سليمان",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101751681",
            "Party Utn" => "101751681",
            "lastName" => "مرزوق",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "نبيل سليمان محمد مرزوق",
            "Utn Frmtd" => "YM101751681",
            "Payin Amount" => "89.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "تقية",
            "thirdName" => "إبراهيم",
            "secondName" => "يحيى",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601717296",
            "Party Utn" => "601717296",
            "lastName" => "رزة",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "تقية يحيى إبراهيم رزة",
            "Utn Frmtd" => "YM601717296",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "إبراهيم",
            "thirdName" => "يحي",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "عبده",
            "senderPhone" => "1",
            "Utn" => "201701032",
            "Party Utn" => "201701032",
            "lastName" => "دعسين",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إبراهيم محمد يحي عبده دعسين",
            "Utn Frmtd" => "YM201701032",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعيد",
            "thirdName" => "محمد",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901797700",
            "Party Utn" => "901797700",
            "lastName" => "الاهدل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعيد حسن محمد الاهدل",
            "Utn Frmtd" => "YM901797700",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "اماني",
            "thirdName" => "ربوعي",
            "secondName" => "",
            "Rcvr Frth Name" => "عييد",
            "senderPhone" => "1",
            "Utn" => "201725562",
            "Party Utn" => "201725562",
            "lastName" => "حسن",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "اماني ربوعي عييد حسن",
            "Utn Frmtd" => "YM201725562",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عصام",
            "thirdName" => "خالد",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301756653",
            "Party Utn" => "301756653",
            "lastName" => "حايك",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عصام محمد خالد حايك",
            "Utn Frmtd" => "YM301756653",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "ابكر",
            "thirdName" => "نهاري",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901784479",
            "Party Utn" => "901784479",
            "lastName" => "درويش",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ابكر محمد نهاري درويش",
            "Utn Frmtd" => "YM901784479",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "هشام",
            "thirdName" => "إبراهيم",
            "secondName" => "يحي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901759239",
            "Party Utn" => "901759239",
            "lastName" => "سندي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "هشام يحي إبراهيم سندي",
            "Utn Frmtd" => "YM901759239",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "رضا",
            "thirdName" => "عمر",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701771027",
            "Party Utn" => "701771027",
            "lastName" => "سيد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "رضا محمد عمر سيد",
            "Utn Frmtd" => "YM701771027",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "منى",
            "thirdName" => "عبدالله",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901712200",
            "Party Utn" => "901712200",
            "lastName" => "الريمي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "منى علي عبدالله الريمي",
            "Utn Frmtd" => "YM901712200",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "احمد",
            "thirdName" => "زياد",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201747892",
            "Party Utn" => "201747892",
            "lastName" => "عليلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "احمد محمد زياد عليلي",
            "Utn Frmtd" => "YM201747892",
            "Payin Amount" => "125.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محسن",
            "thirdName" => "علي",
            "secondName" => "قائد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101779511",
            "Party Utn" => "101779511",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محسن قائد علي احمد",
            "Utn Frmtd" => "YM101779511",
            "Payin Amount" => "90.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "تهاني",
            "thirdName" => "سالم",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201737362",
            "Party Utn" => "201737362",
            "lastName" => "قباصة",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "تهاني علي سالم قباصة",
            "Utn Frmtd" => "YM201737362",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالعزيز",
            "thirdName" => "فخري",
            "secondName" => "هادي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501702205",
            "Party Utn" => "501702205",
            "lastName" => "راسم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالعزيز هادي فخري راسم",
            "Utn Frmtd" => "YM501702205",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "ايهم",
            "thirdName" => "سعيد",
            "secondName" => "عبدالله",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201778362",
            "Party Utn" => "201778362",
            "lastName" => "معروف",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ايهم عبدالله سعيد معروف",
            "Utn Frmtd" => "YM201778362",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "إبراهيم",
            "thirdName" => "قايد",
            "secondName" => "ثابت",
            "Rcvr Frth Name" => "احمد",
            "senderPhone" => "1",
            "Utn" => "401723304",
            "Party Utn" => "401723304",
            "lastName" => "علي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إبراهيم ثابت قايد احمد علي",
            "Utn Frmtd" => "YM401723304",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Suspended",
            "firstName" => "نسيبة",
            "thirdName" => "عمر",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601768896",
            "Party Utn" => "601768896",
            "lastName" => "حميدي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "نسيبة محمد عمر حميدي",
            "Utn Frmtd" => "YM601768896",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "عبده",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301728143",
            "Party Utn" => "301728143",
            "lastName" => "واقدي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد حسن عبده واقدي",
            "Utn Frmtd" => "YM301728143",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "جيهان",
            "thirdName" => "امين",
            "secondName" => "احمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301775453",
            "Party Utn" => "301775453",
            "lastName" => "ابوبكر",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "جيهان احمد امين ابوبكر",
            "Utn Frmtd" => "YM301775453",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "خالد",
            "thirdName" => "خالد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901774149",
            "Party Utn" => "901774149",
            "lastName" => "رافع",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "خالد علي خالد رافع",
            "Utn Frmtd" => "YM901774149",
            "Payin Amount" => "89.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "سمية",
            "thirdName" => "محمد",
            "secondName" => "عبدالله",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701754607",
            "Party Utn" => "701754607",
            "lastName" => "معصلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سمية عبدالله محمد معصلي",
            "Utn Frmtd" => "YM701754607",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "جابر",
            "thirdName" => "يحيى",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701775077",
            "Party Utn" => "701775077",
            "lastName" => "أبكر",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "جابر حسن يحيى أبكر",
            "Utn Frmtd" => "YM701775077",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "رانيا",
            "thirdName" => "علي",
            "secondName" => "يحي",
            "Rcvr Frth Name" => "ابكر",
            "senderPhone" => "1",
            "Utn" => "701742777",
            "Party Utn" => "701742777",
            "lastName" => "زرنوقي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "رانيا يحي علي ابكر زرنوقي",
            "Utn Frmtd" => "YM701742777",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "عبدالله",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401759004",
            "Party Utn" => "401759004",
            "lastName" => "مهدلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده محمد عبدالله مهدلي",
            "Utn Frmtd" => "YM401759004",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سميرة",
            "thirdName" => "ثابت",
            "secondName" => "على الله",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301774273",
            "Party Utn" => "301774273",
            "lastName" => "دبع",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سميرة على الله ثابت دبع",
            "Utn Frmtd" => "YM301774273",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "خليل",
            "thirdName" => "محمد",
            "secondName" => "عبدالحميد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901736140",
            "Party Utn" => "901736140",
            "lastName" => "عبدالله",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "خليل عبدالحميد محمد عبدالله",
            "Utn Frmtd" => "YM901736140",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "حامد",
            "thirdName" => "عبدالله",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301728193",
            "Party Utn" => "301728193",
            "lastName" => "عبدالسلام",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حامد علي عبدالله عبدالسلام",
            "Utn Frmtd" => "YM301728193",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "ناجي",
            "thirdName" => "حزام",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501705475",
            "Party Utn" => "501705475",
            "lastName" => "ناجي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ناجي محمد حزام ناجي",
            "Utn Frmtd" => "YM501705475",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "اشجان",
            "thirdName" => "حسين",
            "secondName" => "علي",
            "Rcvr Frth Name" => "إبراهيم",
            "senderPhone" => "1",
            "Utn" => "801741908",
            "Party Utn" => "801741908",
            "lastName" => "محب",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "اشجان علي حسين إبراهيم محب",
            "Utn Frmtd" => "YM801741908",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عمر",
            "thirdName" => "فتيني",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301702473",
            "Party Utn" => "301702473",
            "lastName" => "حضرمي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عمر حسن فتيني حضرمي",
            "Utn Frmtd" => "YM301702473",
            "Payin Amount" => "125.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "يحي",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601799386",
            "Party Utn" => "601799386",
            "lastName" => "عفشة",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد حسن يحي عفشة",
            "Utn Frmtd" => "YM601799386",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "خادم",
            "thirdName" => "احمد",
            "secondName" => "حسين",
            "Rcvr Frth Name" => "سالم",
            "senderPhone" => "1",
            "Utn" => "101721881",
            "Party Utn" => "101721881",
            "lastName" => "مطيلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "خادم حسين احمد سالم مطيلي",
            "Utn Frmtd" => "YM101721881",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالمجيد",
            "thirdName" => "دبج",
            "secondName" => "أحمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301741553",
            "Party Utn" => "301741553",
            "lastName" => "سالم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالمجيد أحمد دبج سالم",
            "Utn Frmtd" => "YM301741553",
            "Payin Amount" => "69.3",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "زيد",
            "secondName" => "سليمان",
            "Rcvr Frth Name" => "بن",
            "senderPhone" => "1",
            "Utn" => "901724310",
            "Party Utn" => "901724310",
            "lastName" => "زيد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده سليمان زيد بن زيد",
            "Utn Frmtd" => "YM901724310",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبيد",
            "thirdName" => "علي",
            "secondName" => "عبيد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801768318",
            "Party Utn" => "801768318",
            "lastName" => "حنجلة",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبيد عبيد علي حنجلة",
            "Utn Frmtd" => "YM801768318",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعيد",
            "thirdName" => "سليمان",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801752318",
            "Party Utn" => "801752318",
            "lastName" => "زيد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعيد عبده سليمان زيد",
            "Utn Frmtd" => "YM801752318",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "عبده",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701787127",
            "Party Utn" => "701787127",
            "lastName" => "واقدي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده حسن عبده واقدي",
            "Utn Frmtd" => "YM701787127",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "شاكر",
            "thirdName" => "يحيى",
            "secondName" => "حامد",
            "Rcvr Frth Name" => "علي",
            "senderPhone" => "1",
            "Utn" => "201752642",
            "Party Utn" => "201752642",
            "lastName" => "غري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "شاكر حامد يحيى علي غري",
            "Utn Frmtd" => "YM201752642",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعيد",
            "thirdName" => "حسين",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701777927",
            "Party Utn" => "701777927",
            "lastName" => "كنزل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعيد علي حسين كنزل",
            "Utn Frmtd" => "YM701777927",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمود",
            "thirdName" => "احمد",
            "secondName" => "إبراهيم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101779831",
            "Party Utn" => "101779831",
            "lastName" => "ابكر",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمود إبراهيم احمد ابكر",
            "Utn Frmtd" => "YM101779831",
            "Payin Amount" => "139.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "حسن",
            "thirdName" => "عبدالله",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901711589",
            "Party Utn" => "901711589",
            "lastName" => "الأهدل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حسن محمد عبدالله الأهدل",
            "Utn Frmtd" => "YM901711589",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "عبده",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301735183",
            "Party Utn" => "301735183",
            "lastName" => "بو",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي محمد عبده بو",
            "Utn Frmtd" => "YM301735183",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "علي",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701778137",
            "Party Utn" => "701778137",
            "lastName" => "منصوب",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده حسن علي منصوب",
            "Utn Frmtd" => "YM701778137",
            "Payin Amount" => "119.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عميد",
            "thirdName" => "علي",
            "secondName" => "السلال",
            "Rcvr Frth Name" => "ناجي",
            "senderPhone" => "1",
            "Utn" => "301707663",
            "Party Utn" => "301707663",
            "lastName" => "العاتي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عميد السلال علي ناجي العاتي",
            "Utn Frmtd" => "YM301707663",
            "Payin Amount" => "210.55",
        ],
        [
            "Status" => "Available",
            "firstName" => "عاصم",
            "thirdName" => "زغبري",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601729156",
            "Party Utn" => "601729156",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عاصم محمد زغبري احمد",
            "Utn Frmtd" => "YM601729156",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعيد",
            "thirdName" => "عوض",
            "secondName" => "على",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301798313",
            "Party Utn" => "301798313",
            "lastName" => "طليلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعيد على عوض طليلي",
            "Utn Frmtd" => "YM301798313",
            "Payin Amount" => "239.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "غالب",
            "secondName" => "دغيش",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701703767",
            "Party Utn" => "701703767",
            "lastName" => "طليلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي دغيش غالب طليلي",
            "Utn Frmtd" => "YM701703767",
            "Payin Amount" => "52.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "يحيى",
            "thirdName" => "احمد",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401781804",
            "Party Utn" => "401781804",
            "lastName" => "غلاب",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "يحيى محمد احمد غلاب",
            "Utn Frmtd" => "YM401781804",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالرحمن",
            "thirdName" => "عبده",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201748502",
            "Party Utn" => "201748502",
            "lastName" => "بو",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالرحمن محمد عبده بو",
            "Utn Frmtd" => "YM201748502",
            "Payin Amount" => "299.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "نصر الله",
            "thirdName" => "سعد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801774388",
            "Party Utn" => "801774388",
            "lastName" => "طاهر",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "نصر الله علي سعد طاهر",
            "Utn Frmtd" => "YM801774388",
            "Payin Amount" => "559.3",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالكريم",
            "thirdName" => "عبده",
            "secondName" => "سعد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101736161",
            "Party Utn" => "101736161",
            "lastName" => "طليلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالكريم سعد عبده طليلي",
            "Utn Frmtd" => "YM101736161",
            "Payin Amount" => "51.7",
        ],
        [
            "Status" => "Canceled",
            "firstName" => "عبدالله",
            "thirdName" => "علي",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201740972",
            "Party Utn" => "201740972",
            "lastName" => "عماري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله عبده علي عماري",
            "Utn Frmtd" => "YM201740972",
            "Payin Amount" => "101.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "إسماعيل",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201731452",
            "Party Utn" => "201731452",
            "lastName" => "بادي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي عبده إسماعيل بادي",
            "Utn Frmtd" => "YM201731452",
            "Payin Amount" => "209.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "حسين",
            "secondName" => "عوض",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701798177",
            "Party Utn" => "701798177",
            "lastName" => "بحري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد عوض حسين بحري",
            "Utn Frmtd" => "YM701798177",
            "Payin Amount" => "569.3",
        ],
        [
            "Status" => "Canceled",
            "firstName" => "حسن",
            "thirdName" => "عمر",
            "secondName" => "قايد",
            "Rcvr Frth Name" => "عبدالله",
            "senderPhone" => "1",
            "Utn" => "201725772",
            "Party Utn" => "201725772",
            "lastName" => "علية",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حسن قايد عمر عبدالله علية",
            "Utn Frmtd" => "YM201725772",
            "Payin Amount" => "51.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "اكرم",
            "thirdName" => "محمد",
            "secondName" => "إبراهيم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201757892",
            "Party Utn" => "201757892",
            "lastName" => "مشهور",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "اكرم إبراهيم محمد مشهور",
            "Utn Frmtd" => "YM201757892",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "محمد",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801788418",
            "Party Utn" => "801788418",
            "lastName" => "عسيلو",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد حسن محمد عسيلو",
            "Utn Frmtd" => "YM801788418",
            "Payin Amount" => "299.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "عمر",
            "thirdName" => "عبدالله",
            "secondName" => "خالد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301703363",
            "Party Utn" => "301703363",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عمر خالد عبدالله احمد",
            "Utn Frmtd" => "YM301703363",
            "Payin Amount" => "69.3",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "سالم",
            "secondName" => "غالب",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801739028",
            "Party Utn" => "801739028",
            "lastName" => "عبدالله",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد غالب سالم عبدالله",
            "Utn Frmtd" => "YM801739028",
            "Payin Amount" => "51.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "شوقي",
            "thirdName" => "داود",
            "secondName" => "احمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301764173",
            "Party Utn" => "301764173",
            "lastName" => "جلعوم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "شوقي احمد داود جلعوم",
            "Utn Frmtd" => "YM301764173",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعيد",
            "thirdName" => "سعيد",
            "secondName" => "عبدالله",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801765798",
            "Party Utn" => "801765798",
            "lastName" => "معروف",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعيد عبدالله سعيد معروف",
            "Utn Frmtd" => "YM801765798",
            "Payin Amount" => "239.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "داود",
            "thirdName" => "احمد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501711885",
            "Party Utn" => "501711885",
            "lastName" => "مغرسي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "داود علي احمد مغرسي",
            "Utn Frmtd" => "YM501711885",
            "Payin Amount" => "489.3",
        ],
        [
            "Status" => "Available",
            "firstName" => "حسن",
            "thirdName" => "عبدالله",
            "secondName" => "ثابت",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501769365",
            "Party Utn" => "501769365",
            "lastName" => "علي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حسن ثابت عبدالله علي",
            "Utn Frmtd" => "YM501769365",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "خليل",
            "thirdName" => "أحمد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "حميد",
            "senderPhone" => "1",
            "Utn" => "701762217",
            "Party Utn" => "701762217",
            "lastName" => "كنزل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "خليل علي أحمد حميد كنزل",
            "Utn Frmtd" => "YM701762217",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Canceled",
            "firstName" => "عمر",
            "thirdName" => "عبده",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301712183",
            "Party Utn" => "301712183",
            "lastName" => "واقدي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عمر حسن عبده واقدي",
            "Utn Frmtd" => "YM301712183",
            "Payin Amount" => "299.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "محمد",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801709718",
            "Party Utn" => "801709718",
            "lastName" => "نقيب",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد سعيد محمد نقيب",
            "Utn Frmtd" => "YM801709718",
            "Payin Amount" => "39.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "ماهر",
            "thirdName" => "عبدالحميد",
            "secondName" => "عبدالرحمن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801772538",
            "Party Utn" => "801772538",
            "lastName" => "الشرعبي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ماهر عبدالرحمن عبدالحميد الشرعبي",
            "Utn Frmtd" => "YM801772538",
            "Payin Amount" => "102.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "سمير",
            "thirdName" => "يحيى",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801790358",
            "Party Utn" => "801790358",
            "lastName" => "بكير",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سمير علي يحيى بكير",
            "Utn Frmtd" => "YM801790358",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالقوي",
            "thirdName" => "فتيني",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101782591",
            "Party Utn" => "101782591",
            "lastName" => "حضرمي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالقوي حسن فتيني حضرمي",
            "Utn Frmtd" => "YM101782591",
            "Payin Amount" => "299.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "سعد",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "سعيد",
            "senderPhone" => "1",
            "Utn" => "601751346",
            "Party Utn" => "601751346",
            "lastName" => "قادري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده محمد سعد سعيد قادري",
            "Utn Frmtd" => "YM601751346",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "بشير",
            "thirdName" => "عبيد",
            "secondName" => "صالح",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601781966",
            "Party Utn" => "601781966",
            "lastName" => "معلم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "بشير صالح عبيد معلم",
            "Utn Frmtd" => "YM601781966",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "رياض",
            "thirdName" => "غالب",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901772440",
            "Party Utn" => "901772440",
            "lastName" => "جبيلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "رياض محمد غالب جبيلي",
            "Utn Frmtd" => "YM901772440",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عصام",
            "thirdName" => "عبدالله",
            "secondName" => "عمر",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701720667",
            "Party Utn" => "701720667",
            "lastName" => "دوبله",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عصام عمر عبدالله دوبله",
            "Utn Frmtd" => "YM701720667",
            "Payin Amount" => "299.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "احمد",
            "secondName" => "سالم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401774744",
            "Party Utn" => "401774744",
            "lastName" => "خليل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد سالم احمد خليل",
            "Utn Frmtd" => "YM401774744",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "احمد",
            "secondName" => "عبدالله",
            "Rcvr Frth Name" => "محمد",
            "senderPhone" => "1",
            "Utn" => "201719022",
            "Party Utn" => "201719022",
            "lastName" => "صالح",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد عبدالله احمد محمد صالح",
            "Utn Frmtd" => "YM201719022",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعيد",
            "thirdName" => "احمد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201743722",
            "Party Utn" => "201743722",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعيد علي احمد احمد",
            "Utn Frmtd" => "YM201743722",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "",
            "secondName" => "عمر",
            "Rcvr Frth Name" => "معروف",
            "senderPhone" => "1",
            "Utn" => "901706440",
            "Party Utn" => "901706440",
            "lastName" => "سليمان",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي عمر معروف سليمان",
            "Utn Frmtd" => "YM901706440",
            "Payin Amount" => "69.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "محمد",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "عبدالله",
            "senderPhone" => "1",
            "Utn" => "401788754",
            "Party Utn" => "401788754",
            "lastName" => "حضرمي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد سعيد محمد عبدالله حضرمي",
            "Utn Frmtd" => "YM401788754",
            "Payin Amount" => "199.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "يحيى",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301769423",
            "Party Utn" => "301769423",
            "lastName" => "بكير",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد علي يحيى بكير",
            "Utn Frmtd" => "YM301769423",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "خالد",
            "thirdName" => "سعيد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501709935",
            "Party Utn" => "501709935",
            "lastName" => "صميد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "خالد علي سعيد صميد",
            "Utn Frmtd" => "YM501709935",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "مفضل",
            "thirdName" => "خالد",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601787466",
            "Party Utn" => "601787466",
            "lastName" => "علايه",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "مفضل عبده خالد علايه",
            "Utn Frmtd" => "YM601787466",
            "Payin Amount" => "179.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "أنور",
            "thirdName" => "علي",
            "secondName" => "نصر",
            "Rcvr Frth Name" => "محمد",
            "senderPhone" => "1",
            "Utn" => "401723114",
            "Party Utn" => "401723114",
            "lastName" => "قادري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "أنور نصر علي محمد قادري",
            "Utn Frmtd" => "YM401723114",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "يحيى",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501726925",
            "Party Utn" => "501726925",
            "lastName" => "عبدالسلام",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي سعيد يحيى عبدالسلام",
            "Utn Frmtd" => "YM501726925",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "عوض",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301779573",
            "Party Utn" => "301779573",
            "lastName" => "جمال",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد عبده عوض جمال",
            "Utn Frmtd" => "YM301779573",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "محمد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "علي",
            "senderPhone" => "1",
            "Utn" => "301773203",
            "Party Utn" => "301773203",
            "lastName" => "هليبي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد علي محمد علي هليبي",
            "Utn Frmtd" => "YM301773203",
            "Payin Amount" => "209.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "شعيب",
            "thirdName" => "حسن",
            "secondName" => "أحمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701798277",
            "Party Utn" => "701798277",
            "lastName" => "عنبري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "شعيب أحمد حسن عنبري",
            "Utn Frmtd" => "YM701798277",
            "Payin Amount" => "79.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "نسيمة",
            "thirdName" => "عبدالله",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801732278",
            "Party Utn" => "801732278",
            "lastName" => "خلوف",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "نسيمة محمد عبدالله خلوف",
            "Utn Frmtd" => "YM801732278",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "يونس",
            "thirdName" => "علي",
            "secondName" => "علي",
            "Rcvr Frth Name" => "فتيني",
            "senderPhone" => "1",
            "Utn" => "301724683",
            "Party Utn" => "301724683",
            "lastName" => "دبح",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "يونس علي علي فتيني دبح",
            "Utn Frmtd" => "YM301724683",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سليم",
            "thirdName" => "احمد",
            "secondName" => "صالح",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401763134",
            "Party Utn" => "401763134",
            "lastName" => "سليم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سليم صالح احمد سليم",
            "Utn Frmtd" => "YM401763134",
            "Payin Amount" => "209.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "مطيع",
            "thirdName" => "عبدالله",
            "secondName" => "سلمان",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601706846",
            "Party Utn" => "601706846",
            "lastName" => "جلعوم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "مطيع سلمان عبدالله جلعوم",
            "Utn Frmtd" => "YM601706846",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالستار",
            "thirdName" => "سعيد",
            "secondName" => "سلطان",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901749990",
            "Party Utn" => "901749990",
            "lastName" => "محمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالستار سلطان سعيد محمد",
            "Utn Frmtd" => "YM901749990",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "إبراهيم",
            "thirdName" => "إسماعيل",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301718213",
            "Party Utn" => "301718213",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إبراهيم محمد إسماعيل احمد",
            "Utn Frmtd" => "YM301718213",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "محمد",
            "secondName" => "يحيى",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901783980",
            "Party Utn" => "901783980",
            "lastName" => "منصوب",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد يحيى محمد منصوب",
            "Utn Frmtd" => "YM901783980",
            "Payin Amount" => "209.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "سالم",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101734721",
            "Party Utn" => "101734721",
            "lastName" => "كعني",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده محمد سالم كعني",
            "Utn Frmtd" => "YM101734721",
            "Payin Amount" => "48.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "هبه",
            "thirdName" => "عبدالرحمن",
            "secondName" => "صغير",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701789177",
            "Party Utn" => "701789177",
            "lastName" => "علي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "هبه صغير عبدالرحمن علي",
            "Utn Frmtd" => "YM701789177",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "مختار",
            "thirdName" => "عبدالحميد",
            "secondName" => "عبدالرحمن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601787806",
            "Party Utn" => "601787806",
            "lastName" => "الشرعبي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "مختار عبدالرحمن عبدالحميد الشرعبي",
            "Utn Frmtd" => "YM601787806",
            "Payin Amount" => "101.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "إبراهيم",
            "thirdName" => "ياسين",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901770819",
            "Party Utn" => "901770819",
            "lastName" => "جبيلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إبراهيم محمد ياسين جبيلي",
            "Utn Frmtd" => "YM901770819",
            "Payin Amount" => "209.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "مطيوف",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601742586",
            "Party Utn" => "601742586",
            "lastName" => "محمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده حسن مطيوف محمد",
            "Utn Frmtd" => "YM601742586",
            "Payin Amount" => "77.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالله",
            "thirdName" => "غالب",
            "secondName" => "ثابت",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901707880",
            "Party Utn" => "901707880",
            "lastName" => "دوبله",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله ثابت غالب دوبله",
            "Utn Frmtd" => "YM901707880",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "منصور",
            "thirdName" => "سليمان",
            "secondName" => "عمر",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601703626",
            "Party Utn" => "601703626",
            "lastName" => "محيني",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "منصور عمر سليمان محيني",
            "Utn Frmtd" => "YM601703626",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "ابراهيم",
            "thirdName" => "عنيني",
            "secondName" => "ناصر",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201780822",
            "Party Utn" => "201780822",
            "lastName" => "مشهور",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ابراهيم ناصر عنيني مشهور",
            "Utn Frmtd" => "YM201780822",
            "Payin Amount" => "209.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "وليد",
            "thirdName" => "صالح",
            "secondName" => "عبدالله",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201787382",
            "Party Utn" => "201787382",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "وليد عبدالله صالح احمد",
            "Utn Frmtd" => "YM201787382",
            "Payin Amount" => "77.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "محمد",
            "secondName" => "تمبول",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901753679",
            "Party Utn" => "901753679",
            "lastName" => "عباس",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده تمبول محمد عباس",
            "Utn Frmtd" => "YM901753679",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "فيصل",
            "thirdName" => "سيف",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601721766",
            "Party Utn" => "601721766",
            "lastName" => "جبلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "فيصل علي سيف جبلي",
            "Utn Frmtd" => "YM601721766",
            "Payin Amount" => "24.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عوض",
            "thirdName" => "علي",
            "secondName" => "احمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401792984",
            "Party Utn" => "401792984",
            "lastName" => "عبدالله",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عوض احمد علي عبدالله",
            "Utn Frmtd" => "YM401792984",
            "Payin Amount" => "239.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "مرشد",
            "thirdName" => "محمد",
            "secondName" => "صالح",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101789661",
            "Party Utn" => "101789661",
            "lastName" => "حساني",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "مرشد صالح محمد حساني",
            "Utn Frmtd" => "YM101789661",
            "Payin Amount" => "77.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمود",
            "thirdName" => "محمد",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901764859",
            "Party Utn" => "901764859",
            "lastName" => "معروف",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمود سعيد محمد معروف",
            "Utn Frmtd" => "YM901764859",
            "Payin Amount" => "209.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "سالم",
            "secondName" => "قاسم",
            "Rcvr Frth Name" => "سعيد",
            "senderPhone" => "1",
            "Utn" => "901799710",
            "Party Utn" => "901799710",
            "lastName" => "عسال",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي قاسم سالم سعيد عسال",
            "Utn Frmtd" => "YM901799710",
            "Payin Amount" => "54.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "محمد",
            "secondName" => "شمسان",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901716980",
            "Party Utn" => "901716980",
            "lastName" => "معمري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده شمسان محمد معمري",
            "Utn Frmtd" => "YM901716980",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "أسامة",
            "thirdName" => "يحيى",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101757771",
            "Party Utn" => "101757771",
            "lastName" => "عنينة",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "أسامة علي يحيى عنينة",
            "Utn Frmtd" => "YM101757771",
            "Payin Amount" => "349.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "يحيى",
            "thirdName" => "يحيى",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201727722",
            "Party Utn" => "201727722",
            "lastName" => "دعسين",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "يحيى محمد يحيى دعسين",
            "Utn Frmtd" => "YM201727722",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "احمد",
            "thirdName" => "يحيى",
            "secondName" => "يحيى",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301756263",
            "Party Utn" => "301756263",
            "lastName" => "محفوظ",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "احمد يحيى يحيى محفوظ",
            "Utn Frmtd" => "YM301756263",
            "Payin Amount" => "54.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "راسم",
            "thirdName" => "فخري",
            "secondName" => "مصطفى",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801781988",
            "Party Utn" => "801781988",
            "lastName" => "راسم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "راسم مصطفى فخري راسم",
            "Utn Frmtd" => "YM801781988",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "محمد",
            "secondName" => "خبتي",
            "Rcvr Frth Name" => "عمر",
            "senderPhone" => "1",
            "Utn" => "401714644",
            "Party Utn" => "401714644",
            "lastName" => "زهري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي خبتي محمد عمر زهري",
            "Utn Frmtd" => "YM401714644",
            "Payin Amount" => "374.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالله",
            "thirdName" => "إسماعيل",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501757015",
            "Party Utn" => "501757015",
            "lastName" => "بادي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله عبده إسماعيل بادي",
            "Utn Frmtd" => "YM501757015",
            "Payin Amount" => "20.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالله",
            "thirdName" => "سعيد",
            "secondName" => "قاسم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501785175",
            "Party Utn" => "501785175",
            "lastName" => "سالم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله قاسم سعيد سالم",
            "Utn Frmtd" => "YM501785175",
            "Payin Amount" => "54.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبد الله",
            "thirdName" => "",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "يحيى",
            "senderPhone" => "1",
            "Utn" => "101723101",
            "Party Utn" => "101723101",
            "lastName" => "كليب",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبد الله محمد يحيى كليب",
            "Utn Frmtd" => "YM101723101",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "محمد",
            "secondName" => "خبتي",
            "Rcvr Frth Name" => "عمر",
            "senderPhone" => "1",
            "Utn" => "701704207",
            "Party Utn" => "701704207",
            "lastName" => "زهري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي خبتي محمد عمر زهري",
            "Utn Frmtd" => "YM701704207",
            "Payin Amount" => "249.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "عائشه",
            "thirdName" => "علي",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901751230",
            "Party Utn" => "901751230",
            "lastName" => "عنتري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عائشه محمد علي عنتري",
            "Utn Frmtd" => "YM901751230",
            "Payin Amount" => "122.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "احمد",
            "thirdName" => "سعيد",
            "secondName" => "سالم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901733929",
            "Party Utn" => "901733929",
            "lastName" => "خادم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "احمد سالم سعيد خادم",
            "Utn Frmtd" => "YM901733929",
            "Payin Amount" => "44.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "سعد",
            "secondName" => "عبد الله",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501728425",
            "Party Utn" => "501728425",
            "lastName" => "كنزل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده عبد الله سعد كنزل",
            "Utn Frmtd" => "YM501728425",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "سعيد",
            "secondName" => "ثابت",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401775574",
            "Party Utn" => "401775574",
            "lastName" => "بهيدر",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي ثابت سعيد بهيدر",
            "Utn Frmtd" => "YM401775574",
            "Payin Amount" => "459.3",
        ],
        [
            "Status" => "Available",
            "firstName" => "حسام",
            "thirdName" => "معروف",
            "secondName" => "",
            "Rcvr Frth Name" => "عبدالله",
            "senderPhone" => "1",
            "Utn" => "201759582",
            "Party Utn" => "201759582",
            "lastName" => "الوحيدي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حسام معروف عبدالله الوحيدي",
            "Utn Frmtd" => "YM201759582",
            "Payin Amount" => "99.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "حسن",
            "thirdName" => "قاسم",
            "secondName" => "احمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401708974",
            "Party Utn" => "401708974",
            "lastName" => "عسال",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حسن احمد قاسم عسال",
            "Utn Frmtd" => "YM401708974",
            "Payin Amount" => "44.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "ياسين",
            "thirdName" => "محمد",
            "secondName" => "حسين",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301717003",
            "Party Utn" => "301717003",
            "lastName" => "علي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ياسين حسين محمد علي",
            "Utn Frmtd" => "YM301717003",
            "Payin Amount" => "90.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "سعيد",
            "secondName" => "ثابت",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301715873",
            "Party Utn" => "301715873",
            "lastName" => "بهيدر",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي ثابت سعيد بهيدر",
            "Utn Frmtd" => "YM301715873",
            "Payin Amount" => "614.25",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "احمد",
            "secondName" => "إبراهيم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601724306",
            "Party Utn" => "601724306",
            "lastName" => "علي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده إبراهيم احمد علي",
            "Utn Frmtd" => "YM601724306",
            "Payin Amount" => "199.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "وهب الله",
            "thirdName" => "عبدالله",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701726757",
            "Party Utn" => "701726757",
            "lastName" => "",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "وهب الله عبده عبدالله ",
            "Utn Frmtd" => "YM701726757",
            "Payin Amount" => "52.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "فتيني",
            "senderPhone" => "1",
            "Utn" => "401788784",
            "Party Utn" => "401788784",
            "lastName" => "كويك",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي حسن فتيني كويك",
            "Utn Frmtd" => "YM401788784",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "محمد",
            "secondName" => "حسين",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401739234",
            "Party Utn" => "401739234",
            "lastName" => "كنزل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي حسين محمد كنزل",
            "Utn Frmtd" => "YM401739234",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "وسام",
            "thirdName" => "مرشد",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "علي",
            "senderPhone" => "1",
            "Utn" => "401796314",
            "Party Utn" => "401796314",
            "lastName" => "زهير",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "وسام عبده مرشد علي زهير",
            "Utn Frmtd" => "YM401796314",
            "Payin Amount" => "20.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالله",
            "thirdName" => "محمد",
            "secondName" => "عوض",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201727742",
            "Party Utn" => "201727742",
            "lastName" => "عوض",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله عوض محمد عوض",
            "Utn Frmtd" => "YM201727742",
            "Payin Amount" => "49.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالمؤمن",
            "thirdName" => "احمد",
            "secondName" => "علي",
            "Rcvr Frth Name" => "سعيد",
            "senderPhone" => "1",
            "Utn" => "701791007",
            "Party Utn" => "701791007",
            "lastName" => "الصبري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالمؤمن علي احمد سعيد الصبري",
            "Utn Frmtd" => "YM701791007",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعيد",
            "thirdName" => "مدارج",
            "secondName" => "جمال",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601794506",
            "Party Utn" => "601794506",
            "lastName" => "حنيش",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعيد جمال مدارج حنيش",
            "Utn Frmtd" => "YM601794506",
            "Payin Amount" => "349.35",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالكريم",
            "thirdName" => "علي",
            "secondName" => "عائض",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901751220",
            "Party Utn" => "901751220",
            "lastName" => "بادي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالكريم عائض علي بادي",
            "Utn Frmtd" => "YM901751220",
            "Payin Amount" => "20.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعدان",
            "thirdName" => "فتيني",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901752630",
            "Party Utn" => "901752630",
            "lastName" => "جمال",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعدان علي فتيني جمال",
            "Utn Frmtd" => "YM901752630",
            "Payin Amount" => "53.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "ادم",
            "thirdName" => "احمد",
            "secondName" => "داوود",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401700784",
            "Party Utn" => "401700784",
            "lastName" => "سليم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ادم داوود احمد سليم",
            "Utn Frmtd" => "YM401700784",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "الامين",
            "thirdName" => "حسن",
            "secondName" => "ابراهيم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301725393",
            "Party Utn" => "301725393",
            "lastName" => "باجل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "الامين ابراهيم حسن باجل",
            "Utn Frmtd" => "YM301725393",
            "Payin Amount" => "154.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "احمد",
            "secondName" => "",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101772461",
            "Party Utn" => "101772461",
            "lastName" => "حسن",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي احمد حسن",
            "Utn Frmtd" => "YM101772461",
            "Payin Amount" => "20.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "منصور",
            "thirdName" => "محمد",
            "secondName" => "عوض",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101732501",
            "Party Utn" => "101732501",
            "lastName" => "عوض",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "منصور عوض محمد عوض",
            "Utn Frmtd" => "YM101732501",
            "Payin Amount" => "49.1",
        ],
        [
            "Status" => "Available",
            "firstName" => "طالب",
            "thirdName" => "طالب",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "عبدالله",
            "senderPhone" => "1",
            "Utn" => "301789843",
            "Party Utn" => "301789843",
            "lastName" => "الأهدل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "طالب محمد طالب عبدالله الأهدل",
            "Utn Frmtd" => "YM301789843",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "ابراهيم",
            "thirdName" => "يحيى",
            "secondName" => "سالم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701764367",
            "Party Utn" => "701764367",
            "lastName" => "متيله",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "ابراهيم سالم يحيى متيله",
            "Utn Frmtd" => "YM701764367",
            "Payin Amount" => "122.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "يوسف",
            "thirdName" => "حسين",
            "secondName" => "علي",
            "Rcvr Frth Name" => "محمد",
            "senderPhone" => "1",
            "Utn" => "801793258",
            "Party Utn" => "801793258",
            "lastName" => "سالم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "يوسف علي حسين محمد سالم",
            "Utn Frmtd" => "YM801793258",
            "Payin Amount" => "24.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "إبراهيم",
            "thirdName" => "قاسم",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "501737145",
            "Party Utn" => "501737145",
            "lastName" => "سالم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إبراهيم علي قاسم سالم",
            "Utn Frmtd" => "YM501737145",
            "Payin Amount" => "44.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبد الله",
            "thirdName" => "",
            "secondName" => "عبد المطلب",
            "Rcvr Frth Name" => "محسن",
            "senderPhone" => "1",
            "Utn" => "501750775",
            "Party Utn" => "501750775",
            "lastName" => "الشميري",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبد الله عبد المطلب محسن الشميري",
            "Utn Frmtd" => "YM501750775",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالرحمن",
            "thirdName" => "يوسف",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701775967",
            "Party Utn" => "701775967",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالرحمن محمد يوسف احمد",
            "Utn Frmtd" => "YM701775967",
            "Payin Amount" => "199.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "محمد",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "401718174",
            "Party Utn" => "401718174",
            "lastName" => "مهجمي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي عبده محمد مهجمي",
            "Utn Frmtd" => "YM401718174",
            "Payin Amount" => "24.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "عبدة",
            "secondName" => "صالح",
            "Rcvr Frth Name" => "علي",
            "senderPhone" => "1",
            "Utn" => "301762653",
            "Party Utn" => "301762653",
            "lastName" => "صالح",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي صالح عبدة علي صالح",
            "Utn Frmtd" => "YM301762653",
            "Payin Amount" => "52.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "إسما",
            "thirdName" => "عبدة",
            "secondName" => "عيل",
            "Rcvr Frth Name" => "خادم",
            "senderPhone" => "1",
            "Utn" => "501757045",
            "Party Utn" => "501757045",
            "lastName" => "مخرافي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "إسما عيل عبدة خادم مخرافي",
            "Utn Frmtd" => "YM501757045",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عدنان",
            "thirdName" => "عبده",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901790240",
            "Party Utn" => "901790240",
            "lastName" => "احمد",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عدنان محمد عبده احمد",
            "Utn Frmtd" => "YM901790240",
            "Payin Amount" => "131.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "سعد",
            "thirdName" => "حسين",
            "secondName" => "علي",
            "Rcvr Frth Name" => "محمد",
            "senderPhone" => "1",
            "Utn" => "901744710",
            "Party Utn" => "901744710",
            "lastName" => "سالم",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "سعد علي حسين محمد سالم",
            "Utn Frmtd" => "YM901744710",
            "Payin Amount" => "24.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالله",
            "thirdName" => "احمد",
            "secondName" => "سعيد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901788919",
            "Party Utn" => "901788919",
            "lastName" => "بو",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله سعيد احمد بو",
            "Utn Frmtd" => "YM901788919",
            "Payin Amount" => "131.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "سعيد",
            "secondName" => "عبده",
            "Rcvr Frth Name" => "عمر",
            "senderPhone" => "1",
            "Utn" => "601742236",
            "Party Utn" => "601742236",
            "lastName" => "عدني",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي عبده سعيد عمر عدني",
            "Utn Frmtd" => "YM601742236",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "علي",
            "secondName" => "بن",
            "Rcvr Frth Name" => "يحي",
            "senderPhone" => "1",
            "Utn" => "901795000",
            "Party Utn" => "901795000",
            "lastName" => "رزة",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي بن علي يحي رزة",
            "Utn Frmtd" => "YM901795000",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "يحيى",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901725199",
            "Party Utn" => "901725199",
            "lastName" => "كنزل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي محمد يحيى كنزل",
            "Utn Frmtd" => "YM901725199",
            "Payin Amount" => "24.7",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "علي",
            "secondName" => "حسن",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901767979",
            "Party Utn" => "901767979",
            "lastName" => "مهيل",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد حسن علي مهيل",
            "Utn Frmtd" => "YM901767979",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالله",
            "thirdName" => "قاسم",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "101733991",
            "Party Utn" => "101733991",
            "lastName" => "طليلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالله محمد قاسم طليلي",
            "Utn Frmtd" => "YM101733991",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "الغشمي",
            "thirdName" => "سالم",
            "secondName" => "علي",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601787816",
            "Party Utn" => "601787816",
            "lastName" => "دعدع",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "الغشمي علي سالم دعدع",
            "Utn Frmtd" => "YM601787816",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "عادل",
            "thirdName" => "",
            "secondName" => "علي",
            "Rcvr Frth Name" => "محمد",
            "senderPhone" => "1",
            "Utn" => "401793054",
            "Party Utn" => "401793054",
            "lastName" => "علي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عادل علي محمد علي",
            "Utn Frmtd" => "YM401793054",
            "Payin Amount" => "77.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "خالد",
            "thirdName" => "أحمد",
            "secondName" => "مقبل",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801765188",
            "Party Utn" => "801765188",
            "lastName" => "زغيفي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "خالد مقبل أحمد زغيفي",
            "Utn Frmtd" => "YM801765188",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "محمد",
            "secondName" => "معافا",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701734137",
            "Party Utn" => "701734137",
            "lastName" => "دليبي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي معافا محمد دليبي",
            "Utn Frmtd" => "YM701734137",
            "Payin Amount" => "599.3",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبدالكريم",
            "thirdName" => "احمد",
            "secondName" => "قاسم",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "601729566",
            "Party Utn" => "601729566",
            "lastName" => "حميدان",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبدالكريم قاسم احمد حميدان",
            "Utn Frmtd" => "YM601729566",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "علي",
            "secondName" => "احمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "201736802",
            "Party Utn" => "201736802",
            "lastName" => "يوسف",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد احمد علي يوسف",
            "Utn Frmtd" => "YM201736802",
            "Payin Amount" => "77.3",
        ],
        [
            "Status" => "Available",
            "firstName" => "عبده",
            "thirdName" => "عبده",
            "secondName" => "ثابت",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801785578",
            "Party Utn" => "801785578",
            "lastName" => "مغربي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "عبده ثابت عبده مغربي",
            "Utn Frmtd" => "YM801785578",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "يوسف",
            "thirdName" => "عبدالله",
            "secondName" => "علي",
            "Rcvr Frth Name" => "ناجي",
            "senderPhone" => "1",
            "Utn" => "501738175",
            "Party Utn" => "501738175",
            "lastName" => "العزاني",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "يوسف علي عبدالله ناجي العزاني",
            "Utn Frmtd" => "YM501738175",
            "Payin Amount" => "649.25",
        ],
        [
            "Status" => "Available",
            "firstName" => "يحي",
            "thirdName" => "علي",
            "secondName" => "ثابت",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301799943",
            "Party Utn" => "301799943",
            "lastName" => "عدني",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "يحي ثابت علي عدني",
            "Utn Frmtd" => "YM301799943",
            "Payin Amount" => "124.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "علي",
            "thirdName" => "احمد",
            "secondName" => "صالح",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901777259",
            "Party Utn" => "901777259",
            "lastName" => "بو",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "علي صالح احمد بو",
            "Utn Frmtd" => "YM901777259",
            "Payin Amount" => "131.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "محمد",
            "thirdName" => "ثابت",
            "secondName" => "يوسف",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "301789463",
            "Party Utn" => "301789463",
            "lastName" => "دبع",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "محمد يوسف ثابت دبع",
            "Utn Frmtd" => "YM301789463",
            "Payin Amount" => "149.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "صادق",
            "thirdName" => "عبدالله",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "901748289",
            "Party Utn" => "901748289",
            "lastName" => "دوبلة",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "صادق محمد عبدالله دوبلة",
            "Utn Frmtd" => "YM901748289",
            "Payin Amount" => "90.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "هارون",
            "thirdName" => "محمود",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "701769447",
            "Party Utn" => "701769447",
            "lastName" => "جبلي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "هارون محمد محمود جبلي",
            "Utn Frmtd" => "YM701769447",
            "Payin Amount" => "90.9",
        ],
        [
            "Status" => "Available",
            "firstName" => "لؤي",
            "thirdName" => "صغير",
            "secondName" => "محمد",
            "Rcvr Frth Name" => "",
            "senderPhone" => "1",
            "Utn" => "801746968",
            "Party Utn" => "801746968",
            "lastName" => "سليمان",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "لؤي محمد صغير سليمان",
            "Utn Frmtd" => "YM801746968",
            "Payin Amount" => "159.5",
        ],
        [
            "Status" => "Available",
            "firstName" => "حمزة",
            "thirdName" => "علي",
            "secondName" => "",
            "Rcvr Frth Name" => "محمد",
            "senderPhone" => "1",
            "Utn" => "201756752",
            "Party Utn" => "201756752",
            "lastName" => "علي",
            "Payin Currency Code" => "USD",
            "Rcvr Full Name" => "حمزة علي محمد علي",
            "Utn Frmtd" => "YM201756752",
            "Payin Amount" => "159.5",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عميد",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "1",
            "Rcvr Mobile" => "777310734",
            "Fee Amount" => "250",
            "Date" => "29/03/2023",
            "Party File" => "21",
            "thirdName" => "علي",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "السلال",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "ناجي",
            "Utn" => "203370262",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "203370262",
            "lastName" => "العاتي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عميد السلال علي ناجي العاتي",
            "Utn Frmtd" => "YM203370262",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عدنان",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "2",
            "Rcvr Mobile" => "773727164",
            "Fee Amount" => "250",
            "Date" => "29/03/2023",
            "Party File" => "21",
            "thirdName" => "عبده",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "محمد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "103381331",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "103381331",
            "lastName" => "احمد",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عدنان محمد عبده احمد",
            "Utn Frmtd" => "YM103381331",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "علي",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "3",
            "Rcvr Mobile" => "777362921",
            "Fee Amount" => "250",
            "Date" => "29/03/2023",
            "Party File" => "21",
            "thirdName" => "احمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "صالح",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "203342592",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "203342592",
            "lastName" => "بو",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "علي صالح احمد بو",
            "Utn Frmtd" => "YM203342592",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "ايمن",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "1",
            "Rcvr Mobile" => "773308055",
            "Fee Amount" => "250",
            "Date" => "29/03/2023",
            "Party File" => "22",
            "thirdName" => "محمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "يحيى",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "حمود",
            "Utn" => "303322043",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "303322043",
            "lastName" => "حنينه",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "ايمن يحيى محمد حمود حنينه",
            "Utn Frmtd" => "YM303322043",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عميد",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "1",
            "Rcvr Mobile" => "777310734",
            "Fee Amount" => "250",
            "Date" => "29/03/2023",
            "Party File" => "20",
            "thirdName" => "علي",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "السلال",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "ناجي",
            "Utn" => "403362914",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "403362914",
            "lastName" => "العاتي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عميد السلال علي ناجي العاتي",
            "Utn Frmtd" => "YM403362914",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عدنان",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "2",
            "Rcvr Mobile" => "773727164",
            "Fee Amount" => "250",
            "Date" => "29/03/2023",
            "Party File" => "20",
            "thirdName" => "عبده",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "محمد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "603365046",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "603365046",
            "lastName" => "احمد",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عدنان محمد عبده احمد",
            "Utn Frmtd" => "YM603365046",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "ماهر",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "5",
            "Rcvr Mobile" => "776283124",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "19",
            "thirdName" => "عبدالحميد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "عبدالرحمن",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "402454574",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "402454574",
            "lastName" => "الشرعبي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "ماهر عبدالرحمن عبدالحميد الشرعبي",
            "Utn Frmtd" => "YM402454574",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "علي",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "8",
            "Rcvr Mobile" => "776753796",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "19",
            "thirdName" => "",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "عمر",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "معروف",
            "Utn" => "402477154",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "402477154",
            "lastName" => "سليمان",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "علي عمر معروف سليمان",
            "Utn Frmtd" => "YM402477154",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عميد",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "1",
            "Rcvr Mobile" => "777310734",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "18",
            "thirdName" => "علي",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "السلال",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "ناجي",
            "Utn" => "202421192",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "202421192",
            "lastName" => "العاتي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عميد السلال علي ناجي العاتي",
            "Utn Frmtd" => "YM202421192",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عدنان",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "2",
            "Rcvr Mobile" => "773727164",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "18",
            "thirdName" => "عبده",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "محمد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "102438691",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "102438691",
            "lastName" => "احمد",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عدنان محمد عبده احمد",
            "Utn Frmtd" => "YM102438691",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "علي",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "3",
            "Rcvr Mobile" => "777362921",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "18",
            "thirdName" => "احمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "صالح",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "902422850",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "902422850",
            "lastName" => "بو",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "علي صالح احمد بو",
            "Utn Frmtd" => "YM902422850",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عبدالله",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "4",
            "Rcvr Mobile" => "772110323",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "18",
            "thirdName" => "احمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "سعيد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "702489347",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "702489347",
            "lastName" => "بو",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عبدالله سعيد احمد بو",
            "Utn Frmtd" => "YM702489347",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "ماهر",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "5",
            "Rcvr Mobile" => "776283124",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "18",
            "thirdName" => "عبدالحميد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "عبدالرحمن",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "202447032",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "202447032",
            "lastName" => "الشرعبي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "ماهر عبدالرحمن عبدالحميد الشرعبي",
            "Utn Frmtd" => "YM202447032",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عميد",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "1",
            "Rcvr Mobile" => "777310734",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "19",
            "thirdName" => "علي",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "السلال",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "ناجي",
            "Utn" => "802436918",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "802436918",
            "lastName" => "العاتي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عميد السلال علي ناجي العاتي",
            "Utn Frmtd" => "YM802436918",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عدنان",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "2",
            "Rcvr Mobile" => "773727164",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "19",
            "thirdName" => "عبده",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "محمد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "402402564",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "402402564",
            "lastName" => "احمد",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عدنان محمد عبده احمد",
            "Utn Frmtd" => "YM402402564",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "علي",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "3",
            "Rcvr Mobile" => "777362921",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "19",
            "thirdName" => "احمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "صالح",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "602459756",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "602459756",
            "lastName" => "بو",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "علي صالح احمد بو",
            "Utn Frmtd" => "YM602459756",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عبدالله",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "4",
            "Rcvr Mobile" => "772110323",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "19",
            "thirdName" => "احمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "سعيد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "302478823",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "302478823",
            "lastName" => "بو",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عبدالله سعيد احمد بو",
            "Utn Frmtd" => "YM302478823",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عميد",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "1",
            "Rcvr Mobile" => "777310734",
            "Fee Amount" => "350",
            "Date" => "20/03/2023",
            "Party File" => "9",
            "thirdName" => "علي",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "السلال",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "ناجي",
            "Utn" => "602443646",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "602443646",
            "lastName" => "العاتي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عميد السلال علي ناجي العاتي",
            "Utn Frmtd" => "YM602443646",
            "Payin Amount" => "50000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عدنان",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "2",
            "Rcvr Mobile" => "773727164",
            "Fee Amount" => "350",
            "Date" => "20/03/2023",
            "Party File" => "9",
            "thirdName" => "عبده",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "محمد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "702450517",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "702450517",
            "lastName" => "احمد",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عدنان محمد عبده احمد",
            "Utn Frmtd" => "YM702450517",
            "Payin Amount" => "50000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "علي",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "3",
            "Rcvr Mobile" => "777362921",
            "Fee Amount" => "350",
            "Date" => "20/03/2023",
            "Party File" => "9",
            "thirdName" => "احمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "صالح",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "902423449",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "902423449",
            "lastName" => "بو",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "علي صالح احمد بو",
            "Utn Frmtd" => "YM902423449",
            "Payin Amount" => "50000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عبدالله",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "4",
            "Rcvr Mobile" => "772110323",
            "Fee Amount" => "350",
            "Date" => "20/03/2023",
            "Party File" => "9",
            "thirdName" => "احمد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "سعيد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "902456410",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "902456410",
            "lastName" => "بو",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عبدالله سعيد احمد بو",
            "Utn Frmtd" => "YM902456410",
            "Payin Amount" => "50000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "ماهر",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "5",
            "Rcvr Mobile" => "776283124",
            "Fee Amount" => "350",
            "Date" => "20/03/2023",
            "Party File" => "9",
            "thirdName" => "عبدالحميد",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "عبدالرحمن",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "602415626",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "602415626",
            "lastName" => "الشرعبي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "ماهر عبدالرحمن عبدالحميد الشرعبي",
            "Utn Frmtd" => "YM602415626",
            "Payin Amount" => "50000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "a",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "",
            "Rcvr Mobile" => "77777",
            "Fee Amount" => "350",
            "Date" => "20/03/2023",
            "Party File" => "",
            "thirdName" => "a",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "a",
            "secondName" => "a",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP002",
            "Sndr Scnd Name" => "a",
            "Sndr Sur Name" => "a",
            "senderPhone" => "7",
            "Rcvr Frth Name" => "",
            "Utn" => "602423176",
            "Sndr Full Name" => "a a a a",
            "Party Utn" => "602423176",
            "lastName" => "a",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "a",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "a a a a",
            "Utn Frmtd" => "YM602423176",
            "Payin Amount" => "25000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عميد",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "1",
            "Rcvr Mobile" => "777310734",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "17",
            "thirdName" => "علي",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "السلال",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "ناجي",
            "Utn" => "702469177",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "702469177",
            "lastName" => "العاتي",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عميد السلال علي ناجي العاتي",
            "Utn Frmtd" => "YM702469177",
            "Payin Amount" => "10000",
        ],
        [
            "Payin" => "Yeah Money",
            "Opr Othr Fee Amount" => "0",
            "Status" => "Available",
            "Payout" => "Yeah Money",
            "firstName" => "عدنان",
            "Opr Fee Currency Code" => "YER",
            "Party No" => "2",
            "Rcvr Mobile" => "773727164",
            "Fee Amount" => "250",
            "Date" => "20/03/2023",
            "Party File" => "17",
            "thirdName" => "عبده",
            "Fee Currency Code" => "YER",
            "Sndr Frst Name" => "",
            "secondName" => "محمد",
            "Sndr Frth Name" => "",
            "Payout Type" => "Network Provider",
            "Project Code" => "CORP001",
            "Sndr Scnd Name" => "",
            "Sndr Sur Name" => "",
            "senderPhone" => "1",
            "Rcvr Frth Name" => "",
            "Utn" => "902419640",
            "Sndr Full Name" => "VISION HOPE",
            "Party Utn" => "902419640",
            "lastName" => "احمد",
            "Opr Fee Amount" => "0",
            "Opr Othr Fee Desc" => "",
            "Sndr Thrd Name" => "",
            "Year" => "2023",
            "Payin Currency Code" => "YER",
            "Rcvr Full Name" => "عدنان محمد عبده احمد",
            "Utn Frmtd" => "YM902419640",
            "Payin Amount" => "10000",
        ],
    ];

    // Manual Processing Methods

    /**
     * Get the admin user who processed this harvest manually
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo('App\Models\User', 'processed_by');
    }

    /**
     * Get the customer who owns this harvest
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo('App\Models\User', 'party_id');
    }

    /**
     * Check if this harvest is in manual processing
     */
    public function isManual(): bool
    {
        return $this->is_manual === true;
    }

    /**
     * Check if this harvest uses automated processing
     */
    public function isAutomated(): bool
    {
        return $this->is_manual === false;
    }

    /**
     * Get the current manual processing stage
     */
    public function getManualStage(): ?int
    {
        return $this->manual_stage;
    }

    /**
     * Get the status enum instance
     */
    public function getStatusEnum(): ?HarvestStatusEnum
    {
        return HarvestStatusEnum::findByValue($this->status);
    }

    /**
     * Check if harvest is in a specific manual status
     */
    public function hasManualStatus(HarvestStatusEnum $status): bool
    {
        return $this->status === $status->value;
    }

    /**
     * Check if harvest is in pending state
     */
    public function isPending(): bool
    {
        $statusEnum = $this->getStatusEnum();
        return $statusEnum ? $statusEnum->isPending() : false;
    }

    /**
     * Start manual processing
     */
    public function startManualProcessing(): void
    {
        $this->update([
            'is_manual' => true,
            'status' => HarvestStatusEnum::MANUAL_PENDING->value,
            'manual_stage' => 1,
            'manual_started_at' => now()
        ]);
    }

    /**
     * Advance to next manual processing stage
     */
    public function advanceManualStage(int $stage, ?string $notes = null): void
    {
        $statusMap = [
            1 => HarvestStatusEnum::MANUAL_VERIFICATION,
            2 => HarvestStatusEnum::MANUAL_DATA_INPUT,
            3 => HarvestStatusEnum::MANUAL_REVIEW,
            4 => HarvestStatusEnum::MANUAL_APPROVED,
            5 => HarvestStatusEnum::MANUAL_COMPLETED
        ];

        $this->update([
            'manual_stage' => $stage,
            'status' => $statusMap[$stage]->value,
            'manual_notes' => $notes ? ($this->manual_notes . "\n" . $notes) : $this->manual_notes
        ]);
    }

    /**
     * Complete manual processing
     */
    public function completeManualProcessing(int $processedBy, ?string $notes = null): void
    {
        $this->update([
            'status' => HarvestStatusEnum::MANUAL_COMPLETED->value,
            'manual_stage' => 5,
            'processed_by' => $processedBy,
            'manual_completed_at' => now(),
            'manual_notes' => $notes ? ($this->manual_notes . "\n" . $notes) : $this->manual_notes
        ]);
    }

    /**
     * Reject manual processing
     */
    public function rejectManualProcessing(int $processedBy, string $reason): void
    {
        $this->update([
            'status' => HarvestStatusEnum::MANUAL_REJECTED->value,
            'processed_by' => $processedBy,
            'rejection_reason' => $reason,
            'manual_completed_at' => now()
        ]);
    }

    /**
     * Scope for manual harvests
     */
    public function scopeManual($query)
    {
        return $query->where('is_manual', true);
    }

    /**
     * Scope for automated harvests
     */
    public function scopeAutomated($query)
    {
        return $query->where('is_manual', false);
    }

    /**
     * Scope for harvests in specific manual stage
     */
    public function scopeInManualStage($query, int $stage)
    {
        return $query->where('is_manual', true)->where('manual_stage', $stage);
    }

    /**
     * Scope for pending manual harvests
     */
    public function scopePendingManual($query)
    {
        return $query->where('is_manual', true)
                    ->whereIn('status', [
                        HarvestStatusEnum::MANUAL_PENDING->value,
                        HarvestStatusEnum::MANUAL_VERIFICATION->value,
                        HarvestStatusEnum::MANUAL_DATA_INPUT->value,
                        HarvestStatusEnum::MANUAL_REVIEW->value,
                        HarvestStatusEnum::MANUAL_APPROVED->value
                    ]);
    }

    /**
     * Get manual processing progress percentage
     */
    public function getManualProgress(): int
    {
        if (!$this->is_manual) {
            return 0;
        }

        return match($this->status) {
            HarvestStatusEnum::MANUAL_PENDING->value => 10,
            HarvestStatusEnum::MANUAL_VERIFICATION->value => 25,
            HarvestStatusEnum::MANUAL_DATA_INPUT->value => 50,
            HarvestStatusEnum::MANUAL_REVIEW->value => 75,
            HarvestStatusEnum::MANUAL_APPROVED->value => 90,
            HarvestStatusEnum::MANUAL_COMPLETED->value => 100,
            HarvestStatusEnum::MANUAL_REJECTED->value => 0,
            default => 0
        };
    }
}

