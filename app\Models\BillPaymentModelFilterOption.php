<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillPaymentModelFilterOption extends Model
{
    use HasFactory;

    protected $fillable = ['model','model_id','bill_payment_filter_option_id'];
    protected $casts = [
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];

}
