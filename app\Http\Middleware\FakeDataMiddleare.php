<?php

namespace App\Http\Middleware;
use App\Models\Cardless;
use Closure;
use Illuminate\Support\Facades\Http;
class FakeDataMiddleare
{
    public function handle($request, Closure $next, $guard = null)
    {
        if(env('APP_HOSTING', 'remote')=='local' && env('APP_ENV', 'production')!='production'){
            $url="{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1";


            $fakes=[
                "{$url}/j_security_check" => Http::response([
                ],302,[
                    'set-cookie'=>"JSESSIONID=RUkrxR6LMh2GtS88kKsyXgwKOxoPBm0Rl1hf4o7NrPNjmhv9EG-jf!-173767!NONE; path=/; HttpOnly",
                ]),
                "{$url}/me" => Http::response(
                    Cardless::fakeMe,
                    200),
                "{$url}/jwt" => Http::response([
                    'jwtoken'=>"RUkrxR6LMh2GtS88kKsyXgwKOxoPBm0Rl1hf4o7NrPNjmhv9EG"
                ],200),
                "{$url}/mobileClient" => Http::response([
                    'status'=>[
                        "result"    => "INFO",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ],200),
                "{$url}/mobileClient/pushToken/os/" => Http::response([
                    'status'=>[
                        "result"    => "INFO",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ],200),
                "{$url}/CardInquiry" => Http::response([
                    "mobileNumber" => "",
                    "firstName" => "&#x645;&#x62d;&#x645;&#x62f;",
                    "lastName" => "&#x642;&#x645;&#x62d;&#x627;&#x646;",
                    "gender" => "M",
                    "idType" => "ID",
                    "idNumber" => "***********",
                    "idIssuePlace" => "&#x627;&#x645;&#x627;&#x646;&#x629; &#x627;&#x644;&#x639;&#x627;&#x635;&#x645;&#x629; &#x645;&#x639;&#x64a;&#x646;",
                    "idExpiryDate" => "2031-11-02",
                    "nationality" => "YE",
                    "additionalField1" => "15",
                    "additionalField2" => "2",
                    "additionalField3" => "8",
                    "additionalField4" => "1"
                 ], 200, []),
                "{$url}/accounts/demandDeposit?status=ACTIVE&status=CLOSED&status=DORMANT" => Http::response(
                    include(base_path().'/resources/mocks/obdx/demandDeposits_get.php'),
                    200),
                "{$url}/accounts/demandDeposit/4020183415YERCUCS018" => Http::response(
                    include(base_path().'/resources/mocks/obdx/demandDeposit_get.php'),
                    200),
                "{$url}/accounts/demandDeposit/4020183415YERCUCI012" => Http::response(
                    include(base_path().'/resources/mocks/obdx/demandDeposit_get.php'),
                    200),
                //bill payment
                "{$url}/payments/currentDate" => Http::response(
                    [
                        'currentDate'=>[
                            "valueDate"=>"2025-07-06T00:00:00"
                        ]
                    ],
                    200),
                "{$url}/payments/transfers/bill" => Http::response(
                    include(base_path().'/resources/mocks/obdx/internal_post.php'),
                    200),
                "{$url}/payments/transfers/bill/*" => Http::response(
                    include(base_path().'/resources/mocks/obdx/transfer_patch.php'),
                    200),
                "{$url}/utilitypay/*" => Http::response(
                    include(base_path().'/resources/mocks/obdx/transfer_patch.php'),
                    200),
                "{$url}/yeahMoney/postpayin" => Http::response(
                    include(base_path().'/resources/mocks/obdx/yeahmoney_post.php'),
                    200),
                "{$url}/accounts/demandDeposit/*" => Http::response(
                    include(base_path().'/resources/mocks/obdx/demandDeposit_get.php'),
                    200),
                "{$url}/payments/generic" => Http::response(
                    include(base_path().'/resources/mocks/obdx/internal_post.php'),
                    200),
                "{$url}/payments/generic/*" => Http::response(
                    include(base_path().'/resources/mocks/obdx/transfer_patch.php'),
                    200),

                'https://ubp.yk-bank.com:3443/api/tp/v1/catalog*' => Http::response(
                    json_decode(\File::get(base_path( "/resources/mocks/up/catalog.json")),true),
                    200)
            ];

            if($request->filled("service") && $request->filled("subscriberNumber")){
                $json=json_decode(\File::get(base_path( "/resources/mocks/up/query.json")),true);
                if(isset($json["$request->service"])){
                    $fakes['https://ubp.yk-bank.com:3443/api/tp/v1/?*']= Http::response(
                        $json["$request->service"],
                        200);
                }

            }

            $settings=app(\App\Settings\ThirdPartySettings::class)->yeahMoney;
            if($settings->is_test){
                $settings=$settings->test_data;
            }
            $fakes["{$settings->url}/oauth/token"]= Http::response([
                "access_token"=> "feffdfs",
                "token_type"=>"Bearer",
                "expires_in"=> "3800"
            ],200);
            $fakes["{$settings->url}/mt/v1/login"]=Http::response([
                "Result_Code"=> "0",
                "Result_Status"=>"Success",
                "Result_Desc"=> "Record Successfully Retrieved"
            ],200);
            $fakes["{$settings->url}/mt/v1/payoutInfo"]= Http::response(
                include(base_path().'/resources/mocks/yeahmoney/payoutInfo_post.php'),
                200);
            $fakes["{$settings->url}/mt/v1/payout"]= Http::response(
                [
                    "Result_Code"=> "0",
                    "Result_Status"=>"Success",
                    "Result_Desc"=> "Record Successfully Retrieved",
                    "Payout_Trx_Id"=>"**********"
                ],
                200);

            $settings=app(\App\Settings\ThirdPartySettings::class)->sms;
            if($settings->is_test){
                $settings=$settings->test_data;
            }
            $fakes[$settings->url]=Http::response(Cardless::fakeSMS,200);


            $settings=app(\App\Settings\ThirdPartySettings::class)->ana;
            if($settings->is_test){
                $settings=$settings->test_data;
            }
            $fakes["{$settings->url}/banky/ana_registration"]= Http::response(
                [
                    "result_code"=> "0",
                    "result_status"=> "Success",
                    "result_desc"=> "Success",
                    "custno"=> "0555666",
                    "accountid"=> "4020555666YERSDCA010"
                ],
                200);

            $fakes+=$this->flex($request);
            $fakes+=$this->loan($request);
            $fakes+=$this->vcard($request);
            $fakes+=$this->loyalty($request);
            $fakes+=$this->wasil($request);

            Http::fake($fakes);
        }
        return $next($request);
    }





    public function flex($request)
    {
        $settings=app(\App\Settings\ThirdPartySettings::class)->cardless;
        if($settings->is_test){
            $settings=$settings->test_data;
        }

        $fakes=[
            "{$settings->url}/oauth/token" => Http::response([
                "access_token"=> "feffdfs",
                "token_type"=>"Bearer",
                "expires_in"=> "3800"
            ],200),
            "{$settings->url}/banky/post_crtrt_trx" => Http::response([
                "result_code"=> "0",
                "result_status"=>"Success",
                "result_desc"=> "Record Successfully Retrieved",
                "fccref"=>"402FTFN242340004",
            ],200),
            "{$settings->url}/banky/revers_rt" => Http::response([
                "result_code"=> "0",
                "result_status"=>"Success",
                "result_desc"=> "Successfully reverse amount to customer account"
            ],200),
            "{$settings->url}/banky/new_account" => Http::response([
                "result_code"=> "0",
                "result_status"=>"Success",
                "result_desc"=> "Successfully reverse amount to customer account",
                "accountid"=>"4020555666YERSDCA010"
            ],200),
            "{$settings->url}/banky/pc_fx_trans" => Http::response([
                    "result_code"=> "0",
                    "result_status"=>"Success",
                    "result_desc"=> "Record Successfully Retrieved",
                    "reference_no"=>"402FTFN242340004",
                    "reference_no_fee"=>"402FTRQ242340003"
                ]
                , 200, []),
            "{$settings->url}/banky/cby_clearing" => Http::response([
                "result_code" => "0",
                "result_status" => "Success",
                "result_desc" => "Success",
                "transaction_no" => "YKBA_20250205_0006",
                "linkcode" => "84414",
                "cby_resp" => [
                    "BlockRef" => "YKBA_20250205_0006",
                    "TotalRecordSuccAdded" => 1,
                    "LinkCode" => 84414,
                    "ErrorCode" => 0,
                    "ErrMessage" => ""
                ]
            ],200),
            "{$settings->url}/banky/query_charge_prod" => Http::response(
                    include(base_path().'/resources/mocks/flex/query_charge_prod_v2.php')
                , 200, []),
            "{$settings->url}/banky/query_credit_card" => Http::response([
                    "result_code"=> "0",
                    "result_status"=>"Success",
                    "result_desc"=> "Record Successfully Retrieved",
                    "cards_details"=>[
                        [
                            "request_reference_no"=>"402STAN223100001",
                            "name_on_card" => "Mohammed Qamhan",
                            "primary_card" => "Y",
                            "card_renewal_dt" => "2024-11-06",
                            "card_no" => "****************",
                            "card_product" => "MC002",
                            "card_status" => "A"
                        ],
                        [
                            "request_reference_no"=>"402STAN223100001",
                            "name_on_card" => "Mohammed Qamhan",
                            "primary_card" => "N",
                            "card_renewal_dt" => "2024-11-06",
                            "card_no" => "****************",
                            "card_product" => "V003",
                            "card_status" => "A"
                        ],
                        [
                            "request_reference_no"=>"402STAN223100001",
                            "name_on_card" => "Mohammed Qamhan",
                            "primary_card" => "Y",
                            "card_renewal_dt" => "2022-11-06",
                            "card_no" => "****************",
                            "card_product" => "V004",
                            "card_status" => "E"
                        ],
                    ]
                ]
                , 200, []),

        ];
        return $fakes;
    }
    public function loan($request)
    {
        $settings=app(\App\Settings\ThirdPartySettings::class)->loan;
        if($settings->is_test){
            $settings=$settings->test_data;
        }

        $fakes=[
            "{$settings->url}/oauth/token" => Http::response([
                "access_token"=> "feffdfs",
                "token_type"=>"Bearer",
                "expires_in"=> "3800"
            ],200),
            "{$settings->url}/api/v1/order" => Http::response([
                "resultCode" => 0,
                "resultDesc" => "Your request is being processed and you will be notified when it is completed!",
            ],200),
            "{$settings->url}/api/v1/order?customer_no*" => Http::response(
                include(base_path().'/resources/mocks/eloan/orders_get.php')
            ,200),
            "{$settings->url}/api/v1/activities" => Http::response(
                include(base_path().'/resources/mocks/eloan/activities_get.php')
            ,200),
            "{$settings->url}/api/v1/sectorTypes" => Http::response(
                include(base_path().'/resources/mocks/eloan/sectors_get.php')
            ,200),
            "{$settings->url}/api/v1/products" => Http::response(
                include(base_path().'/resources/mocks/eloan/products_get.php')
            ,200),
            "{$settings->url}/api/v1/customer?p_cust_no=*" => Http::response(
                include(base_path().'/resources/mocks/eloan/customer_get.php')
            ,200),

        ];
        return $fakes;
    }
    public function vcard($request)
    {
        $settings=app(\App\Settings\ThirdPartySettings::class)->virtualCard;
        if($settings->is_test){
            $settings=$settings->test_data;
        }

        $fakes=[
            "{$settings->url}/cards?*" => Http::response([
                "data"=>[
                    [
                        "customerId" => "YKB1000002264",
                        "applicationNumber" => "221560177971",
                        "deviceIndicator" => "P",
                        "cardPackId" => "001YKBP010000000052",
                        "deviceNumber" => ****************,
                        "expiryDate" => "1124",
                        "cvv" => "1234",
                        "walletNumber" => "2206050310380139409",
                        "walletCurrency" => 840,
                        "msisdn" => "770683888",
                        "registeredEmailId" => "<EMAIL>",
                        "embossedName" => "HAITHAM A. ALSHAMI",
                        "type" => "Primary",
                    ],
                    [
                        "customerId" => "YKB1000002267",
                        "applicationNumber" => "221560177972",
                        "deviceIndicator" => "P",
                        "cardPackId" => "001YKBP010000000053",
                        "deviceNumber" => ****************,
                        "expiryDate" => "1124",
                        "cvv" => "1234",
                        "walletNumber" => "2206050310380139410",
                        "walletCurrency" => 840,
                        "msisdn" => "770683889",
                        "registeredEmailId" => "<EMAIL>",
                        "embossedName" => "HAITHAM A. ALSHAMI",
                        "type" => "Primary",
                    ],
                ]
            ]
            , 200, [])
        ];
        return $fakes;
    }

    public function loyalty($request)
    {
        $settings=app(\App\Settings\ThirdPartySettings::class)->loyalty;
        if($settings->is_test){
            $settings=$settings->test_data;
        }

        $fakes=[
            "{$settings->url}/login" => Http::response([
                "token"=> "30|S6Rtv4bHoR2seBdyF8jQHdoOwxR2jQuxTcukDQeWa6ccecc9",
            ],200),
            "{$settings->url}/member/token" => Http::response(
                [
                   "token"=> "eyJpdiI6IlU3MUhITGRVTm4zelArdEZZZ0s1MFE9PSIsInZhbHVlIjoidzZIUFk3NTZhYXNwRFNNWmdsbHdPNGZYZTJXTHRmckE5WUdIemQrc05IUFpCQTdQbVRrdG51OWdBNUlRZDNaSWo5cE81dkE0cHl3dFo1WXlCSk9uNUl6RVNUWlFabERZZktNSStxQ05tb2hLM2s3TzZwNmVUaUlic0FzWkJxN25wS1cwZGFDTUdZRWtpdDZUbEhnQWlvVFp0bUx6VGhodWVVM2pCZ3VwQ1NKN3hTaTMzeEtWekhHTE1KOGpXd1FkaitWSElhYzVZOFNySHFmNUdnNnMrRW1iNFBxQnhIeU9tNDRuUVVYazNRMD0iLCJtYWMiOiJmMmY2NTMwMzM2MjhmODU3YmY1MGFmYjM4NWU0YmUyNTRkMGFhZTBmYTFmNzZkYjFkMmNjZDM0MTE1YzdlNmYzIiwidGFnIjoiIn0="
                ]
            , 200, []),
            "{$settings->url}/member/identifier" => Http::response(
                [
                   "token"=> "eyJpdiI6IlU3MUhITGRVTm4zelArdEZZZ0s1MFE9PSIsInZhbHVlIjoidzZIUFk3NTZhYXNwRFNNWmdsbHdPNGZYZTJXTHRmckE5WUdIemQrc05IUFpCQTdQbVRrdG51OWdBNUlRZDNaSWo5cE81dkE0cHl3dFo1WXlCSk9uNUl6RVNUWlFabERZZktNSStxQ05tb2hLM2s3TzZwNmVUaUlic0FzWkJxN25wS1cwZGFDTUdZRWtpdDZUbEhnQWlvVFp0bUx6VGhodWVVM2pCZ3VwQ1NKN3hTaTMzeEtWekhHTE1KOGpXd1FkaitWSElhYzVZOFNySHFmNUdnNnMrRW1iNFBxQnhIeU9tNDRuUVVYazNRMD0iLCJtYWMiOiJmMmY2NTMwMzM2MjhmODU3YmY1MGFmYjM4NWU0YmUyNTRkMGFhZTBmYTFmNzZkYjFkMmNjZDM0MTE1YzdlNmYzIiwidGFnIjoiIn0="
                ]
            , 200, []),
            "{$settings->url}/cards" => Http::response(
                [
                    [
                        "id" => 237728496963584,
                        "club_id" => 237359891693568,
                        "name" => "Yeah Money",
                        "head" => [
                            "en_US" => "Yeah Money"
                        ],
                        "title" => [
                            "en_US" => ""
                        ],
                        "description" => [
                            "en_US" => ""
                        ],
                        "unique_identifier" => "646-388-300-315",
                        "issue_date" => "2024-12-03T17:50:00.000000Z",
                        "expiration_date" => "2028-12-03T17:50:00.000000Z",
                        "bg_color" => "#1b6eb0",
                        "bg_color_opacity" => 75,
                        "text_color" => "#ffffff",
                        "text_label_color" => "#dedede",
                        "qr_color_light" => "#fcfcfc",
                        "qr_color_dark" => "#1f1f1f",
                        "currency" => "YER",
                        "initial_bonus_points" => 0,
                        "points_expiration_months" => 12,
                        "currency_unit_amount" => 5000,
                        "points_per_currency" => 4,
                        "point_value" => null,
                        "min_points_per_purchase" => 1,
                        "max_points_per_purchase" => 20,
                        "is_visible_by_default" => 1,
                        "is_visible_when_logged_in" => 0,
                        "total_amount_purchased" => 0,
                        "number_of_points_issued" => 0,
                        "last_points_issued_at" => null,
                        "number_of_points_redeemed" => 0,
                        "number_of_rewards_redeemed" => 0,
                        "last_reward_redeemed_at" => null,
                        "views" => 0,
                        "last_view" => null,
                        "meta" => [
                            "phone" => "",
                            "route" => "",
                            "website" => "",
                            "round_points_up" => 1
                        ],
                        "created_at" => "2024-12-03T17:59:43.000000Z",
                        "updated_at" => "2024-12-03T18:00:58.000000Z",
                        "balance" => -1
                    ],
                    [
                        "id" => ***************,
                        "club_id" => 237359891693568,
                        "name" => "Bill Payment",
                        "head" => [
                            "en_US" => "Gaming"
                        ],
                        "title" => [
                            "en_US" => ""
                        ],
                        "description" => [
                            "en_US" => ""
                        ],
                        "unique_identifier" => "589-495-685-292",
                        "issue_date" => "2024-12-02T17:43:00.000000Z",
                        "expiration_date" => "2029-02-28T17:43:00.000000Z",
                        "bg_color" => "#0033ff",
                        "bg_color_opacity" => 75,
                        "text_color" => "#ffffff",
                        "text_label_color" => "#dedede",
                        "qr_color_light" => "#fcfcfc",
                        "qr_color_dark" => "#1f1f1f",
                        "currency" => "YER",
                        "initial_bonus_points" => 0,
                        "points_expiration_months" => 12,
                        "currency_unit_amount" => 1000,
                        "points_per_currency" => 10,
                        "point_value" => null,
                        "min_points_per_purchase" => 1,
                        "max_points_per_purchase" => 100000,
                        "is_visible_by_default" => 1,
                        "is_visible_when_logged_in" => 0,
                        "total_amount_purchased" => 340000,
                        "number_of_points_issued" => 134,
                        "last_points_issued_at" => "2024-12-03T14:20:53.000000Z",
                        "number_of_points_redeemed" => 0,
                        "number_of_rewards_redeemed" => 0,
                        "last_reward_redeemed_at" => null,
                        "views" => 3,
                        "last_view" => "2024-12-03T17:22:35.000000Z",
                        "meta" => [
                            "phone" => "",
                            "route" => "",
                            "website" => "",
                            "round_points_up" => 1
                        ],
                        "created_at" => "2024-12-02T17:56:28.000000Z",
                        "updated_at" => "2024-12-03T17:50:31.000000Z",
                        "balance" => -1
                    ]
                ]
            , 200, []),
            "{$settings->url}/cards/{$request->cardId}/{$request->memberId}/transactions/purchases" => Http::response(
                [
                    "staff_id" => ***************,
                    "member_id" => ***************,
                    "card_id" => ***************,
                    "partner_name" => "Banky Lite",
                    "partner_email" => "<EMAIL>",
                    "staff_name" => "Mohammed Qamhan",
                    "staff_email" => "<EMAIL>",
                    "card_title" => [
                        "ar_SA" => "ياه موني",
                        "en_US" => "Yeah Money"
                    ],
                    "currency" => "YER",
                    "points_per_currency" => 4,
                    "meta" => [
                        "round_points_up" => true
                    ],
                    "min_points_per_purchase" => 1,
                    "max_points_per_purchase" => 40,
                    "expires_at" => "2025-12-29T17:53:10.000000Z",
                    "created_by" => ***************,
                    "purchase_amount" => null,
                    "points" => 1000,
                    "event" => "staff_credited_points",
                    "note" => null,
                    "created_at" => "2024-12-29T17:53:10.000000Z",
                    "updated_at" => "2024-12-29T17:53:10.000000Z",
                    "id" => ***************
                ]
            , 200, []),
            "{$settings->url}/cards/{$request->cardId}/{$request->memberId}/transactions/purchases" => Http::response(
                [
                   "staff_id" => ***************,
                    "member_id" => ***************,
                    "card_id" => ***************,
                    "partner_name" => "Banky Lite",
                    "partner_email" => "<EMAIL>",
                    "staff_name" => "Mohammed Qamhan",
                    "staff_email" => "<EMAIL>",
                    "card_title" => [
                        "ar_SA" => "سداد الفواتير",
                        "en_US" => "Bill Payment"
                    ],
                    "currency" => "YER",
                    "points_per_currency" => 10,
                    "meta" => [
                        "round_points_up" => true
                    ],
                    "min_points_per_purchase" => 1,
                    "max_points_per_purchase" => 100000,
                    "expires_at" => "2025-12-19T17:50:28.000000Z",
                    "created_by" => ***************,
                    "purchase_amount" => "2400000",
                    "points" => 240,
                    "event" => "staff_credited_points_for_purchase",
                    "note" => "تسديد فاتور الانترنت",
                    "created_at" => "2024-12-19T17:50:28.000000Z",
                    "updated_at" => "2024-12-19T17:50:28.000000Z",
                    "id" => 243388533813248
                ]
            , 200, [])

        ];
        return $fakes;
    }

    public function wasil($request)
    {
        $settings=app(\App\Settings\ThirdPartySettings::class)->cardless;
        if($settings->is_test){
            $settings=$settings->test_data;
        }

        $fakes=[
            "{$settings->url}/oauth/token" => Http::response([
                "access_token"=> "feffdfs",
                "token_type"=>"Bearer",
                "expires_in"=> "3800"
            ],200),
            "{$settings->url}/banky/cby/get_commission" => Http::response([
                "result_code"=> "0",
                "result_status"=> "Success",
                "commission"=> 200
            ]
            , 200, []),
            "{$settings->url}/banky/cby/mpt_verify_rmt" => Http::response([
                    "result_code"=> "0",
                    "result_status"=> "Success",
                    "transaction_reference"=> "02f1858a-1a93-8caa-e063-e29d11ac8bd0",
                    "financial_reference"=> "MPTrv230815-0006000",
                    //"cancel_reference"=> "02f824c3-b2b9-cc43-e063-e29d11ac73e2"
                ]
                , 200, []),
            "{$settings->url}/banky/cby/mpt_get_trx_by_ref" => Http::response([
                "result_code"=> "0",
                "result_status"=> "Success",
                "request"=> [
                    [
                        "req_id"=> 801,
                        "req_type"=> "MPT_CREATE_RMT",
                        "req_crt_dt_time"=> "2024-05-23T13:42:06.527389",
                        "res_mpt"=> "********",
                        "res_transactionreference"=> "191d6c18-ed87-5267-e063-652816acc718",
                        "res_status"=> "Success",
                        "res_financialreference"=> "MPT240523-0215711",
                        "res_rowversion"=> "0x000000000006AAD2",
                        "req_ref_id"=> "664f1d764d4c2",
                        "req_ref_id_instid"=> "BANKY-664f1d764d4c2",
                        "req_instid"=> "BANKY",
                        "req_agentid"=> "BANKY",
                        "req_amount"=> "1000",
                        "req_currency"=> "YER",
                        "req_recipientaccountno"=> "*********",
                        "req_senderaccountno"=> "*********",
                        "req_sendername"=> "طارق حميد يحيى الحاشدى",
                        "req_senderidno"=> "***********",
                        "req_senderidtype"=> "1",
                        "req_showsendername"=> "true",
                        "req_mpt"=> null,
                        "req_transactionreference"=> null,
                        "req_rowversion"=> null,
                        "req_collectreference"=> null,
                        "req_date"=> null,
                        "req_operation_reference"=> null
                    ]
                ],
                "response"=> [
                    [
                        "req_id"=> 801,
                        "req_ref_id"=> "664f1d764d4c2",
                        "req_type"=> "MPT_CREATE_RMT",
                        "req_cby_mpt_trans_ref"=> "191D6C18-ED87-5267-E063-652816ACC718",
                        "req_agentname"=> "BANKY",
                        "req_services"=> null,
                        "req_crt_dt_time"=> "2024-05-23T13:42:06",
                        "res_status_code"=> "201",
                        "res_status"=> "Success",
                        "res_transaction_reference"=> "191d6c18-ed87-5267-e063-652816acc718",
                        "res_sender_agent_id"=> null,
                        "res_sender_agent_name"=> null,
                        "res_mpt"=> "********",
                        "res_status_"=> null,
                        "res_amount"=> null,
                        "res_currency"=> null,
                        "res_sender_account_no"=> null,
                        "res_sender_id_no"=> null,
                        "res_sender_id_type"=> null,
                        "res_show_sender_name"=> null,
                        "res_sender_name"=> null,
                        "res_channel"=> null,
                        "res_recipient_account_no"=> null,
                        "res_creation_date"=> "2024-05-23T13:42:06.893",
                        "res_row_version"=> "0x000000000006AAD2",
                        "res_message"=> null,
                        "res_available_balance"=> null,
                        "res_current_balance"=> null,
                        "res_financial_reference"=> "MPT240523-0215711",
                        "res_cancel_reference"=> null,
                        "res_collection_date"=> null,
                        "res_collected_by"=> null,
                        "res_error"=> null
                    ]
                ]
            ]
            , 200, []),
            "{$settings->url}/banky/cby/mpt_create_rmt" => Http::response([
                    "result_code"=> "0",
                    "result_status"=> "Success",
                    "transaction_reference"=> "02f1858a-1a93-8caa-e063-e29d11ac8bd0",
                    "row_version"=> "0x000000000005B183",
                    "mpt"=> "********",
                    "creation_date"=> "2023-08-15T17:22:40.923",
                    "financial_reference"=> "MPT230815-0005998"
                ]
                , 200, []),
            "{$settings->url}/banky/cby/mpt_find_rmt" => Http::response([
                "result_code"=> "0",
                "result_status"=> "Success",
                "transaction_reference"=> "02f1858a-1a94-8caa-e063-e29d11ac8bd0",
                "row_version"=> "0x000000000005B184",
                "sender_agent_id"=> "****************",
                "sender_agent_name"=> "مدفوعات اليمن الكويت ريال",
                "mpt"=> "********",
                "status"=> "1",
                "amount"=> "100",
                "currency"=> "YER",
                "sender_account_no"=> "0173946",
                "sender_id_no"=> "***********",
                "sender_id_type"=> "1",
                "show_sender_name"=> "true",
                "sender_name"=> "thiyazen",
                "recipient_account_no"=> "*********",
                "creation_date"=> "2023-08-15T17:33:14.533"
            ]
            , 200, []),
            "{$settings->url}/banky/cby/mpt_collect_rmt" => Http::response([
                "result_code"=> "0",
                "result_status"=> "Success",
                "transaction_reference"=> "02f1858a-1a94-8caa-e063-e29d11ac8bd0",
                "financial_reference"=> "02f1858a-1a94-8caa-e063-e29d11ac43d2"
            ]
            , 200, []),
            "{$settings->url}/banky/cby/mpt_cancel_rmt" => Http::response([
                "result_code"=> "0",
                "result_status"=> "Success",
                "operation_reference"=> "02f824c3-b2b9-cc43-e063-e29d11ac73e2",
                "transaction_reference"=> "02f1858a-1a93-8caa-e063-e29d11ac8bd0",
                "financial_reference"=> "MPTrv230815-0006000"
            ]
            , 200, []),
            "{$settings->url}/banky/cby/mpt_get_expired_rmts" => Http::response([
                "result_code"=> "0",
                "result_status"=> "Success",
                "transactions"=> [
                    [
                        "transaction_reference"=> "1885db5d-41db-0da0-e063-652816ac2156",
                        "expiry_date"=> "2024-05-17T01:38:03.517",
                        "row_version"=> "0x0000000000069620"
                    ]
                ]
            ]
            , 200, []),
        ];
        return $fakes;
    }
}
