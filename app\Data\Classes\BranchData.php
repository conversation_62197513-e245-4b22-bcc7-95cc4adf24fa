<?php

namespace App\Data\Classes;

use <PERSON><PERSON>\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Data;

class BranchData{
    public static function values():array {
        return app(\App\Settings\ConfigSettings::class)->branchConfig->values;

    }
    public static function north():array {
        return app(\App\Settings\ConfigSettings::class)->branchConfig->north;
    }
    public static function south():array {
        return app(\App\Settings\ConfigSettings::class)->branchConfig->south;

    }
    public static function sanaaBranches():array {
        return app(\App\Settings\ConfigSettings::class)->branchConfig->sanaaBranches;

    }
}

