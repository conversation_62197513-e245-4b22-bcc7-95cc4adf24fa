<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains performance-related configurations for the application.
    | These settings help optimize the application for better speed and efficiency.
    |
    */

    'cache' => [
        /*
        |--------------------------------------------------------------------------
        | Default Cache Store
        |--------------------------------------------------------------------------
        |
        | This option controls the default cache connection that gets used while
        | using this caching library. This connection is used when another is
        | not explicitly specified when executing a given caching function.
        |
        */
        'default' => env('CACHE_DRIVER', 'redis'),

        /*
        |--------------------------------------------------------------------------
        | Cache Stores
        |--------------------------------------------------------------------------
        |
        | Here you may define all of the cache "stores" for your application as
        | well as their drivers. You may even define multiple stores for the
        | same cache driver to group types of items stored in your caches.
        |
        */
        'stores' => [
            'redis' => [
                'driver' => 'redis',
                'connection' => 'cache',
                'lock_connection' => 'default',
            ],
            'apcu' => [
                'driver' => 'apcu',
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Cache Key Prefix
        |--------------------------------------------------------------------------
        |
        | When utilizing a RAM based store such as APC or Memcached, there might
        | be other applications utilizing the same cache. So, we'll specify a
        | value to get prefixed to all our keys so we can avoid collisions.
        |
        */
        'prefix' => env('CACHE_PREFIX', 'banky_cache'),
    ],

    'session' => [
        /*
        |--------------------------------------------------------------------------
        | Session Driver
        |--------------------------------------------------------------------------
        |
        | This option controls the default session "driver" that will be used on
        | requests. By default, we will use the lightweight native driver but
        | you may specify any of the other wonderful drivers provided here.
        |
        */
        'driver' => env('SESSION_DRIVER', 'redis'),

        /*
        |--------------------------------------------------------------------------
        | Session Lifetime
        |--------------------------------------------------------------------------
        |
        | Here you may specify the number of minutes that you wish the session
        | to be allowed to remain idle before it expires. If you want them
        | to immediately expire on the browser closing, set that option.
        |
        */
        'lifetime' => env('SESSION_LIFETIME', 120),

        /*
        |--------------------------------------------------------------------------
        | Session Connection
        |--------------------------------------------------------------------------
        |
        | When using the "database" or "redis" session drivers, you may specify a
        | connection that should be used to manage these sessions. This should
        | correspond to a connection in your database configuration options.
        |
        */
        'connection' => env('SESSION_CONNECTION', 'default'),
    ],

    'queue' => [
        /*
        |--------------------------------------------------------------------------
        | Default Queue Connection Name
        |--------------------------------------------------------------------------
        |
        | Laravel's queue API supports an assortment of back-ends via a single
        | API, giving you convenient access to each back-end using the same
        | syntax for every one. Here you may define a default connection.
        |
        */
        'default' => env('QUEUE_CONNECTION', 'redis'),

        /*
        |--------------------------------------------------------------------------
        | Queue Connections
        |--------------------------------------------------------------------------
        |
        | Here you may configure the connection information for each server that
        | is used by your application. A default configuration has been added
        | for each back-end shipped with Laravel. You are free to add more.
        |
        */
        'connections' => [
            'redis' => [
                'driver' => 'redis',
                'connection' => 'default',
                'queue' => env('REDIS_QUEUE', 'default'),
                'retry_after' => 90,
                'block_for' => null,
                'after_commit' => false,
            ],
        ],
    ],

    'database' => [
        /*
        |--------------------------------------------------------------------------
        | Database Query Logging
        |--------------------------------------------------------------------------
        |
        | By default, the underlying database queries executed by Eloquent are
        | logged. However, in some situations, you may want to disable this
        | logging. You may do so using the "log" configuration option.
        |
        */
        'log' => env('DB_LOG_QUERIES', false),

        /*
        |--------------------------------------------------------------------------
        | Database Connection Pooling
        |--------------------------------------------------------------------------
        |
        | When using Oracle database, connection pooling can significantly
        | improve performance by reusing database connections.
        |
        */
        'pool' => [
            'enabled' => env('DB_POOL_ENABLED', true),
            'min_connections' => env('DB_POOL_MIN', 5),
            'max_connections' => env('DB_POOL_MAX', 20),
        ],
    ],

    'opcache' => [
        /*
        |--------------------------------------------------------------------------
        | OPcache Settings
        |--------------------------------------------------------------------------
        |
        | These settings control OPcache behavior for better performance.
        | OPcache stores precompiled script bytecode in shared memory.
        |
        */
        'enabled' => env('OPCACHE_ENABLE', true),
        'memory_consumption' => env('OPCACHE_MEMORY_CONSUMPTION', 256),
        'max_accelerated_files' => env('OPCACHE_MAX_ACCELERATED_FILES', 20000),
        'revalidate_freq' => env('OPCACHE_REVALIDATE_FREQ', 0),
        'jit_buffer_size' => env('OPCACHE_JIT_BUFFER_SIZE', '100M'),
    ],

    'optimization' => [
        /*
        |--------------------------------------------------------------------------
        | Route Caching
        |--------------------------------------------------------------------------
        |
        | Route caching can significantly improve performance by caching
        | the route definitions. This is especially useful in production.
        |
        */
        'route_cache' => env('ROUTE_CACHE_ENABLED', true),

        /*
        |--------------------------------------------------------------------------
        | Config Caching
        |--------------------------------------------------------------------------
        |
        | Configuration caching can improve performance by caching all
        | configuration files into a single file.
        |
        */
        'config_cache' => env('CONFIG_CACHE_ENABLED', true),

        /*
        |--------------------------------------------------------------------------
        | View Caching
        |--------------------------------------------------------------------------
        |
        | View caching compiles all Blade templates and caches them
        | for faster rendering.
        |
        */
        'view_cache' => env('VIEW_CACHE_ENABLED', true),

        /*
        |--------------------------------------------------------------------------
        | Event Caching
        |--------------------------------------------------------------------------
        |
        | Event caching can improve performance by caching event listeners
        | and their mappings.
        |
        */
        'event_cache' => env('EVENT_CACHE_ENABLED', true),
    ],

    'monitoring' => [
        /*
        |--------------------------------------------------------------------------
        | Performance Monitoring
        |--------------------------------------------------------------------------
        |
        | Enable performance monitoring to track slow queries and requests.
        |
        */
        'enabled' => env('PERFORMANCE_MONITORING', false),
        'slow_query_threshold' => env('SLOW_QUERY_THRESHOLD', 1000), // milliseconds
        'slow_request_threshold' => env('SLOW_REQUEST_THRESHOLD', 2000), // milliseconds
    ],
];
