<?php

namespace App\Models;

use App\CacheModel;
use App\Data\AppConfigData;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Story extends CacheModel
{   

    use HasFactory;
    protected $table = 'stories';

    protected $fillable = [
        "id",
        "name",
        "icon",
        "items",
        "status"
    ];
  
    protected $casts = [
        'items' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'name','status','created_at','updated_at'
    ];
   
    public static function getAvailableStory(){
        return static::getWithCache("story", function ($query) {
            return static::select("id","icon","items")  
            ->where('status',1)
            ->orderBy('created_at','desc')
            ->first();
        });
    }
  
}
