<?php

namespace App\Traits;

use App\Data\StatusData;
use App\Enums\ServiceTagEnum;
use App\Jobs\ProcessLoyaltyAddTransaction;
use App\Models\CustomerType;
use App\Models\Service;

trait AuthorizesServices
{
    protected ?Service $service;
    protected function bootAuthorizesServices(): void
    {
        $this->middleware(function ($request, $next) {
            // This will run automatically if hooked from the base controller
            // You can set or override properties, etc.
            $this->available(/*$this->requiredAllPrivilege()*/);

            return $next($request);
        });
         // Defer until request is available


    }
      // Force the using class to implement this
    /**
     * @return ServiceTagEnum[]
     */
    abstract protected function getServiceTags(): array;
    // public function getServiceTag(): ?ServiceTagEnum
    // {
    //     return null;
    // }
    // public function getAuthorizedCustomerTypes(): array
    // {

    //     return [
    //         CustomerType::RETAIL,
    //         CustomerType::CORPORATE,
    //         CustomerType::AGENT,
    //         CustomerType::GUEST,
    //         CustomerType::UNAUTHENTICATED,
    //         CustomerType::BUSINESS
    //     ];
    // }
    // public function requiredAllPrivilege(): bool
    // {
    //     return true;
    // }
    /**
     * Authorize a given action for the current user.
     *
     */

    /**
     * Store a newly created resource in storage.
     *
     * @param  ?ServiceTagEnum[] $overrideTag
     */
    public function available($overrideTag=null)
    {
        $generalConfig=app(\App\Settings\ConfigSettings::class)->generalConfig;
        if($generalConfig->authorizeServices!=1){
            return;
        }

        $tags=$overrideTag??$this->getServiceTags();
        if(empty($tags) || count($tags)>1 ){
            return;
        }else{
            $tag=$tags[0];
        }
        $this->service = Service::getServiceWithTag($tag->value);
        if(!is_null($this->service) && !is_null($this->service->serviceCustomerType?->status)
            && in_array($this->service->serviceCustomerType?->status,[1,3])){
            return;
        }
        return abort(response()->json(StatusData::from([
            "result"    => "SUCCESSFUL",
            "contextID" => "MIDDLEWARE",
            "message"   => [
                "title"   => __("System cannot process the request currently. Please try later."),
                "detail"  => "Authorizes",
                "code"    => "DIGX_PROD_DEF_0000",
                "type"    => "ERROR",
                "relatedMessage"=> [
                    [
                        "detail"=> "Access denied.",
                        "code"=> "FC_SM_025"
                    ]
                ],
            ]
        ]),\Symfony\Component\HttpFoundation\Response::HTTP_INTERNAL_SERVER_ERROR));
        // if(in_array($user->customerType,$this->getAuthorizedCustomerTypes()) ){
        //     if(($user->customerType==CustomerType::CORPORATE && ($user->allPrivilege || !$requiredAllPrivilege)) || $user->customerType!=CustomerType::CORPORATE){
        //         return;
        //     }
        // }
        // return abort(response()->json(StatusData::from([
        //     "result"    => "SUCCESSFUL",
        //     "contextID" => "MIDDLEWARE",
        //     "message"   => [
        //         "title"   => __("System cannot process the request currently. Please try later."),
        //         "detail"  => "",
        //         "code"    => "DIGX_PROD_DEF_0000",
        //         "type"    => "ERROR",
        //         "relatedMessage"=> [
        //             [
        //                 "detail"=> "Access denied.",
        //                 "code"=> "FC_SM_025"
        //             ]
        //         ],
        //     ]
        // ])));
        //abort(\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN);
    }

    public function loyalty($amount,$note="")
    {
        if(auth()?->user()?->id=="0183415"){
            if(!empty($this->service?->loyalty_card_id??"")){
                $user=auth()->user();
                $object=new \stdClass();
                $object->id=$user->id;
                $object->name=$user->name;
                $object->email= $user->email;
                $object->phone= $user->phone;

                ProcessLoyaltyAddTransaction::dispatch( $object,$amount,$note??"",$this->service->loyalty_card_id)
                ->onQueue('critical');
            }
        }


    }
}
