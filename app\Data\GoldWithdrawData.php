<?php

namespace App\Data;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class GoldWithdrawData extends Data
{
    #[MapInputName('req_ref_id')]
    public ?string $id;

    public ?AccountIdData $debitAccountId;

    #[MapInputName('crt_dt_time')]
    public ?string $date;

    #[MapInputName('brn_cd')]
    public ?string $branchId;
    public ?CurrencyAmountData $amount;

    public ?string $status;

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['debitAccountId']= [
            "value"=>$properties["accountid"],
            "displayValue"=>$properties["accountid"],
        ];
        $properties['amount']= [
            "amount"=>$properties["amount"],
            "currency"=>$properties["currency"],
        ];
        return $properties;
    }

}
