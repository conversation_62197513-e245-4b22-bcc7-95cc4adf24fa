<?php

namespace App\Models;

use App\Enums\InvoiceTransactionsTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceTransaction extends Model
{
    use HasFactory;
    protected $fillable = ['invoice_id', 'status', 'type','amount','reference_id', 'external_reference_id', 'remarks','gift_id', 'gift_transaction_id'];
    protected $casts = [
        'amount' => 'object',
        'type' => InvoiceTransactionsTypeEnum::class,
    ];

    public function invoice()
    {
        return $this->belongsTo('App\Models\Invoice');
    }
    public function gift()
    {
        return $this->belongsTo('App\Models\Gift');
    }

    public function giftTransaction()
    {
        return $this->belongsTo('App\Models\GiftTransaction');
    }
}
