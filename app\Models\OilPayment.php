<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
class OilPayment extends Model
{
    use HasFactory;
    protected $fillable = ['party_id','reference_id','external_reference_id','transaction_id','status','type','oil_region_id','customer_account_id','debit_account_id'/*,'credit_account_id'*/,'amount','fee','exchange_rate','remarks',"payment_result"];
    protected $casts = [
        'customer_account_id'   => 'object',
        //'credit_account_id'     => 'object',
        'debit_account_id'      => 'object',
        'amount'                => 'object',
        'fee'                   => 'object',
        'exchange_rate'         => 'object',
        'payment_result'        => 'object',
        'created_at'            => 'datetime:Y-m-d H:i:s',
        'updated_at'            => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn','oil_region_id'
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            $query->party_id = auth()->user()->id;
        });
    }

    protected function serializeDate(DateTimeInterface $date){
        return $date->timezone('Asia/Aden')->format('Y-m-d H:i:s');
    }
    public function region()
    {
        return $this->hasOne(OilRegion::class,'id','oil_region_id');
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', 'model_id')->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}
