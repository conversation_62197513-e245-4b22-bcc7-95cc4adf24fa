<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Telescope\EntryResult;
use <PERSON><PERSON>\Telescope\Storage\EntryModel;
use <PERSON><PERSON>\Telescope\Storage\EntryQueryOptions;

class MyTelescopeEntry extends EntryModel
{
    protected $connection= 'oracle';

    protected $casts = [
        'content' => 'object',
    ];
    public function find($id): mixed
    {
        $entry = static::on($this->connection)->with('relatedEntries')->with('tags')->whereUuid($id)->firstOrFail();

        // $tags = \DB::table('telescope_entries_tags')
        //                 ->where('entry_uuid', $id)
        //                 ->pluck('tag')
        //                 ->all();
        //$entry->tags=$tags;
        return $entry;
        // return new EntryResult(
        //     $entry->uuid,
        //     null,
        //     $entry->batch_id,
        //     $entry->type,
        //     $entry->family_hash,
        //     $entry->content,
        //     $entry->created_at,
        //     $tags
        // );
    }
    /**
     * Scope the query for the given type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \Laravel\Telescope\Storage\EntryQueryOptions  $options
     * @return $this
     */
    protected function whereTag($query, EntryQueryOptions $options)
    {
        $query->when($options->tag, function ($query, $tag) {
            $tags = collect(explode(',', $tag))->map(fn ($tag) => trim($tag));

            if ($tags->isEmpty()) {
                return $query;
            }
            $query->exists(function ($query) use ($tags) {
                $query->select('entry_uuid')
                ->from('telescope_entries_tags')
                ->whereIn('tag', $tags->all());
            });
            return $query->whereIn('uuid', function ($query) use ($tags) {
                $query->select('entry_uuid')
                ->from('telescope_entries_tags');
                if(count($tags)>1)
                    $query->whereIn('tag', $tags->all());
                else
                    $query->where('tag', $tags->first());
            });
        });

        return $this;
    }

    public function relatedEntries()
    {
        return $this->hasMany(MyTelescopeEntry::class, "batch_id","batch_id")->where('type','<>','request');
    }

    public function parent()
    {
        return $this->hasOne(MyTelescopeEntry::class, "batch_id","batch_id")->where('type','request');
    }

    public function tags()
    {
        return $this->hasMany(MyTelescopeEntryTag::class,"entry_uuid","uuid");
    }
}
