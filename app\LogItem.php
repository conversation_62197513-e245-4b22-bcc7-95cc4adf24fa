<?php

namespace App;

use App\Models\LogEntry;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Telescope\EntryType;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;

class LogItem
{
    /**
     * Log process.
     *
     * @param  object $item
     * @return void
     */
    public static function record($item){
        Telescope::afterRecording(function ($telescope, IncomingEntry $entry) use ($item){
            //return true;

            $logEntry=$entry->toArray();
            $logEntry["content"]=$entry->content;//json_encode($entry->content, JSON_INVALID_UTF8_SUBSTITUTE);
            $logEntry["model"]=static::class;
            $logEntry["model_id"]=$item->id;
            $logEntry["batch_id"]=\Str::orderedUuid()->toString();

            LogEntry::create(
                $logEntry
            );
        });
    }
    public static function store($item){
        $generalConfig=app(\App\Settings\ConfigSettings::class)->generalConfig;
        if(!$generalConfig->operationsLogging){
            return;
        }
        array_unshift(Telescope::$afterStoringHooks,function ($entries, $batchId) use ($item){
            if(is_null($item)|| is_null($item->id)){
                \Log::warning("Item not logged cause it {id}",[['id' => $item->id??null]]);
                return;
            }
            \Log::alert("Item with {id} logged",[['id' => $item->id]]);

            $entries= collect($entries);
            [$exceptions, $entries] = $entries->partition->isException();

            static::storeExceptions($item,$batchId,$exceptions);

            $entries=$entries->filter(function ($entry)  {
                return in_array($entry->type,[EntryType::CLIENT_REQUEST,EntryType::EXCEPTION,EntryType::REQUEST,EntryType::JOB,EntryType::QUERY]);
            });
            $entries->chunk(10)->each(function ($chunked) use ($item,$batchId) {
                LogEntry::insert($chunked->map(function ($entry) use ($item,$batchId) {
                    //$entry->content = json_encode($entry->content, JSON_INVALID_UTF8_SUBSTITUTE);
                    $logEntry=$entry->toArray();
                   // $logEntry["content"]=json_decode(json_encode($logEntry["content"], JSON_INVALID_UTF8_SUBSTITUTE));
                    $logEntry["batch_id"]= $batchId;
                    $logEntry["model"]=get_class($item);
                    $logEntry["model_id"]=$item->id;
                    $logEntry["tags"]=json_encode($entry->tags);
                    return $logEntry;
                })->toArray());
            });
        });
        //Telescope::$afterStoringHooks[]=;
        // Telescope::afterStoring(function (array $entries, $batchId) use ($item){
        //     collect($entries)->chunk(10)->each(function ($chunked) use ($item,$batchId) {
        //         LogEntry::insert($chunked->map(function ($entry) use ($item,$batchId) {
        //             //$entry->content = json_encode($entry->content, JSON_INVALID_UTF8_SUBSTITUTE);
        //             $logEntry=$entry->toArray();
        //             $logEntry["batch_id"]= $batchId;
        //             $logEntry["model"]=get_class($item);
        //             $logEntry["model_id"]=$item->id;
        //             return $logEntry;
        //         })->toArray());
        //     });
        // });
    }

     /**
     * Store the given array of exception entries.
     *
     * @param  \Illuminate\Support\Collection|\Laravel\Telescope\IncomingEntry[]  $exceptions
     * @return void
     */
    public static function storeExceptions($item,$batchId,Collection $exceptions)
    {
        $exceptions->chunk(10)->each(function ($chunked) use ($item,$batchId){
            LogEntry::insert($chunked->map(function ($exception) use ($item,$batchId) {
                //$entry->content = json_encode($entry->content, JSON_INVALID_UTF8_SUBSTITUTE);
                $logEntry=array_merge($exception->toArray(), [
                    'family_hash' => $exception->familyHash(),
                    'content' => json_encode(array_merge(
                        $exception->content, ['occurrences' => 0]
                    )),
                ]);
                //$logEntry["content"]=json_decode(json_encode($logEntry["content"], JSON_INVALID_UTF8_SUBSTITUTE));
                $logEntry["batch_id"]= $batchId;
                $logEntry["model"]=get_class($item);
                $logEntry["model_id"]=$item->id;
                $logEntry["tags"]=json_encode($exception->tags);
                return $logEntry;
            })->toArray());
        });
    }
}

