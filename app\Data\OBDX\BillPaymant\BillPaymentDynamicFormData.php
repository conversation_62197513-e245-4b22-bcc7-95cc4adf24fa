<?php

namespace App\Data\OBDX\BillPayment;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\BaseNonNullableDataCollection;
use App\Data\CurrencyAmountData;
use App\Data\Form\FormBaseData;
use App\Data\Form\FormFieldData;
use App\Data\Form\FormHeaderData;
use App\Data\Form\FormOptionData;
use App\Data\GeneralResponseData;
use App\Data\NameData;
use App\Models\BillPaymentBundle;
use App\Models\BillPaymentItem;
use Illuminate\Support\Facades\Lang;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\DataCollection;


class BillPaymentDynamicFormData extends BaseNonNullableData
{
    public function __construct(
        #[MapOutputName('data'),DataCollectionOf(FormBaseData::class)]
        public ?DataCollection $fields=null,
    ) {
    }

    public static function from(mixed ...$payloads) : static
    {
        $data=$payloads[0];
        $billPayment=new BillPaymentDynamicFormData();
        $billPayment->getResult(request()->service,$data->fields,$data->filters,$data,$data->items,1);
        return $billPayment;
    }


    protected function getResult($service,$mergedFields,$filters,$parent,$items,$level)
    {
        $mergedFields=$mergedFields->toCollection();
        foreach ($mergedFields as $mergedField) {
            if($mergedField->name=="service"){
                $mergedField->value=$service;
                break;
            }
        }
        $mergedFields=FormBaseData::collect($mergedFields,BaseNonNullableDataCollection::class);
        $this->fields=$this->getFilters($mergedFields,$filters,$parent,$items,$level);
    }

    protected function getFilters($mergedFields,$filters,$parent,$items,$level)
    {
        //Log::debug(count($filters));
        if(is_null($filters) || !count($filters)){
            if($level==1){
                if(!count($items)){
                    return null;

                }
                $result=[];
                $filteredField=new FormFieldData(
                    id:"",
                    label:new NameData(
                        ar:Lang::get("Type", locale: 'ar'),
                        en:"Type"
                    ),
                    element:'select'
                );
                $mergedFields=$mergedFields->toCollection();
                foreach ($mergedFields as $mergedField) {
                    if($mergedField->name=="item"){
                        $filteredField=$mergedField->copy();
                        break;
                    }else {
                        $otherField=$mergedFields->shift();
                        $otherField->id=$parent->id;
                        if(!($otherField->element=='select' && is_null($otherField->options))){
                            $result[]=$otherField;
                        }
                    }
                }
                if($filteredField->element=="grid" && count($items)<=1){
                    $filteredField->element="filter";
                }
                $mergedFields=FormBaseData::collect($mergedFields,BaseNonNullableDataCollection::class);
                if(!count($items)){
                    $filteredField->element='hidden';
                    $filteredField->value="{$parent->id}";
                    $result[]=$filteredField;
                    $result= array_merge($result,$this->getFields($mergedFields,$parent));
                }else{
                    if(count($items)<2){
                        $filteredField->value="{$items->first()->id}";
                    }
                    $filteredField->options=FormOptionData::collect($items->map(function($item)use($mergedFields,$parent,$filteredField){
                        $_mergedFields=$this->mergeFields($mergedFields,$item->fields??null);
                        return new FormOptionData(
                            id:$item->id,
                            name:new NameData(
                                ar:$item->title->ar,
                                en:$item->title->en
                            ),
                            image:$item->image,
                            subtitle:is_null($item->payload?->denAmount??null)?null:"{$item->payload?->denAmount} {$item->payload?->currency}",
                            description:$item->payload?->denDesc??null,
                            url:isset($parent->url) && !is_null($parent->url)?sprintf("$parent->url?title=%s",$parent->id,$item->id,$item->name):null,
                            fields:$filteredField->element=='grid'?null:FormBaseData::collect($this->loadItemFilter($_mergedFields,$item),BaseNonNullableDataCollection::class)
                        );
                    }),BaseNonNullableDataCollection::class);
                    $result[]=$filteredField;
                }

                //abort(response()->json(FormFieldData::collect($result,BaseNonNullableDataCollection::class)));

            }else{

                $result= $this->getItems($mergedFields,$items,$level);
            }
        }else{
            $result= $this->getFilterView($mergedFields,$filters,$parent,$items,$level);
        }
        return FormBaseData::collect($result,BaseNonNullableDataCollection::class);
    }

    protected function getFilterView($mergedFields,$filters,$parent,$items,$level): array
    {
        $result=[];
        foreach ($filters as $filter) {
            if($filter->type=="header"){
                foreach ($filter->options as $option) {
                    $_items=$items->filter(function($item)use($option){
                        return collect($item->options)->contains(function ($_option) use($option) {
                            return $_option->id==$option->id;
                        });
                    })->values();
                    if(count($_items))
                    $result=[
                        ...$result,
                        new FormHeaderData(
                            element:"header",
                            backgroundColor:"#2196F3",
                            foregroundColor:"#ffffff",
                            //name:$option->name
                            name:new NameData(
                                ar:$option->title->ar,
                                en:$option->title->en
                            ),
                        ),
                        ...($this->getFilters($mergedFields,$filter->filters,$parent,$_items,$level)??[])
                    ];
                }
                 //abort(response()->json(FormBaseData::collect($result,BaseNonNullableDataCollection::class)->toArray()));
            }else{
                $result=[];
                // $filteredField=new FormFieldData(
                //     id:"",
                //     label:__("Type"),
                //     element:$parent->view_type??('select')
                // );
                $mergedFields=$mergedFields->toCollection();
                foreach($mergedFields as $mergedField) {
                    if($mergedField->name=="item"){
                        //$filteredField=$mergedField->copy();
                        break;
                    }else {
                        //$result[]=$mergedFields->shift();
                        $otherField=$mergedFields->shift();
                        $otherField->id=$parent->id;
                         if(!($otherField->element=='select' && is_null($otherField->options))){
                            $result[]=$otherField;
                        }
                    }
                }
                $mergedFields=FormBaseData::collect($mergedFields,BaseNonNullableDataCollection::class);

                $filterItem= new FormFieldData(
                    id:$filter->id,
                    label:$filter->show_title?new NameData(
                        ar:$filter->title->ar,
                        en:$filter->title->en
                    ):null,
                    name:$filter->key_name??"filter{$filter->id}",
                    element:$filter->type??'filter',
                    //value: "{$filter->options->first()->id}",
                    options:count($filter->options)<=0?null:FormOptionData::collect($filter->options->map(function($option)use($mergedFields,$filter,$parent,$items,$level){
                        $_items=$items->filter(function($item)use($option){
                            return collect($item->options)->contains(function ($_option) use($option) {
                                return $_option->id==$option->id;
                            });
                        })->values();
                        return new FormOptionData(
                            id:$option->key_value??$option->id,
                            name:new NameData(
                                ar:$option->title->ar,
                                en:$option->title->en
                            ),
                            fields:$this->getFilters($mergedFields,$filter->filters,$parent,$_items,$level)
                        );
                    })->filter(function($option){
                        return !is_null($option->fields) && count($option->fields);
                    })->values(),BaseNonNullableDataCollection::class)
                );
                //abort(response()->json($filterItem));
                $filterItem->value=$filterItem->options->first()->id;
                $result[]=$filterItem;
            }
        }
        return $result;
    }

    protected function getItems($mergedFields,$items,$level):array
    {
        $result=[];
        if($level==2){
            $formField=new FormFieldData(
                id:"",
                name:"bundle",
                element:'chip',
                value: "{$items->first()?->id}",
                options:FormOptionData::collect($items->map(function($item)use($mergedFields){
                    return new FormOptionData(
                        id:$item->id,
                        name:new NameData(
                            ar:$item->title->ar,
                            en:$item->title->en
                        ),
                        subtitle:$item->payload?->denId=="000000"?null:"{$item->amount?->amount} {$item->amount?->currency}",
                        description:$item->payload?->denDesc??null,
                        fields:FormBaseData::collect($this->getFields($mergedFields,$item),BaseNonNullableDataCollection::class)
                    );
                }),BaseNonNullableDataCollection::class)
            );
            if(count($formField->options)){
                $result[]=$formField;
            }
            //abort(response()->json(FormFieldData::collect($result,BaseNonNullableDataCollection::class)->toArray()));
        }else{
            foreach ($items as $item) {
                $_mergedFields=$this->mergeFields($mergedFields,$item->fields);
                $result= array_merge($result,$this->loadItemFilter($_mergedFields,$item));//$result->merge($this->loadItemFilter($item)->toCollection());
            }
            //abort(response()->json($result));

        }
        return $result;//FormFieldData::collect($result,BaseNonNullableDataCollection::class);
    }

    protected function loadItemFilter($mergedFields,$item):?array
    {

        if(is_null($item)){
            return null;
        }

        if(count($item->bundles??[])){
            $itemId=$item->id;
            $item->load(['filters' => function ($query)use ($itemId) {
                $query->whereNull('parent_filter_id')->with([
                    'filters' => function ($query) use ($itemId) {
                        $query->with([
                            'filters' => function ($query) use ($itemId) {
                                $query->with('options')
                                ->where('model_type', BillPaymentItem::class)
                                ->where('model_id', $itemId);
                            }
                        ])
                        ->with('options')
                        ->where('model_type', BillPaymentItem::class)
                        ->where('model_id', $itemId);
                    }
                ])->with('options');
            }]);
            return [...$this->getFilters($mergedFields,$item->filters,$item,$item->bundles,2)];
        }else{
            return $this->getFields($mergedFields,$item);
        }
    }
    protected function getFields($mergedFields,$item):?array
    {

        if(is_null($item)){
            return null;
        }
        $mergedFields=$mergedFields->toCollection()->filter(function($field){
            return  !($field->name=='item' || ($field->element=='select' && is_null($field->options)));
        });
        // $mergedFields=$mergedFields->toCollection()->filter(function($field){
        //     return $field->name!='item' || !($field->element=='select' && !count($field->options));
        // });
        if($item instanceof BillPaymentItem){
            $mergedFields=$mergedFields->each(function(&$field)use($item){
                if($field->name=='biller'){
                    $field->value="{$item->biller}";
                }
                if($field->name=='billerCategory'){
                    $field->value="{$item->biller_category}";
                }
            });
            if(isset($item->payload->allowOpenAmount) && $item->payload->allowOpenAmount==true){
                $mergedFields=$mergedFields->filter(function($field){
                    return $field->name!='amount:currency';
                });
            }else{
                $mergedFields=$mergedFields->filter(function($field){
                    return $field->name!='amount:amount';
                });
            }

        }else{
            if($item instanceof BillPaymentBundle){
                if(($item->payload?->denId??"000000")!="000000"){
                    $mergedFields=$mergedFields->filter(function($field){
                        return $field->name!='amount:amount';
                    });
                }
            }
        }
        $mergedFields=$mergedFields->each(function(&$field)use($item){
            $field->id="{$item->id}";
        });

        // if(($item->payload?->denId??"000000")!="000000"){
        //     $mergedFields=$mergedFields->filter(function($field){
        //        return $field->name!='amount:amount';
        //     });
        // }else{

            // if(!(isset($item->additional_fields) && !is_null($item->additional_fields) && count($item->additional_fields))){
            //     $mergedFields=$mergedFields->filter(function($field)use($item){
            //         return $field->name!='amount:currency';
            //     });
            // }

        //}
        return [
            ...$mergedFields
        ];
    }

    protected function mergeFields(DataCollection $parents,?DataCollection $childs):?BaseNonNullableDataCollection
    {
        //abort(response()->json($parents->toCollection()),400);
        if(is_null($childs)){
            return $parents;
        }
        $childs=$childs->toCollection();

        $fields = $parents->toCollection()->map(function ($field) use ($childs) {
            $child=$childs->firstWhere('name', $field->name);

            if (!is_null($child)) {
                return $child->copy();
            }
            return $field->copy();
        });

        return FormBaseData::collect($fields,BaseNonNullableDataCollection::class);
    }
}
