<?php

namespace App\Settings;
use App\Data\ThirdPartyData;
use App\Data\ThirdPartyServiceNameData;
use Spatie\LaravelSettings\Settings;

class ThirdPartySettings extends Settings
{
    public ThirdPartyData $loan;
    public ThirdPartyData $cardless;
    public ThirdPartyServiceNameData $serviceName;
    public ThirdPartyData $yeahMoney;
    public ThirdPartyData $virtualCard;
    public ThirdPartyData $ana;
    public ThirdPartyData $utilityPayement;
    public ThirdPartyData $agent;
    public ThirdPartyData $sms;
    public ThirdPartyData $obdxAdmin;
    public ThirdPartyData $loyalty;
    public ThirdPartyData $obdxBanky;

    public static function group(): string
    {
        return 'thirdParty';
    }

    public static function encrypted(): array
    {
        return [
            'loan',
            'cardless',
            'yeahMoney',
            'virtualCard',
            'ana',
            'utilityPayement',
            'agent',
            'sms',
            'serviceName',
            'obdxAdmin',
            'loyalty',
            'obdxBanky'
        ];
    }
}
