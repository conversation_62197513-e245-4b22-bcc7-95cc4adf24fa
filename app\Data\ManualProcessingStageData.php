<?php

namespace App\Data;

use Spatie\LaravelData\Data;

class ManualProcessingStageData extends Data
{
    public function __construct(
        public int $stage,
        public string $title,
        public string $description,
        public bool $isCompleted,
        public bool $isCurrent,
        public ?string $completedAt,
        public ?string $notes
    ) {}

    public static function getStagesForHarvest($harvest): array
    {
        $currentStage = $harvest->manual_stage ?? 1;
        $status = $harvest->status;

        $stages = [
            1 => [
                'title' => 'Customer Verification',
                'description' => 'Verify customer identity and data matches remittance information'
            ],
            2 => [
                'title' => 'Remittance Details Input',
                'description' => 'Input remittance details from external system'
            ],
            3 => [
                'title' => 'Final Review',
                'description' => 'Review all information before processing'
            ],
            4 => [
                'title' => 'Approval',
                'description' => 'Approve transaction for execution'
            ],
            5 => [
                'title' => 'Completion',
                'description' => 'Execute transfer and complete processing'
            ]
        ];

        return collect($stages)->map(function ($stage, $stageNumber) use ($currentStage, $status) {
            // $stageNumber is already 1-based from the array keys
            return new self(
                stage: $stageNumber,
                title: $stage['title'],
                description: $stage['description'],
                isCompleted: $stageNumber < $currentStage || $status === 8,
                isCurrent: $stageNumber === $currentStage && $status !== 8 && $status !== -2,
                completedAt: null, // Could be enhanced to track individual stage completion times
                notes: null
            );
        })->values()->all();
    }
}
