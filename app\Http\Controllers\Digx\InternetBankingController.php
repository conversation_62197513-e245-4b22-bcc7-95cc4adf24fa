<?php

namespace App\Http\Controllers\Digx;
use App\Helpers\ProxyHelperFacade;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Support\Facades\Cookie;


class InternetBankingController extends Controller
{
    protected $url;

    protected $cookies;
    protected $headers;

    public function __construct(){
        $this->url="{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1";
        $this->headers=[];
        $headers=  collect(request()->headers->all())->except([
            "content-length",
            "postman-token",
            "accept-encoding",
        ])->all();

        foreach ($headers as $key => $value) {
            $this->headers[$key]=implode(",",$value);
        }
    }
     /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|null
     */
    public function login(Request $request)
    {
      //  return response()->json($headers);
        //$this->headers[host]('host',["online.yk-bank.com:4437"]);
        $response = rescue(function ()use($request){
            return Http::withOptions([
                'verify' => false,
            ])
            ->withHeaders($this->headers)
            ->withoutRedirecting()
            ->timeout(10)
            ->asForm()
            ->post(
                "{$this->url}/j_security_check",
                [
                    'j_username' =>$request->j_username,
                    'j_password' => $request->j_password,
                ]
            );
        }, function ($e) {
            return $e->getMessage();
        });

        if(is_string($response)){
            return null;
        }

        $headers=collect($response->getHeaders());

       // $host="{$_ENV['OBDX_URL']}";
        $host=request()->getHost();
        if(in_array($response->status(),[303,302])){
            //Add cookies to current request
            $cookies=collect($response->cookies()->toArray())
            ->flatMap(function ($values) {
                return array_map(function ($value){
                    return $value;
                }, [$values["Name"]=>$values["Value"]]);
            })->toArray();

            $this->cookies=CookieJar::fromArray($cookies,$host);
            $this->checkAbility();
        }


        $responseBuilder = response($response->body())
        ->setStatusCode($response->status());

        if($response->hasHeader('location')){
            $responseBuilder = $responseBuilder->withHeaders(
                $headers
                ->put('location',["{$_ENV['OBDX_HTTP']}://{$host}:{$_ENV['OBDX_PORT']}/digx"] )
                ->toArray()
            );
        }else{
            $responseBuilder = $responseBuilder->withHeaders($response->headers());
        }
        foreach ($response->cookies()->toArray() as $cookie) {
            $responseBuilder->withCookie(cookie($cookie['Name'], $cookie['Value'],0,null,$host));
        }
        return $responseBuilder;
    }


    protected function checkAbility()
    {
        $response = Http::withOptions([
            'cookies' => $this->cookies
        ])
        ->withHeaders($this->headers)
        ->get("{$this->url}/me",[]);

        $result=$response->object();
       if((isset($result->status->message->code) && ($result->status->message->code??"-1")=="0") && isset($result->userProfile)){

           if(in_array('RetailUser',$result->userProfile->roles) || in_array('Customer',$result->userProfile->roles)){
               return abort(response('
               <html>
               <head>
               <title>Error Page</title>
               </head>

               <body>




               </body>
               </html>
           ')
           ->withHeaders([
               'X-AUTH-LOCATION-URL' => '/index.html?module=login&p_error_code=OBDXIDM-0&p_error_message=Please use bankylite app.'
           ])
           ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN));


           }
       }

    }

}
