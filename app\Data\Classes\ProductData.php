<?php

namespace App\Data\Classes;

use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Data;

class ProductData{

    public static function values():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->values;
    }
    public static function current():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->current;

    }
    public static function saving():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->saving;
    }
    public static function ad():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->ad;
    }
    public static function st():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->ad;
    }

    public static function corporation():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->corporation;
    }
    public static function individual():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->individual;
    }
    public static function cash():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->cash;
    }
    public static function cashNoneYER():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->cashNoneYER;
    }
    public static function cashOut():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->cashOut;
    }
    public static function cashOutNoneYER():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->cashOutNoneYER;
    }
    public static function eloan():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->eloan;
    }

    public static function creditCard():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->creditCard;
    }

    public static function allowedCurrenciesToOpenOld():array {
        $branches= [
            "banky.commercial"=>[
                //1-	اذا العميل المسجل بمنتج CUCI ريال يمني في بنكي  فانه يستطيع فتح عملة اجنبية في منتج CURI
                "CUCI"=>["YER"=>["CURI"=>["USD","SAR"]]],
                //3-	اذا العميل المسجل بمنتج CURI  لعملة اجنبية  في بنكي فانه يستطيع فتح عملة الريال اليمني في منتج CUCI
                "CURI"=>[
                    "USD"=>["CUCI"=>["YER"]],
                    "SAR"=>["CUCI"=>["YER"]],
                    "AED"=>["CUCI"=>["YER"]],
                    "EUR"=>["CUCI"=>["YER"]],
                ],
                //2-	اذا العميل المسجل بمنتج CUCS ريال يمني في بنكي فانه يستطيع فتح عملة اجنبية في منتج CUST
                "CUCS"=>["YER"=>["CUST"=>["USD","SAR"]]],
                //4-	Ø§Ø°Ø§ Ø§Ù„Ø¹Ù…ÙŠÙ„ Ø§Ù„Ù…Ø³Ø¬Ù„ Ø¨Ù…Ù†ØªØ¬ CUST Ù„Ø¹Ù…Ù„Ø© Ø§Ø¬Ù†Ø¨ÙŠØ©  ÙÙŠ Ø¨Ù†ÙƒÙŠ ÙØ§Ù†Ù‡ ÙŠØ³ØªØ·ÙŠØ¹ ÙØªØ­ Ø¹Ù…Ù„Ø© Ø§Ù„Ø±ÙŠØ§Ù„ Ø§Ù„ÙŠÙ…Ù†ÙŠ ÙÙŠ Ù…Ù†ØªØ¬ CUCS
                "CUST"=>[
                    "USD"=>["CUCS"=>["YER"]],
                    "SAR"=>["CUCS"=>["YER"]],
                    "AED"=>["CUCS"=>["YER"]],
                    "EUR"=>["CUCS"=>["YER"]],
                ],
                //5-	اذا العميل المسجل بمنتج سمسم لأي عملة في بنكي فانه يستطيع فتح عملة اجنبية او محلية في نفس منتج سمسم SDCA
                "SDCA"=>[
                    "YER"=>["SDCA"=>["USD","SAR"]],
                    "USD"=>["SDCA"=>["YER","SAR"]],
                    "SAR"=>["SDCA"=>["YER","USD"]],
                ],
                //6-	اذا العميل المسجل بمنتج سندباد لأي عملة في بنكي فانه يستطيع فتح عملة اجنبية او محلية في نفس منتج سندباد SNDA
                "SNDA"=>[
                    "YER"=>["SNDA"=>["USD","SAR"]],
                    "USD"=>["SNDA"=>["YER","SAR"]],
                    "SAR"=>["SNDA"=>["YER","USD"]],
                ],
            ],
            "banky.islamic"=>[],
        ];
        return $branches[env('APP_FLAG',"banky.commercial")]??[];
    }
    public static function allowedCurrenciesToOpen():array {
        $branches= [
            "banky.commercial"=>[
                //1-	اذا العميل المسجل بمنتج CUCI ريال يمني في بنكي  فانه يستطيع فتح عملة اجنبية في منتج CURI
                "CUCI"=>[
                    "YER"=>[
                        "PNPL"=>["YER"],
                        "CURI"=>["USD","SAR","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ]
                ],
                //3-	اذا العميل المسجل بمنتج CURI  لعملة اجنبية  في بنكي فانه يستطيع فتح عملة الريال اليمني في منتج CUCI
                "CURI"=>[
                    "USD"=>[
                        "PNPL"=>["YER"],
                        "CUCI"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "SAR"=>[
                        "PNPL"=>["YER"],
                        "CUCI"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "AED"=>[
                        "PNPL"=>["YER"],
                        "CUCI"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "EUR"=>[
                        "PNPL"=>["YER"],
                        "CUCI"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                ],
                //2-	اذا العميل المسجل بمنتج CUCS ريال يمني في بنكي فانه يستطيع فتح عملة اجنبية في منتج CUST
                "CUCS"=>[
                    "YER"=>[
                        "PNPL"=>["YER"],
                        "CUST"=>["USD","SAR","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ]
                ],
                //4-	اذا العميل المسجل بمنتج CUST لعملة اجنبية  في بنكي فانه يستطيع فتح عملة الريال اليمني في منتج CUCS
                "CUST"=>[
                    "USD"=>[
                        "CUCS"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "SAR"=>[
                        "CUCS"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "AED"=>[
                        "CUCS"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "EUR"=>[
                        "CUCS"=>["YER"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                ],
                //5-	اذا العميل المسجل بمنتج سمسم لأي عملة في بنكي فانه يستطيع فتح عملة اجنبية او محلية في نفس منتج سمسم SDCA
                "SDCA"=>[
                    "YER"=>[
                        "PNPL"=>["YER"],
                        "SDCA"=>["USD","SAR","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "USD"=>[
                        "PNPL"=>["YER"],
                        "SDCA"=>["YER","SAR","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "SAR"=>[
                        "PNPL"=>["YER"],
                        "SDCA"=>["YER","USD","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "EUR"=>[
                        "PNPL"=>["YER"],
                        "SDCA"=>["YER","USD","SAR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                ],
                //6-	اذا العميل المسجل بمنتج سندباد لأي عملة في بنكي فانه يستطيع فتح عملة اجنبية او محلية في نفس منتج سندباد SNDA
                "SNDA"=>[
                    "YER"=>[
                        "PNPL"=>["YER"],
                        "SNDA"=>["USD","SAR"]
                    ],
                    "USD"=>[
                        "PNPL"=>["YER"],
                        "SNDA"=>["YER","SAR"]
                    ],
                    "SAR"=>[
                        "PNPL"=>["YER"],
                        "SNDA"=>["YER","USD"]
                    ],
                ],
                //7-	اذا العميل المسجل بمنتج سمسم لأي عملة في بنكي فانه يستطيع فتح عملة اجنبية او محلية في نفس منتج سمسم رمادي SDCA
                "SDCG"=>[
                    "YER"=>[
                        "PNPL"=>["YER"],
                        "SDCG"=>["USD","SAR","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "USD"=>[
                        "PNPL"=>["YER"],
                        "SDCG"=>["YER","SAR","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "SAR"=>[
                        "PNPL"=>["YER"],
                        "SDCG"=>["YER","USD","EUR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                    "EUR"=>[
                        "PNPL"=>["YER"],
                        "SDCG"=>["YER","USD","SAR"],
                        "GI21"=>["21G"],
                        "GI24"=>["24G"]
                    ],
                ],
                "CUCC"=>[
                    "YER"=>[
                        "CURC"=>["USD","SAR","EUR"],
                        "GC21"=>["21G"],
                        "GC24"=>["24G"]
                    ]
                ],
                "CURC"=>[
                    "USD"=>[
                        "CUCC"=>["YER"],
                        "SDCA"=>["SAR","EUR"],
                        "GC21"=>["21G"],
                        "GC24"=>["24G"]
                    ],
                    "SAR"=>[
                        "CUCC"=>["YER"],
                        "SDCA"=>["USD","EUR"],
                        "GC21"=>["21G"],
                        "GC24"=>["24G"]
                    ],
                    "EUR"=>[
                        "CUCC"=>["YER"],
                        "SDCA"=>["USD","SAR"],
                        "GC21"=>["21G"],
                        "GC24"=>["24G"]
                    ],
                ],
            ],
            "banky.islamic"=>[
                "CRCI"=>["YER"=>["CURI"=>["USD","SAR"]]],
                "CURI"=>[
                    "USD"=>["CRCI"=>["YER"]],
                    "SAR"=>["CRCI"=>["YER"]],
                    "AED"=>["CRCI"=>["YER"]],
                    "EUR"=>["CRCI"=>["YER"]],
                ],
                "CRSI"=>[
                    "YER"=>["CRSI"=>["USD","SAR"]],
                    "USD"=>["CRSI"=>["YER","SAR"]],
                    "SAR"=>["CRSI"=>["YER","USD"]],
                ],
                "CURS"=>[
                    "YER"=>["CURS"=>["USD","SAR"]],
                    "USD"=>["CURS"=>["YER","SAR"]],
                    "SAR"=>["CURS"=>["YER","USD"]],
                ],
                "SAVI"=>[
                    "YER"=>["SAVI"=>["USD","SAR"]],
                    "USD"=>["SAVI"=>["YER","SAR"]],
                    "SAR"=>["SAVI"=>["YER","USD"]],
                ],
                "SAVS"=>[
                    "YER"=>["SAVS"=>["USD","SAR"]],
                    "USD"=>["SAVS"=>["YER","SAR"]],
                    "SAR"=>["SAVS"=>["YER","USD"]],
                ],
                //5-	اذا العميل المسجل بمنتج سمسم لأي عملة في بنكي فانه يستطيع فتح عملة اجنبية او محلية في نفس منتج سمسم SDCA
                "SDCA"=>[
                    "YER"=>["SDCA"=>["USD","SAR"]],
                    "USD"=>["SDCA"=>["YER","SAR"]],
                    "SAR"=>["SDCA"=>["YER","USD"]],
                ],
                //6-	اذا العميل المسجل بمنتج سندباد لأي عملة في بنكي فانه يستطيع فتح عملة اجنبية او محلية في نفس منتج سندباد SNDA
                "SNDA"=>[
                    "YER"=>["SNDA"=>["USD","SAR"]],
                    "USD"=>["SNDA"=>["YER","SAR"]],
                    "SAR"=>["SNDA"=>["YER","USD"]],
                ],

            ],
        ];
        return $branches[env('APP_FLAG',"banky.commercial")]??[];
    }
    public static function gold():array {
        return app(\App\Settings\ConfigSettings::class)->productConfig->gold;
    }
}

