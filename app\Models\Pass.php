<?php

namespace App\Models;
use App\Scopes\CustomerScope;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Pass extends Model
{
    use HasFactory;
    //protected $fillable = ["reference_id","party_id","receiver_mobile",'account_id','type','amount','fee','remarks',"status",'external_reference_id'];
    protected $fillable = ["party_id",'remarks',"type","status","received"];
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    protected $table = 'pass';

    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            $user=auth()->user();
            $query->party_id = "{$user->id}";
        });
    }
    public function transactions()
    {
        return $this->hasMany('App\Models\PassTransaction', 'pass_id');
    }

    public function transaction()
    {
        return $this->hasOne('App\Models\PassTransaction', 'pass_id');
    }
    public function resolvedUser()
    {
        return $this->belongsTo('App\Models\User','resolved_by');
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }

}
