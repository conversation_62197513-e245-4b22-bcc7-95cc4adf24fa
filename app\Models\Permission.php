<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\CacheModel;
use Auth;

class Permission extends \Spatie\Permission\Models\Permission
{
    public function subs(){
        // return $this->relatedToMany('app\ModelToReturn', function($query) {
        //    // $query -> ... // extend the query to go find the model data
        // });
   //     return $this->hasManyThrough(Permission::class, UserInterface::class,'identifier_level2','name_level2','name_level2','identifier_level2');

      //  $sds= $this->belongsToMany(Permission::class,'identifier_level1','name_level1','identifier_level2','name_level2')->withPivot('gender');

         return $this->hasMany(\Spatie\Permission\Models\Permission::class,'name_level2','name_level2')->whereColumn("name","!=","name_level2");
        // ->whereRaw("(LENGTH(identifier) - LENGTH(REPLACE(identifier,'.',''))) >= 1")->orderBy('sort');
        //return $this->hasMany('App\SubUserInterface',  'parent_identifier', 'identifier')->orderBy('sort');
    }

}
