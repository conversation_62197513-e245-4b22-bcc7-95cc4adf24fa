<?php

namespace App\Http\Controllers\Digx\Pass;
use App\Data\AccountConfigData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\Pass\PassCreateRequestData;
use App\Data\ReceiptData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\LimitTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Models\Pass;
use App\Models\PassTransaction;
use App\Services\NotificationService;
use App\Services\OBDX\CustomerService;
use App\Services\PassService;
use App\Traits\AuthorizesServices;
use Carbon\Carbon;
use App\Data\AccountData;

use App\Http\Controllers\Controller;
use App\LogItem;
use Illuminate\Http\Request;
use App\Services\FlexService;
use App\Enums\TransactionStatusEnum;

class SendController extends Controller
{

    public $details=null;

    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::WASIL
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $transactions=Pass::with(['transactions'=>function($query){
            if(env('DB_CONNECTION')=='oracle'){
                $query->select('id','pass_id as reference_id','pass_id','type','status','account_id','amount','fee','receiver_mobile','extra'
                // DB::raw("case when extra is null then '' else json_value(EXTRA, '$.mpt') end as tracking_code"),
                // DB::raw("case when extra is null then extra else json_value(EXTRA, '$.sender_name') end as sender_name"),
                // DB::raw("case when extra is null then extra else json_value(EXTRA, '$.sender_account_no') end as sender_account"),
                // DB::raw("case when extra is null then extra else json_value(EXTRA, '$.sender_agent_name') end as sender_agent")
                );
            }else{
                $query->select('id','pass_id as reference_id','pass_id','type','status','account_id','amount','fee','receiver_mobile',
                'extra->mpt as tracking_code','extra->sender_account_no as sender_account','extra->sender_name as sender_name',
                'extra->sender_agent_name as sender_agent');
            }
            return $query->whereIn('status',[ TransactionStatusEnum::COMPLETED->value,TransactionStatusEnum::PENDING->value]);
        }])
        ->select('id','type','remarks','received','created_at as date')
        ->whereIn('status',[ TransactionStatusEnum::COMPLETED->value,TransactionStatusEnum::PENDING->value])
        //->whereIn('type', [InvoiceTransactionsTypeEnum::Payment->value,InvoiceTransactionsTypeEnum::Refund->value,InvoiceTransactionsTypeEnum::Claim->value])
        ->whereHas('transactions',function($query){
            return $query->whereIn('status',[ TransactionStatusEnum::COMPLETED->value,TransactionStatusEnum::PENDING->value]);
        })
        ->skip($request->from)
        ->take($request->limit)
        ->orderBy("created_at","DESC")
        ->get();

        $transactions=collect($transactions)->map(function($pass){
            return $pass->only(['id','type','remarks','received','date'])+[
                'transactions'=> collect($pass->transactions)->map(function($transaction){
                    return collect($transaction)->toArray()+[
                        'tracking_code'=>$transaction->extra?->mpt??"",
                        'sender_name'=>$transaction->extra?->sender_name??"",
                        'sender_account'=>$transaction->extra?->sender_account_no??"",
                        'sender_agent'=>$transaction->extra?->sender_agent_name??"",
                    ];
                })->toArray()
            ];
        })->toArray();
        return response()->json($transactions);
    }

    public function indexAll(Request $request)
    {
        return response()->json([""]);
    }
    /**
     * Display the specified resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function receipt($id,Request $request)
    {
        $item= Pass::with(['transactions'=>function($query){
            return $query->select('id','pass_id as reference_id','pass_id','type','status','account_id','amount','fee','remarks','receiver_mobile','extra->mpt as tracking_code','extra','extra->sender_account_no as sender_account','extra->sender_name as sender_name','extra->sender_agent_name as sender_agent')
            ->whereIn('status', [ TransactionStatusEnum::COMPLETED->value,TransactionStatusEnum::PENDING->value]);
        }])
        ->select('id','type','remarks','status','created_at as date')
        ->whereIn('status', [ TransactionStatusEnum::COMPLETED->value,TransactionStatusEnum::PENDING->value])
        ->whereHas('transactions',function($query){
            return $query->whereIn('status',[ TransactionStatusEnum::COMPLETED->value,TransactionStatusEnum::PENDING->value]);
        })
        ->where('id', $id)
        ->first();

        if(is_null($item)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "RECEIPT-WASIL",
                    "message"   => [
                        "title"   => __("This transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $this->generateReceipt( $this->getReceiptData($item));
    }
    protected function getReceiptData(Pass $item,?PassTransaction $transaction=null): ReceiptData
    {
        $transaction??=$item->transactions->last();

        $sender=null;
        if($transaction->type=="claim"){
            if(isset($transaction->extra->sender_name)){
                $sender=$transaction->extra->sender_name;
            }
            if(isset($transaction->extra->sender_account_no)){
                if(is_null($sender)){
                    $sender=$transaction->extra->sender_account_no;
                }else{
                    $sender.=' - ('.$transaction->extra->sender_account_no.')';
                }
            }
        }


        $title=__("Funds transfer") ." (".__('Wasil').")"." ".__("wasil_type_". $transaction->type);
        return ReceiptData::from([
            "id"=> $item->id,
            "date"=> date_format(date_create($item->created_at), "Y-m-d H:i:s"),
            "title"=>  $title,
            "beneficiary"=> $transaction->type=="claim"?null:$transaction->receiver_mobile,
            "sender"=> $sender,
            "statement"=>$title,
            "details"=> [
                "debitAccountId"=> $transaction->account_id ,
                "remittanceId"=> $transaction->extra->mpt,
                "amount"=>$transaction->amount,
                "fee"=>$transaction->fee,
                "remarks"=>$item->remarks

            ]
        ]);
    }

    public function query(Request $request, Pass $pass)
    {
        $pass->load('transactions');
        $transaction=$pass->transactions
        ->where('status',$pass->status)
        ->where('type',$pass->type)
        ->first();

        $allowQuery=false;
        if(!is_null($transaction)){
            switch($pass->status){
                case TransactionStatusEnum::PENDING->value:
                    if($pass->type==InvoiceTransactionsTypeEnum::Payment->value){
                        $requestData=PassCreateRequestData::from([
                            'reference_id'          => $transaction->reference_id,
                            'receiver_mobile'       => $transaction->receiver_mobile,
                            "amount"                => $transaction->amount,
                            //"external_reference_id" => "",
                        ]);
                        $response=PassService::findByReference($requestData);
                        $result=$response->getAdditionalData()['extra']??null;
                        if($response->status->message->code=="0" && isset($result->result_code) && $result->result_code=="0"){
                            $pass->status=TransactionStatusEnum::COMPLETED->value;
                            $pass->save();


                            $transaction->extra= $result;
                            $transaction->external_reference_id= $transaction->extra->mpt;
                            $transaction->status=TransactionStatusEnum::COMPLETED->value;
                            $transaction->save();
                            $allowQuery=true;
                        // }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
                        //     return response()->json($response,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
                        }else{
                            return response()->json($response,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
                        }
                    }else /*if($pass->type==InvoiceTransactionsTypeEnum::Refund->value)*/{
                        return response()->json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "ERROR",
                                "contextID" => "STORE-WASIL",
                                "message"   => [
                                    "title"   => __("Can't verify this remittance, customer service will review this transaction & call you back soon!"),
                                    "detail"  => "",
                                    "code"    => "DIGX_SWITCH_WASIL_101",
                                    "type"    => "ERROR"
                                ]
                             ]
                        ]));
                    }
                    break;
                case TransactionStatusEnum::COMPLETED->value:
                    if($pass->type==InvoiceTransactionsTypeEnum::Payment->value){
                        $allowQuery=true;
                    }else if($pass->type==InvoiceTransactionsTypeEnum::Refund->value){
                        return response()->json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "SUCCESSFUL",
                                "contextID" => "",
                                "message"   => [
                                    "title"   => __("Remittance status [Canceled]"),
                                    "detail"  => "",
                                    "code"    => "0",
                                    "type"    => "INFO"
                                ]
                            ]
                        ])->additional([
                            'data'=>[
                                "refresh"=>1,
                            ]
                        ]));
                    }
                    break;
            }
        }

        if($allowQuery){
            $requestData=PassCreateRequestData::from([
                'reference_id'          =>uniqid(),
                'receiver_mobile'       => $transaction->receiver_mobile,
                "amount"                => $transaction->amount,
                "external_reference_id" => $transaction->extra->mpt,
            ]);

            $result=PassService::find($requestData);
            if($result->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
                $pass->received=1;
                $pass->save();
            //    $result->status->message->code=="0";
            }else if($result->status->message->code=="DIGX_SWITCH_WASIL_EXPIRED_001"){
                $this->details=$result->getAdditionalData()['details'];
                return $this->destroy($request,$pass);
            }else if($result->status->message->code!="0"){
                return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }
            return response()->json(GeneralResponseData::from($result->toArray())
            ->additional([
                'data'=>[
                        "received"=>$pass->received,
                    ]
                ])
            );

        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-WASIL",
                "message"   => [
                    "title"   => __("Can't check status of this remittance!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_WASIL_101",
                    "type"    => "ERROR"
                ]
             ]
        ]));
    }
    /**
     * Show the form for creating a new resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Pass  $pass
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, Pass $pass)
    {

        if($pass->status!=TransactionStatusEnum::INIT->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   => "This transaction already expired",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'transferDetails'=>$pass->transaction->only(["account_id","receiver_mobile","amount","fee"])+["id"=>$pass->id,"remarks"=>$pass->remarks??""],
        ]));

    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function create()
    {
        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'data'=>[
                "beforeInitMessage"=>__("Wasil remittance will be expired automatically in 5 days, please make sure the reciever will collect it before that. otherwise the remittance will reversed back to your account")
            ]
        ]));
    }

    /**
     * Store a newly created resource in storage.
     *
     * This method validates the incoming request data, checks the user's phone number,
     * verifies the account details, calculates the commission, and creates a new pass transaction.
     * It returns a JSON response indicating the success or failure of the operation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'debitAccountId.displayValue'=>"required",
            'debitAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required",
            'receiverMobile'=>"required|max:9|min:9"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_WASIL_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        if(is_null(auth()->user()->phone)||empty(auth()->user()->phone)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   => __("Please update your kyc from nearest branch!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $user=$request->user()->userProfile;
        $requestData=PassCreateRequestData::from($request->all()+[
            'reference_id'  =>uniqid(),
            'party_id'      =>$user->partyId->value,
            "type"          =>'d',
            'status'        =>TransactionStatusEnum::INIT->value
        ]);

        $valid=false;
        $message="Can't load your accounts!";
        $result=CustomerService::account($request,$requestData->accountId->value);
        if($result instanceof AccountData){
            $account=$result;
            if($requestData->amount->amount>$account->balance->amount){
                $message="You don't have enough balance in your account!";
            }else{
                $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$requestData->amount->currency &&
                $account->allowedService(AccountConfigData::waseelTransfer);
            }
        }
        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        //protected $fillable = ["party_id","receiver_mobile",'debit_account_id','amount','remarks',"status",'external_reference_id'];

        $result=PassService::commision($requestData);
        if($result->status->message->code!="0"){
            return response()->json($result);
        }

        $requestData->fee=CurrencyAmountData::from([
            "amount"=>$result->getAdditionalData()["commission"],
            "currency"=>'YER',
        ]);

        $pass=Pass::create([
            "type"              =>InvoiceTransactionsTypeEnum::Payment->value,
            "status"            =>TransactionStatusEnum::INIT->value,
            "remarks"           =>$requestData->remarks,
        ]);
        LogItem::store($pass);

        $referenceId=uniqid();
        PassTransaction::create([
            "pass_id"           =>$pass->id,
            "type"              =>InvoiceTransactionsTypeEnum::Payment->value,
            "status"            =>TransactionStatusEnum::INIT->value,
            "account_id"        =>$requestData->accountId->toArray(),
            "receiver_mobile"   =>$requestData->receiverMobile,
            "amount"            =>$requestData->amount->toArray(),
            "fee"               =>$requestData->fee->toArray(),
            "reference_id"      =>$referenceId,
            "remarks"           =>sprintf(trans("wasil_remark_message"),
                $requestData->receiverMobile,
                $referenceId
            )
        ]);



        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-WASIL-$pass->id",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "paymentId"=>$pass->id,
        ]));

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Pass  $pass
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request,  Pass $pass)
    {
        $user=$request->user()->userProfile;
        LogItem::store($pass);

        $pass->load('transactions');
        $passTransaction=$pass->transactions
        ->where('status',$pass->status)
        ->where('type',$pass->type)
        ->first();

        if(is_null($passTransaction) || count($pass->transactions)>1 ||
            $pass->status!=TransactionStatusEnum::INIT->value ||
            $pass->type!=InvoiceTransactionsTypeEnum::Payment->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CONFIRM-WASIL",
                    "message"   => [
                        "title"   => "This transaction already expired",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $requestData=PassCreateRequestData::from($passTransaction->toArray());
        $requestData->partyId=$pass->party_id;

        $valid=false;
        $message="Can't load your accounts!";
        $result=CustomerService::account($request,$requestData->accountId->value);
        if($result instanceof AccountData){
            $account=$result;
            if(($requestData->fee->amount+$requestData->amount->amount)>$account->balance->amount){
                $message="You don't have enough balance in your account!";
            }else{
                $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$requestData->amount->currency &&
                $account->allowedService(AccountConfigData::waseelTransfer);
            }
        }
        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }


        $whareDate="updated_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="updated_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }


        $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        $result=$targetLimitLinkages
        ->filter(function ($element, $key) use($requestData){
            return $element->target->value==LimitTypeEnum::WASIL->value &&
             $element->target->area==($requestData->accountId->isNorth()?'N':'S') &&
              $element->target->currency==$requestData->accountId->currencyId();
        })
        ->first();

        if(is_null($result)){
            $result=$targetLimitLinkages
            ->filter(function ($element, $key){
                return $element->target->value==LimitTypeEnum::WASIL->value &&
                 $element->target->area=='' &&
                  $element->target->currency=='';
            })
            ->first();
        }

        $trxLimit=$result->limits->filter(function ($element, $key){
            return $element->limitType=='TXN';
        })->first();
        if(!is_null($trxLimit) && ($requestData->amount->amount < $trxLimit->amountRange->minTransaction->amount || $requestData->amount->amount > $trxLimit->amountRange->maxTransaction->amount)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   =>sprintf( __("Your transaction amount exceed the allowed limit! Min %s Max %s per transaction"),$trxLimit->amountRange->minTransaction->amount,$trxLimit->amountRange->maxTransaction->amount),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $dailyLimit=$result->limits->filter(function ($element, $key){
            return $element->periodicity=='DAILY';
        })->first();
        if(!is_null($dailyLimit)){
            $limit=PassTransaction::select("amount->amount as limits")
            ->whereIn('pass_id', function ($query) use($pass,$whareDate){
                return $query->selectRaw('id')
                    ->from('pass')
                    ->where('party_id', auth()->user()->id)
                    ->where('type', InvoiceTransactionsTypeEnum::Payment->value)
                    ->where(function($query) use($pass){
                        return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                        ->orWhere('pass_id',$pass->id);
                    })
                    ->whereRaw($whareDate,
                        [Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()]);
            })
            ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
            ->where(function($query) use($pass){
                return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                ->orWhere('pass_id',$pass->id);
            })
            ->get();

            // return response()->json($limit);

            if(!is_null($limit) && ($limit->count()>$dailyLimit->maxCount || $limit->sum("limits")>$dailyLimit->maxAmount->amount)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-WASIL",
                        "message"   => [
                            "title"   => __("Your daily account limit exceed the allowed limit!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_WASIL_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }
        }

        $monthlyLimit=$result->limits->filter(function ($element, $key){
            return $element->periodicity=='MONTHLY';
        })->first();
        if(!is_null($monthlyLimit)){
            $limit=PassTransaction::select("amount->amount as limits")
            ->whereIn('pass_id', function ($query) use($pass,$whareDate){
                return $query->selectRaw('id')
                    ->from('pass')
                    ->where('party_id', auth()->user()->id)
                    ->where('type', InvoiceTransactionsTypeEnum::Payment->value)
                    ->where(function($query) use($pass){
                        return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                        ->orWhere('pass_id',$pass->id);
                    })
                    ->whereRaw($whareDate,
                        [Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()]);
            })
            ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
            ->where(function($query) use($pass){
                return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                ->orWhere('pass_id',$pass->id);
            })
            ->get();

            if(!is_null($limit) && ($limit->count()>$monthlyLimit->maxCount || $limit->sum("limits")>$monthlyLimit->maxAmount->amount)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-WASIL",
                        "message"   => [
                            "title"   => __("Your monthly account limit exceed the allowed limit!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_WASIL_101",
                            "type"    => "ERROR"
                        ]
                        ]
                ]));
            }
        }



        // Start getting user ID card info.
        $resultCardInfo=CustomerService::cardInfo($request);
        if($resultCardInfo->status->message->code!="0"){
            return response()->json($resultCardInfo);
        }
        $userCardInfo=$resultCardInfo->getAdditionalData()["cardInfo"];

        $requestData->senderName=html_entity_decode($user->firstName)." ".html_entity_decode($user->middleName)." ".html_entity_decode($user->lastName);
        $requestData->senderCardIdNumber=$userCardInfo->idNumber;
        $requestData->senderCardIdType= $userCardInfo->idType=="ID"?1:2;
        $requestData->senderMobile=auth()->user()->phone;

        /** Payment Process**/
        $object = new \stdClass();
        $object->service_name = ThirdPartyServiceNameData::wasil();
        $object->fee_service_name = ThirdPartyServiceNameData::wasilFee();
        $object->account_id =$requestData->accountId->value;
        $object->amount = $requestData->amount;
        $object->fee = $requestData->fee;
        $object->remarks =sprintf(trans("wasil_remark_message"),
            $requestData->receiverMobile,
            $requestData->referenceId
        );
        $object->remarksFee =sprintf(trans("wasil_fee_remark_message"),
            $requestData->referenceId
        );

        /** Debit from customer account to service account**/
        $result = FlexService::debitToAccountWithFees($object);
        $paymentResult=$result->getAdditionalData();
        if(isset($paymentResult['payment'])){
            $passTransaction->payment_result= $paymentResult['payment'];
            $passTransaction->save();
        }

        if($result->status->message->code=="0"){

             /** Service Process**/
            $result=PassService::send($requestData);
            if($result->status->message->code=="0"){
                $pass->status=TransactionStatusEnum::COMPLETED->value;
                $pass->save();

                $passTransaction->extra= $result->getAdditionalData()["extra"];
                $passTransaction->external_reference_id= $passTransaction->extra->mpt;
                $passTransaction->status=TransactionStatusEnum::COMPLETED->value;
                $passTransaction->save();

                NotificationService::sendMessagesToParty([
                    [
                        'title'=>__("Send remittance"),
                        'body'=>sprintf(__("Successfully send remittance to mobile number [%s] through [%s] service"),
                            $requestData->receiverMobile,
                            __("Wasil")
                        ),
                        'type'=>'operation',
                    ]
                ]);

                $result->status->message->title=__("The remittance sent successfully, you need to notify the reciever about it. Please copy the remittance & send it to reciever.");
                return response()->json(
                    GeneralResponseData::from($result->toArray())
                    ->additional([
                        'externalReferenceId'=>$passTransaction->external_reference_id,
                        'receipt'=> $this->getReceiptData($pass,$passTransaction)
                    ])
                );
            }else if($result->status->message->code=="DIGX_SWITCH_WASIL_TIMEOUT"){
                $pass->status=TransactionStatusEnum::PENDING->value;
                $pass->save();

                $passTransaction->status=TransactionStatusEnum::PENDING->value;
                $passTransaction->save();

            }else{
                $pass->status=TransactionStatusEnum::ERROR->value;
                $pass->resolved=1;
                $pass->save();
                $passTransaction->status=TransactionStatusEnum::ERROR->value;
                $passTransaction->save();

                $paymentResult=$passTransaction->payment_result;
                $object=new \stdClass();
                $object->reference_id   = $paymentResult->amount->referenceId;
                $object->service_name   =  ThirdPartyServiceNameData::wasil();
                $object->account_id     = $requestData->accountId->value;

                $reverseResult=FlexService::reverseToAccount($object);
                if($reverseResult->status->message->code=="0"){
                    $paymentResult->amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                    $passTransaction->payment_result= $paymentResult;
                    $passTransaction->save();
                }
                if(isset($paymentResult->fee)){
                    $object=new \stdClass();
                    $object->reference_id   = $paymentResult->fee->referenceId;
                    $object->service_name   = ThirdPartyServiceNameData::wasilFee();
                    $object->account_id     = $requestData->accountId->value;

                    $reverseResult=FlexService::reverseToAccount($object);
                    if($reverseResult->status->message->code=="0"){
                        $paymentResult->fee->status=InvoiceTransactionsTypeEnum::Reverse->value;
                        $passTransaction->payment_result= $paymentResult;
                        $passTransaction->save();
                    }
                }


            }
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }

    public static function limits($limitType){
        if(!in_array($limitType,[LimitTypeEnum::WASIL])){
            return;
        }

        $whareDate="updated_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="updated_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }


        $limits=[];
        $trxLimit=[];
        $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        $assignedLimits=$targetLimitLinkages
        ->filter(function ($element, $key) use($limitType){
            return $element->target->value==$limitType->value &&
             $element->target->area==(request()->input('area')??"") &&
              $element->target->currency==(request()->input('currency')??"");
        })
        ->first();

        foreach ($assignedLimits->limits as $item) {
            switch ($item->periodicity??"") {
                case 'DAILY':
                    $limitUtilization=PassTransaction::select("amount->amount as limits")
                    ->whereIn('pass_id', function ($query) use($whareDate){
                        return $query->selectRaw('id')
                            ->from('pass')
                            ->where('party_id', auth()->user()->id)
                            ->where('type', InvoiceTransactionsTypeEnum::Payment->value)
                            ->where('status',TransactionStatusEnum::COMPLETED->value)
                            ->whereRaw($whareDate,
                                [Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()]);
                    })
                    ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                case 'MONTHLY':
                    $limitUtilization=PassTransaction::select("amount->amount as limits")
                    ->whereIn('pass_id', function ($query) use($whareDate){
                        return $query->selectRaw('id')
                            ->from('pass')
                            ->where('party_id', auth()->user()->id)
                            ->where('type', InvoiceTransactionsTypeEnum::Payment->value)
                            ->where('status',TransactionStatusEnum::COMPLETED->value)
                            ->whereRaw($whareDate,
                                [Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()]);
                    })
                    ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                default:
                    if($item->limitType=="TXN" && isset($item->amountRange->minTransaction) && isset($item->amountRange->maxTransaction)){
                        $trxLimit=[
                            "minAmount"=>$item->amountRange->minTransaction,
                            "maxAmount"=>$item->amountRange->maxTransaction,
                        ];
                    }
            }
        }
        $trxLimit["limits"]=$limits;
        return abort(response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional($trxLimit)));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Pass  $pass
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request,  Pass $pass)
    {
        LogItem::store($pass);

        $pass->load('transactions');
        $transaction=$pass->transactions
        ->where('status',$pass->status)
        ->where('type',$pass->type)
        ->first();

        if(!is_null($transaction) && count($pass->transactions)==1 && $pass->status==TransactionStatusEnum::PENDING->value && $pass->type==InvoiceTransactionsTypeEnum::Payment->value){
            $requestData=PassCreateRequestData::from([
                'reference_id'          => $transaction->reference_id,
                'receiver_mobile'       => $transaction->receiver_mobile,
                "amount"                => $transaction->amount,
                //"external_reference_id" => "",
            ]);
            $response=PassService::findByReference($requestData);
            $result=$response->getAdditionalData()['extra']??null;
            if($response->status->message->code=="0" && isset($result->result_code) && $result->result_code=="0"){
                $pass->status=TransactionStatusEnum::COMPLETED->value;
                $pass->save();

                $transaction->extra= $result;
                $transaction->external_reference_id= $transaction->extra->mpt;
                $transaction->status=TransactionStatusEnum::COMPLETED->value;
                $transaction->save();
            }else{
                return response()->json($response,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }

        }

        if(is_null($transaction) || count($pass->transactions)>1 || $pass->status!=TransactionStatusEnum::COMPLETED->value ||
            $pass->type!=InvoiceTransactionsTypeEnum::Payment->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   => __("You can't cancel this remittance!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        // $valid=false;
        // $message="This transaction not in the correct status!";
        // $pass->load('transactions');
        // foreach( $pass->transactions as $transaction){
        //     if($transaction->status==TransactionStatusEnum::COMPLETED->value){
        //         if($transaction->type==InvoiceTransactionsTypeEnum::Payment->value){
        //             $valid=true;
        //         }
        //         if($transaction->type==InvoiceTransactionsTypeEnum::Refund->value){
        //             $message="Remittance already canceled!";
        //             $valid=false;
        //             break;
        //         }
        //     }
        // }
        // if(!$valid){
        //     response()->json(GeneralResponseData::from([
        //         'status'=>[
        //             "result"    => "ERROR",
        //             "contextID" => "STORE-WASIL",
        //             "message"   => [
        //                 "title"   => __($message),
        //                 "detail"  => "",
        //                 "code"    => "DIGX_SWITCH_WASIL_101",
        //                 "type"    => "ERROR"
        //             ]
        //          ]
        //     ]));
        // }

        // $transaction=$pass->transactions->where('status',TransactionStatusEnum::COMPLETED->value)
        // ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
        // ->first();

        $details=$this->details;
        if(is_null($this->details)){
            $requestData=PassCreateRequestData::from([
                'reference_id'          =>uniqid(),
                'receiver_mobile'       => $transaction->receiver_mobile,
                "amount"                => $transaction->amount,
                "external_reference_id" => $transaction->extra->mpt,
            ]);

            $result=PassService::find($requestData);
            if($result->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
                $pass->received=1;
                $pass->save();
                return response()->json(GeneralResponseData::from($result->toArray())
                ->additional([
                    'data'=>[
                            "received"=>$pass->received,
                        ]
                    ])
                );
            }else if($result->status->message->code!="0" && $result->status->message->code!="DIGX_SWITCH_WASIL_EXPIRED_001"){
                return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }
            $details=$result->getAdditionalData()['details'];
        }

        $verifyResult=PassService::verify(PassCreateRequestData::from([
            "reference_id"=> uniqid(),
            "external_reference_id"=> $transaction->extra->transaction_reference,
            "row_version" => $transaction->extra->operation_reference??$transaction->extra->transaction_reference,
        ]));

        if($verifyResult->status->message->code!="0" || ($verifyResult->getAdditionalData()['verifyStatus']??"")!=InvoiceTransactionsTypeEnum::Payment->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   => __("You can't cancel this remittance!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_102",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $details->verify=$verifyResult->getAdditionalData()['extra'];

        $passTransaction=PassTransaction::create([
            "pass_id"           =>$pass->id,
            "type"              =>InvoiceTransactionsTypeEnum::Refund->value,
            "status"            =>TransactionStatusEnum::INIT->value,
            "account_id"        =>$transaction->account_id,
            "receiver_mobile"   =>$transaction->receiver_mobile,
            "amount"            =>$transaction->amount,
            "fee"               =>$transaction->fee,
            "reference_id"      =>uniqid(),
            "remarks"           =>$transaction->remarks,
            "extra"=>$details,
        ]);

        $requestData=PassCreateRequestData::from([
            "reference_id"=> $passTransaction->reference_id,
            "external_reference_id"=> $transaction->extra->transaction_reference,
            "row_version" => $transaction->extra->row_version,
            //"operation_reference" => $transaction->extra->operation_reference??null,
        ]);
        $result=PassService::cancel($requestData);
        if($result->status->message->code=="0"){
            $pass->status=TransactionStatusEnum::COMPLETED->value;
            $pass->type=InvoiceTransactionsTypeEnum::Refund->value;
            $pass->save();

            $extra=$result->getAdditionalData()["extra"];
            if(!isset($transaction->extra->operation_reference) && isset($extra->operation_reference)){
                $oldExtra=$transaction->extra;
                $oldExtra->operation_reference=$extra->operation_reference;//??$extra->cancel_reference;
                $transaction->extra=$oldExtra;
                $transaction->save();
            }

            $passTransaction->extra= collect($passTransaction->extra)
            ->merge($extra);//$result->getAdditionalData()["extra"];
            $passTransaction->external_reference_id= $passTransaction->extra->financial_reference;
            $passTransaction->status=TransactionStatusEnum::COMPLETED->value;
            $passTransaction->save();

            /** Reverse to user account */
            $paymentResult=$transaction->payment_result;
            $object=new \stdClass();
            $object->reference_id   = $paymentResult->amount->referenceId;
            $object->service_name   =  ThirdPartyServiceNameData::wasil();
            $object->account_id     = $transaction->account_id->value;

            $reverseResult=FlexService::reverseToAccount($object);
            if($reverseResult->status->message->code=="0"){
                $paymentResult->amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                $passTransaction->payment_result= $paymentResult;
                $passTransaction->save();
            }
            // if(isset($paymentResult->fee)){
            //     $object=new \stdClass();
            //     $object->reference_id   = $paymentResult->fee->referenceId;
            //     $object->service_name   = ThirdPartyServiceNameData::wasilFee();
            //     $object->account_id     = $transaction->account_id->value;

            //     $reverseResult=FlexService::reverseToAccount($object);
            //     if($reverseResult->status->message->code=="0"){
            //         $paymentResult->fee->status=InvoiceTransactionsTypeEnum::Reverse->value;
            //         $passTransaction->payment_result= $paymentResult;
            //         $passTransaction->save();
            //     }
            // }

            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Cancel remittance"),
                    'body'=>sprintf(__("Successfully cancel remittance #[%s] through [%s] service"),
                        ($transaction->extra?->mpt??""),
                        __("Wasil")
                    ),
                    'type'=>'operation',
                ]
            ]);

            if(!is_null($this->details)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("This remittance expired & amount successfully reversed to your account!"),
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ])->additional([
                    'data'=>[
                        "refresh"=>1,
                    ]
                ]));
            }
            return response()->json(
                GeneralResponseData::from($result->toArray())
                ->additional([
                    'externalReferenceId'=>$passTransaction->external_reference_id,
                ])
            );
        // }else if($result->status->message->code=="DIGX_SWITCH_WASIL_TIMEOUT"){
        //     $pass->status=TransactionStatusEnum::PENDING->value;
        //     $pass->type=InvoiceTransactionsTypeEnum::Refund->value;
        //     $pass->save();

        //     $passTransaction->status=TransactionStatusEnum::PENDING->value;
        //     $passTransaction->save();
        }else{
            $pass->status=TransactionStatusEnum::ERROR->value;
            $pass->type=InvoiceTransactionsTypeEnum::Refund->value;

            $pass->save();
            $passTransaction->status=TransactionStatusEnum::ERROR->value;
            $passTransaction->save();
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }


}
