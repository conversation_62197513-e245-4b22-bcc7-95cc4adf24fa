<?php

namespace App\Http\Middleware;

use Illuminate\Cookie\Middleware\EncryptCookies as Middleware;
use Closure;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
class EncryptDigxCookies extends Middleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string[]  ...$guards
     * @return mixed
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards)
    {
       // dd($request->path());
       // $isEncrypted=$this->isEecrypt($request);
        if(($request->path()=="digx/v1/switch/me" && !$request->hasHeader("X-CHALLENGE-RESPONSE"))){
            $response=$next($request);
            return $response;
        }else{
            $response=$next($this->decrypt($request));
            if($response->headers->has("jwtoken")){
                return $response;
            }
            return $this->encrypt($response);
        }
    }
}
