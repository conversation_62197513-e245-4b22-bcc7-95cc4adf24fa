<?php

namespace App\Models;

use App\CacheModel;
use App\Data\AppConfigData;
use App\Data\AppVersionHistoryItemData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\LaravelData\DataCollection;

class AppVersionHistory extends CacheModel
{

    use HasFactory;
    protected $table = 'app_version_histories';

    protected $fillable = [
        "version",
        "name",
        "description",
        "os_types",
        "available",
        "status"
    ];

    protected $casts = [
        'description' => 'object',
        'os_types' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'status','created_at','updated_at'
    ];
    // public function getDescriptionAttribute($value)
    // {
    //     $locale = app()->getLocale();
    //     return $value->{$locale} ?? $value;
    // }

    public static function getNewUpdatesAvailable(){
        $aph=(request()->header('apnus')??0);
        $items = static::select("version", "name", "description", "os_types")
            ->where('available', 1)
            ->where('status', 1)
            ->where('version', '>', $aph)
            // ->where(function($query) {
            //     $query->where('os_types', 'like', '%"android"%')
            //             ->orWhereNull('os_types');
            // })
            ->orderBy('version', 'desc')
            ->take(20)
            ->get();


        // $items->filter(function($item) use($aph){
        //     return in_array(request()->header('os'),$item->os_types);
        // });
        return  AppVersionHistoryItemData::collect($items->toArray(),DataCollection::class);
    }
    public static function getWhatsNew(){
        if((request()->header('appVersion')??0)>(request()->header('aphs')??0)){
            $appVersion=(request()->header('appVersion')??0);
            return static::getWithCache("appVersionHistories.$appVersion", function ($query) use($appVersion){
                $items= static::select("version","name","description")
                ->where('version','<=',$appVersion)
                ->where('status',1)
                // ->where(function($query) {
                //     $query->whereJsonContains('os_types', 'android')
                //         ->orWhereNull('os_types');
                // })
                ->orderBy('version','desc')
                ->take(20)
                ->get();

                return  AppVersionHistoryItemData::collect($items->toArray(),DataCollection::class);

            });
        }
        return null;
    }
}
