<?php

namespace App\Services;

use App\Models\RemittanceNetwork;
use App\Models\HarvestServiceCode;

class RemittanceNetworkService
{
    /**
     * Detect remittance network based on tracking code
     */
    public static function detectNetwork(string $trackingCode): ?RemittanceNetwork
    {
        // Basic validation first
        if (empty($trackingCode) || strlen($trackingCode) < 6) {
            return null;
        }

        // Use caching to avoid repeated database queries
        $cacheKey = "network_detection:" . md5($trackingCode);

        return \Cache::remember($cacheKey, 300, function () use ($trackingCode) {
            try {
                // Set a timeout for the database query
                return \DB::transaction(function () use ($trackingCode) {
                    return RemittanceNetwork::findByTrackingCode($trackingCode);
                }, 3); // 3 second timeout
            } catch (\Exception $e) {
                \Log::warning("Network detection failed for tracking code {$trackingCode}: " . $e->getMessage());
                return null;
            }
        });
    }

    /**
     * Determine if tracking code should use automated processing
     */
    public static function shouldUseAutomatedProcessing(string $trackingCode): bool
    {
        $network = static::detectNetwork($trackingCode);
        
        if (!$network) {
            // If no network detected, try automated processing first
            return true;
        }

        return $network->is_auto;
    }

    /**
     * Get appropriate service code for tracking code
     */
    public static function getServiceCodeForTrackingCode(string $trackingCode, int $requestedServiceCode): ?HarvestServiceCode
    {
        $network = static::detectNetwork($trackingCode);

        if (!$network) {
            // Use requested service code if no network detected
            return HarvestServiceCode::find($requestedServiceCode);
        }

        // Check if requested service code matches the network's service code
        if ($requestedServiceCode === $network->service_code_id) {
            return $network->serviceCode;
        }

        // Return the network's associated service code
        return $network->serviceCode;
    }

    /**
     * Get processing method for tracking code
     */
    public static function getProcessingMethod(string $trackingCode): string
    {
        $network = static::detectNetwork($trackingCode);
        
        if (!$network) {
            return 'automated'; // Default to automated if no network detected
        }

        return $network->is_auto ? 'automated' : 'manual';
    }

    /**
     * Get comprehensive network information for tracking code
     */
    public static function getNetworkInfo(string $trackingCode): array
    {
        $network = static::detectNetwork($trackingCode);
        
        if (!$network) {
            return [
                'network_detected' => false,
                'network_name' => null,
                'network_code' => null,
                // 'processing_method' => 'automated',
                // 'requires_manual' => false,
                'processing_method' => 'manual',
                'requires_manual' => true,
                'service_code_id' => null
            ];
        }

        return [
            'network_detected' => true,
            'network_name' => $network->name,
            'network_code' => $network->code,
            'processing_method' => $network->is_auto ? 'automated' : 'manual',
            'requires_manual' => !$network->is_auto,
            'service_code_id' => $network->service_code_id,
            'tracking_pattern' => $network->tracking_code_pattern,
            'expected_length' => $network->tracking_code_length
        ];
    }

    /**
     * Validate tracking code format
     */
    public static function validateTrackingCode(string $trackingCode): array
    {
        $network = static::detectNetwork($trackingCode);
        
        if (!$network) {
            return [
                'valid' => false,
                'errors' => ['No network found for this tracking code format'],
                'network' => null
            ];
        }

        $errors = [];
        
        // Check length
        if (strlen($trackingCode) !== $network->tracking_code_length) {
            $errors[] = "Expected length: {$network->tracking_code_length}, got: " . strlen($trackingCode);
        }

        // Check pattern if defined
        if ($network->tracking_code_pattern && !preg_match($network->tracking_code_pattern, $trackingCode)) {
            $errors[] = "Tracking code format does not match expected pattern for {$network->name}";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'network' => $network->name
        ];
    }

    /**
     * Get all available networks for admin
     */
    public static function getAllNetworks(): array
    {
        return RemittanceNetwork::with('serviceCode')
            ->orderBy('name')
            ->get()
            ->map(function ($network) {
                return [
                    'id' => $network->id,
                    'name' => $network->name,
                    'code' => $network->code,
                    'is_auto' => $network->is_auto,
                    'status' => $network->status,
                    'tracking_code_length' => $network->tracking_code_length,
                    'tracking_code_prefix' => $network->tracking_code_prefix,
                    'service_code_name' => $network->serviceCode?->name,
                    'service_code_id' => $network->service_code_id,
                    'processing_method' => $network->is_auto ? 'Automated' : 'Manual'
                ];
            })
            ->toArray();
    }

    /**
     * Get statistics for networks
     */
    public static function getNetworkStatistics(): array
    {
        $networks = RemittanceNetwork::withCount('harvests')->with('serviceCode')->get();
        
        return [
            'total_networks' => $networks->count(),
            'automated_networks' => $networks->where('is_auto', true)->count(),
            'manual_networks' => $networks->where('is_auto', false)->count(),
            'active_networks' => $networks->where('status', true)->count(),
            'networks' => $networks->map(function ($network) {
                return [
                    'name' => $network->name,
                    'harvests_count' => $network->harvests_count,
                    'service_code_name' => $network->serviceCode?->name,
                    'processing_method' => $network->is_auto ? 'Automated' : 'Manual',
                    'status' => $network->status ? 'Active' : 'Inactive'
                ];
            })
        ];
    }

    /**
     * Check if network supports automated processing
     */
    public static function supportsAutomatedProcessing(string $networkCode): bool
    {
        $network = RemittanceNetwork::where('code', $networkCode)->first();
        return $network ? $network->is_auto : false;
    }

    /**
     * Get network by code
     */
    public static function getNetworkByCode(string $code): ?RemittanceNetwork
    {
        return RemittanceNetwork::where('code', $code)->first();
    }

    /**
     * Update network processing method
     */
    public static function updateProcessingMethod(string $networkCode, bool $isAutomated): bool
    {
        $network = RemittanceNetwork::where('code', $networkCode)->first();
        
        if (!$network) {
            return false;
        }

        $network->update(['is_auto' => $isAutomated]);
        return true;
    }
}
