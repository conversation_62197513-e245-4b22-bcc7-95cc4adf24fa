<?php

namespace App\Http\Controllers\Digx;
use App\Data\GeneralResponseData;
use App\Enums\TransactionStatusEnum;

use App\Http\Controllers\Controller;
use App\Models\PartyVerify;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Services\NotificationService;

class <PERSON><PERSON><PERSON>ontroller extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $devices=PartyVerify::select("id", 'device_key','type','status', 'terminal','updated_at as date')
        ->where('status',TransactionStatusEnum::COMPLETED->value)
        ->orderBy('created_at','DESC');

        if((request()->header('appVersion')??0)>=203){
            $devices=$devices->orWhere(function($query){
                $query->where('status','!=',TransactionStatusEnum::COMPLETED->value)
                ->where('type','APP');
            });
        }
        $devices=$devices->get();
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            'devices'=>$devices,
        ]));

    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator=validator()->make($request->all(),[
            'status' => 'required|in:'.join(',',[TransactionStatusEnum::PENDING->value,TransactionStatusEnum::CANCELED->value])
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $partyVerify=PartyVerify::where('status',TransactionStatusEnum::INIT->value)
        ->where('type','APP')
        ->where('id',$id)
        ->first();

        if(is_null($partyVerify) || Carbon::parse($partyVerify->expiry_date)->isBefore(Carbon::now())){
            return response()->json(GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "NEW-DEVICE-APPROVAL",
                    "message"   => [
                        "title"   => __("The approval request expired!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_DEVICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            )));
        }
        $partyVerify->status=$request->status;
        $partyVerify->save();

        $status=$partyVerify->status==TransactionStatusEnum::PENDING->value?"Approved":"Rejected";
        if(isset($partyVerify->terminal->registrationToken) && !is_null($partyVerify->terminal->registrationToken)){
            NotificationService::send([
                'tokens'=>[
                    $partyVerify->terminal->registrationToken
                ],
                'title'=>__("New Login"),
                'body'=>sprintf(trans("Your request was [%s]"),
                    __($status)
                ),
                'type'=>'verification',
                'extra_id'=>$partyVerify->id
            ]);
        }



        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully complete the approval"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        )));


    }
    /**
     * Delete the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($deviceKey)
    {
        $partyVerifies=PartyVerify::where('device_key',$deviceKey)->get();
        NotificationService::send([
            'tokens'=>$partyVerifies->map(function ($element,int $key){
                if(isset($element->terminal->registrationToken)){
                    return $element->terminal->registrationToken;
                }
                return null;
            })->filter()->unique()->toArray(),
            'title'=>__("Deleted device"),
            'type'=>'unverification',
            'extra_id'=>$deviceKey
        ]);
        PartyVerify::where('device_key',$deviceKey)
            ->delete();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully delete device from list"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }

}
