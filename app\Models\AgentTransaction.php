<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Str;

class AgentTransaction extends Model
{
    use HasFactory;
    protected $connection= 'agent';

    protected $table = 'transactions';
    protected $casts = [
        'amount' => 'object',
        'customer_account_id' => 'object',
        'request_data' => 'object',
        'init_response_data' => 'object',
        'response_data' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    public function service()
    {
        return $this->belongsTo('App\Models\AgentUser',"party_id");
    }

    public function logs()
    {
        $_name=static::class;
        return $this->hasMany(LogEntry::class, "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}

