<?php

namespace App\Services;
use App\Data\AccountIdData;
use App\Data\GeneralResponseData;
use App\Data\TokenData;

use App\Data\UP\Query\BillPaymentQueryBaseData;
use App\Data\VirtualCardData;
use App\Enums\BillPaymentServiceCodeEnum;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

use App;
use App\Models\User;

/**
 * Service to create and update orders
 */
class UtilityPayementService
{

    const YMSC=42103;
    //const YMSC=42103;

    protected $settings;
    protected $isTest=false;

    /**
     * Service to create and update orders
     */
    public function __construct()
    {
        //$this->updateUrl();
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->utilityPayement;
        if($this->settings->is_test){
            $this->settings=$this->settings->test_data;
        }
    }

     /**
     * Register a stub callable that will intercept requests and be able to return stub responses.
     *
     * @param  string  $method
     * @param  string  $url
     * @param  callable|array $params
     * @param  callable|array $fake
     * @return $response
     */
    function getHttpRequest(string $method,string $url,array $params=[],array $fake=[],$timeout=null){
        if(!empty($fake)){
            $request=Http::fake($fake);
        }else{
            $request=Http::withOptions([
                'verify'=>false
            ]);
            if(!is_null($timeout)){
                $request=$request->timeout($timeout);
            }else if(empty($url)){
                $request=$request->timeout(10);
            }
        }
        $params=$params+[
            "USR"=>$this->settings->utility_payment->username,
            "TKN"=>$this->settings->utility_payment->password
        ];
        if($method=='GET' && !empty($params)){
            $params=[
                'query' => $params,
            ];
        }else if($method!='GET' && !empty($params)){
            $params=[
                'json' => $params,
            ];
        }
        $response = rescue(function () use($request,$method,$url,$params){
            return $request->send( $method,"{$this->settings->url}/$url",$params );
        }, function ($e) {
            return $e->getMessage();
        });

        //$response=$request->send( $method,"{$this->settings->url}/$url",$params );
        return $response;

    }

    public static function catalog() {

        $utilityPayementService=new self();
        $fake=[];
        $params=[
            "USR"=>$utilityPayementService->settings->utility_payment->app_username??request()->usr
        ];

        $result=[
        "RC"=>0,
        ];

        $response=$utilityPayementService->getHttpRequest('GET','catalog',$params,$fake,20);
        if(!is_string($response)){
            $result=$response->object();
        }
        return $result;

    }


    public static function query($request) {

        $utilityPayementService=new self();
        $fake=[];

        $params=[
            "ac"=>$request->ac,
            "sc"=>$request->sc,
            "sno"=>$request->sno,
        ];
        if(in_array($request->sc??"",["42108"])){
            $params['sac']=$request->sac;
        }
        if(isset($request->item)){
            $params['item']=$request->item;
        }
        $response=$utilityPayementService->getHttpRequest('GET','',$params,$fake);

        if(!is_string($response)){
            $result=$response->object();

            if(in_array($request->sc??"",["42103"]) && (isset($result->SD) || isset($result->sd))){
                // if(!isset($result->bal)){
                //     $result->bal="";
                // }
                // return [
                //     "RC"=>0
                // ];
                $sd=json_decode($result->SD??$result->sd);
                //$sd=$result->SD;
                $sd->Balance=$sd->Balance??$sd->CREDIT??0;
                $sd->CREDIT=$sd->CREDIT??$sd->Balance??0;
                $result->bal=$sd->CREDIT;

                //\Log::critical($sd->Offers);
                if(isset($sd->Offers)){
                    $offers=[];
                    $sd->Offers=json_decode($sd->Offers);
                    foreach ($sd->Offers as &$offer) {
                        //\Log::critical($offer->exp_date);
                        if(isset($offer->exp_date)){
                            $offer->exp_date=str_replace("(","",str_replace(")","",$offer->exp_date));
                            //\Log::critical($offer->exp_date);
                        }
                        $offers[]=$offer;
                    }
                    $sd->Offers=json_encode($offers);
                }


                // if($result->MT==2 && !isset($sd->Balance) && isset($sd->CREDIT)){
                //     $sd->Balance= $sd->Balance??$sd->CREDIT;
                //     $result->bal=$sd->CREDIT;
                // }
                if($result->MT==1){
                    $response=$utilityPayementService->getHttpRequest('GET','',[
                        "ac"=>4007,
                        "sc"=>$request->sc,
                        "sno"=>$request->sno,
                    ],$fake);
                    if(!is_string($response)){
                        $response=$response->object();
                        if(isset($response->SD)){
                            $response->SD=json_decode($response->SD);
                            $sd->loanStatus=$response->SD->loanStatus;

                        }
                    }
                }

                if(isset($result->SD)){
                    $result->SD=json_encode($sd);
                }else if(isset($result->sd)){
                    $result->sd=json_encode($sd);
                }
            }
        }else{
            $result=[
                "RC"=>0
            ];
        }

        return $result;

    }
    public static function queryV2($request):BillPaymentQueryBaseData {

        $utilityPayementService=new self();
        $result=null;
        foreach ($request->ac as $ac) {
            $params=collect($request)->toArray();
            $params["ac"]=$ac;
            $response=$utilityPayementService->getHttpRequest('GET','',$params,[],15);
            if(!is_string($response)){
                $_result=$response->object();
                $result??=$_result;
                if((isset($_result->SD) || isset($_result->sd))){
                    $sd=json_decode($_result->SD??$_result->sd);
                    if(isset($result->SD) && is_object($result->SD) && isset($sd->loanStatus)){
                        $result->SD->loan=[
                            "status"=>$sd->loanStatus,
                            "amount"=>$sd->loanAmount??null,
                            "desc"=>$sd->loanDesc??null,
                        ];
                    }else{
                        $result->SD=$sd;
                    }
                }
            }else{
                $result=[
                    "RC"=>"Error"
                ];
            }
        }
        $result=static::deepJsonDecode($result);
        //abort(response()->json($result));
        return BillPaymentQueryBaseData::fromArray(BillPaymentServiceCodeEnum::findByValue($request->sc),$result);
    }

    public static function deepJsonDecode($input)
    {
        /// Try to decode strings multiple times if necessary
        while (is_string($input)) {
            $decoded = json_decode($input, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $input = $decoded;
            } else {
                break;
            }
        }

        // Recurse through arrays
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = self::deepJsonDecode($value);
            }
        }
        if (is_object($input)) {
            return self::deepJsonDecode(collect($input)->toArray());
        }
        return $input;
    }
    public static function transactions($request) {

        $utilityPayementService=new self();
        $fake=[];

        $params=[
            "ac"=>$request->ac,
            "sc"=>$request->sc,
            "sno"=>$request->sno,
            "ExtData"=>[
                "Msisdn"=>$request->msisdn,
                "FromDate"=>$request->fromDate??Carbon::now()->subMonths(3)->format("mdY"),
                "ToDate"=>$request->toDate??Carbon::now()->format("mdY"),
                "NoOfTxns"=>$request->limit??20,
            ]
        ];

        $response=$utilityPayementService->getHttpRequest('POST','',$params,$fake);

        if(!is_string($response)){
            $result=$response->object();
        }else{
            $result=[
                "RC"=>0
            ];
        }

        return $result;

    }

    public static function status($request) {

        $utilityPayementService=new self();
        $fake=[];

        $params=[
            "ac"=>$request->ac,
            "sc"=>$request->sc,
            "sno"=>$request->sno,
            "ExtData"=>[
                "Msisdn"=>$request->msisdn,
                "DestCard"=>$request->sno,
                "Status"=>"BLOCKED",
            ]
        ];

        $response=$utilityPayementService->getHttpRequest('POST','',$params,$fake);

        if(!is_string($response)){
            $result=$response->object();
            if($result->RC!=0){
                if(str_contains($result->MSG,'007')==true){
                    $params['ExtData']['Status']="UNBLOCKED";
                    $response=$utilityPayementService->getHttpRequest('POST','',$params,$fake);

                    if(!is_string($response)){
                        $result=$response->object();
                    }else{
                        $result=[
                            "RC"=>-1
                        ];
                    }
                }
            }
        }else{
            $result=[
                "RC"=>-1
            ];
        }

        return $result;

    }
}
