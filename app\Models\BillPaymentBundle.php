<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillPaymentBundle extends Model
{
    use HasFactory;

    protected $fillable = ['id', 'bill_payment_item_id','title','amount','status', 'payload'];
    protected $casts = [
        'title' => 'object',
        'amount' => 'object',
        'payload' => 'object',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    public function getNameAttribute()
    {
        $local=app()->getLocale();
        return  $this->title?->{"$local"};
    }    public function item()
    {
        return $this->belongsTo(BillPaymentItem::class, 'bill_payment_item_id');
    }

    public function options()
    {
        return $this->morphToMany(BillPaymentFilterOption::class, "model","bill_payment_model_filter_options");
    }
}
