<?php

namespace App\Data;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Illuminate\Support\Collection;

class PaymentResultData extends Data
{
    public function __construct(
        public ?string $referenceId,
        public ?string $externalReferenceId,
        public ?string $status
    ) {
    }

}
