<?php

namespace App\Models;

use App\Scopes\MerchantScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory;
    /**
    * The list of hidden request headers.
    *
    * @var array
    */
    public static $hiddenParameters = [
        'txn_token',
        'customer_phone',
        '*.txn_token',
     ];
    protected $fillable = ["txn_token","initiator_id","initiator_type",'debit_account_id','order_id','customer_phone','amount','purpose','remarks'];
    protected $casts = [
        'debit_account_id' => 'object',
        'customer_account_id' => 'object',
        'amount' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new MerchantScope);
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User',"initiator_id");
    }
    public function transactions()
    {
        return $this->hasMany('App\Models\InvoiceTransaction', "invoice_id");
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}
