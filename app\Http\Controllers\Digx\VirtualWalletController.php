<?php

namespace App\Http\Controllers\Digx;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Models\VirtualWallet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class VirtualWalletController extends Controller
{
    protected $types=[
        "gold"=>"gold"
    ];

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $this->validate($request,[
            'type' => 'required|in:'.join(",",collect($this->types)->keys()->toArray())
        ]);
        $virtualWallets=VirtualWallet::select("id", 'name','image','amount','goal','status','type')
            ->where('type',$request->type)
            ->get();
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            'wallets'=>$virtualWallets,
        ]));

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\VirtualWallet  $virtualWallet
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(VirtualWallet $virtualWallet)
    {
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            'wallet'=>$virtualWallet,
        ]));
    }
        /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'name'=>"required|max:15|min:2",
            'image'=>"required",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required",
            'goal.amount'=>"required|numeric",
            'goal.currency'=>"required",
            'type' => 'required|in:'.join(",",collect($this->types)->keys()->toArray())
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-VIRTUAL-WALLET",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_VIRTUAL_WALLET_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }


        $virtualWallet=VirtualWallet::create([
            "name"     =>$request->name,
            "image"   =>$request->image,
            "amount"=>[
                "amount"=> $request->input("amount.amount"),
                "currency"=>  $request->input("amount.currency"),
            ],
            "goal"=>[
                "amount"=> $request->input("goal.amount"),
                "currency"=>  $request->input("goal.currency"),
            ],
            "type"=>$request->type,
            "status" =>1,
        ]);


        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-VIRTUAL-WALLET-$virtualWallet->id",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));

    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\VirtualWallet  $virtualWallet
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function update(Request $request, VirtualWallet $virtualWallet)
    {
        $validator=validator()->make($request->all(),[
            'name'=>"required|max:15|min:2",
            'image'=>"required",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required",
            'goal.amount'=>"required|numeric",
            'goal.currency'=>"required",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-VIRTUAL-WALLET",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_VIRTUAL_WALLET_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $virtualWallet->name=$request->name;
        $virtualWallet->image=$request->image;
        $virtualWallet->amount=CurrencyAmountData::from($request->amount)->toArray();
        $virtualWallet->goal=CurrencyAmountData::from($request->goal)->toArray();
        $virtualWallet->save();

        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully update item"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        )));

    }
    /**
     * Delete the specified resource in storage.
     *
     * @param  \App\Models\VirtualWallet  $virtualWallet
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(VirtualWallet $virtualWallet)
    {

        $virtualWallet->delete();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully delete item"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }

}
