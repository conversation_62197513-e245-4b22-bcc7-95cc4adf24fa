<?php

namespace App\Data;
use Illuminate\Support\Collection;

use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\CursorPaginatedDataCollection;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Optional;
use Spatie\LaravelData\PaginatedDataCollection;
use Illuminate\Pagination\AbstractCursorPaginator;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Support\Enumerable;
use Illuminate\Support\LazyCollection;
use Illuminate\Contracts\Pagination\CursorPaginator as CursorPaginatorContract;
use Illuminate\Contracts\Pagination\Paginator as PaginatorContract;
use Illuminate\Validation\Rule;
use Spatie\LaravelData\Support\Validation\ValidationContext;
class AppVersionHistoryItemData extends BaseNonNullableData
{

    public function __construct(
        //public ?int $id,
        public ?string $name,
        public ?string $description,
        public ?int $version,
        public ?array $os_types,
        //public ?int $available,
        //public ?int $status,
    ) {
    }
   
    public static function prepareForPipeline(array $properties) : array
    {
        $locale=app()->getLocale();
        $properties['description']= data_get($properties,"description.$locale");
        return $properties;
    }
    
}
