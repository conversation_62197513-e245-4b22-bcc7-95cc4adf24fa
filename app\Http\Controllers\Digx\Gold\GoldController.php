<?php

namespace App\Http\Controllers\Digx\Gold;

use App\Data\AccountConfigData;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralItemData;
use App\Data\GeneralResponseData;
use App\Data\GoldGeneralItemData;
use App\Data\NameData;
use App\Data\OBDX\ExchangeRateData;
use App\Data\ReceiptData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\LimitTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Enums\TransactionStatusEnum;
use App\Models\ExchangeRate;
use App\Models\News;
use App\Models\VirtualWallet;
use App\Services\OBDX\CustomerService;
use App\Traits\AuthorizesServices;
use Carbon\Carbon;

use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\Gold;
use App\Models\GoldTransaction;
use Illuminate\Http\Request;
use App\Services\FlexService;
use App\Services\NotificationService;
use App\Services\OBDX\UtilsService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class GoldController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user=$request->user()->userProfile;

        $object=new \stdClass();
        $object->reference_id= round(microtime(true));
        $object->partyId= $user->partyId->value;
        $result=FlexService::goldRequestList($object);
        return response()->json($result);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        //
    }
    public function goldConfig(Request $request){
        $goldConfig=app(\App\Settings\ConfigSettings::class)->goldConfig;

        $goldConfig->term= \Cache::tags(['goldTerm'])->rememberForever('goldConfig.term',function (){
            $contents = \File::get(base_path( "/resources/mocks/utils/goldterm.json"));
            return NameData::from(json_decode($contents));
        });

        // $contents = \File::get(base_path( "/resources/mocks/utils/goldterm.json"));
        // $goldConfig->term=NameData::from(json_decode($contents));

        $goldConfig->goldTypes=$goldConfig->goldTypes->filter(function (GoldGeneralItemData $element,int $key)use($request){
            return $element->status==1 && $element->appVersion<=($request->header('appVersion')??0);
        });
        $goldConfig->goldTypes
        ->each(function(&$item) use($request,$goldConfig){
            $item->services= $item->services->filter(function (GoldGeneralItemData $element,int $key) use($request){
                return $element->status==1 && $element->appVersion<=($request->header('appVersion')??0);
            })->values();
            if($item->id==CurrencyTypeEnum::G21->value){
                $goldConfig->services=$item->services;
            }
        });
        // $goldConfig->services=$goldConfig->services->filter(function (GoldGeneralItemData $element,int $key)use($request) {
        //     return $element->status==1 && $element->appVersion<=($request->header('appVersion')??0);
        // })->values();
        $goldConfig->features=$goldConfig->features->filter(function (GeneralItemData $element,int $key) {
            return $element->status==1;
        })->values();

        return response()->json($goldConfig->toAppJson(),200);
    }
    public function home(Request $request)
    {

        $validator=validator()->make($request->all(),[
            'debitAccountId.value'=>"required|max:20|min:20",
            'creditAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
               'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "INDEX-GOLD",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_HOME_100",
                        "type"    => "INFO"
                    ]
                ]
            ]));
        }

        $user=auth()->user();

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));
        $amount=CurrencyAmountData::from($request->input("amount"));

        $result=new \stdClass();
        $goldAccount=null;
        $linkedAccount=null;

        if(in_array($debitAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$debitAccountId;
            $linkedAccount=$creditAccountId;
        }else if(in_array($creditAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$creditAccountId;
            $linkedAccount=$debitAccountId;
        }

        $result->exchangeRates=[];
        if(!is_null($goldAccount) && !is_null($linkedAccount)){
            $result->exchangeRates[]=UtilsService::exchange(
                $amount,
                $goldAccount,
                $linkedAccount
            );
            $result->exchangeRates[]=UtilsService::exchange(
                $amount,
                $linkedAccount,
                $goldAccount
            );
        }

        if(count($result->exchangeRates)!=2 || ($goldAccount->partyId()!="0000000" && $user->id!=$goldAccount->partyId())){
            return  response()->json(GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_100",
                        "type"    => "ERROR"
                    ]
                ]
            )));
        }

        $result->items=[];
        if($goldAccount->partyId()!="0000000"){
            $trans=CustomerService::history($request,$goldAccount->value,3);
            if(!($trans instanceof GeneralResponseData)){
                $result->items=$trans;
            }
        }

        if(($request->header('appVersion')??0)>178){
            if(($request->header('appVersion')??0)>179){
                if($request->firstLoad){
                    $newsDate=Carbon::now()->subDays(3)->startOfDay()->startOfHour()->toDateTimeString();
                    $whareDate=$this->getDateByConnection("'$newsDate'",'YYYY-MM-DD HH24:MI:SS');

                    $result->news= News::select( "id",
                        "title as name",
                        "description",
                        "image as icon",
                        "url",
                        "source_name",
                        "source_icon",
                        'created_at as date'
                    )->where('status',1)
                    ->where('type','gold')
                    ->whereDate('created_at','>=',DB::raw($whareDate))
                    ->take($request->limit??3)
                    ->orderBy('created_at','DESC')
                    ->get();

                    if($goldAccount->partyId()!="0000000"){
                        $result->virtualWallets= VirtualWallet::select( "id",
                            'name','image','amount','goal','type'
                        )->where('status',1)
                        ->where('type','gold')
                        ->get();
                    }
                    //$result->news= GeneralItemData::collect(json_decode(\File::get(base_path( "/resources/mocks/utils/gold_news.json"))));
                }

            }
            $result->charts=$this->chart($request,'array');

            if(env( 'DB_CONNECTION')=='oracle' && $goldAccount->partyId()!="0000000"){
                $currentRate=0;//collect($result->exchangeRates);//->sum('rateNet.amount');
                foreach ($result->exchangeRates as $key => $value) {
                    if($value->debitRate->amount>$value->creditRate->amount && $value->debitRate->currency==CurrencyTypeEnum::YER->value){
                        $currentRate+=$value->debitRate->amount;
                    }
                    // else{
                    //     $currentRate+=$value->creditRate->amount;
                    // }
                }
               // $currentRate=$currentRate/2;
                if($user->id=="0183415" && ($request->header('appVersion')??0)>179){

                    $charts=Gold::select('id',DB::raw("json_value(\"AMOUNT\", '$.\"amount\"') as amount"),
                        DB::raw("{$this->getFormatedDateByConnection('created_at','YYYY-MM-DD HH24:MI:SS')} as created_date"),
                        DB::raw("case when json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%21G%' or json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%24G%'
                            then 'sell' ELSE 'buy' END as type"),
                        // DB::raw("coalesce(cast(json_value(\"EARN\", '$.\"amount\"') as number),(($currentRate - greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number)))))
                        //     as rate"),
                        // DB::raw("case when json_value(\"CREDIT_ACCOUNT_ID\", '$.\"value\"') like '%21G%' or json_value(\"CREDIT_ACCOUNT_ID\", '$.\"value\"') like '%24G%'
                        //     then $currentRate
                        //     ELSE
                        //     LAG (greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number))) OVER (
                        //                            PARTITION BY type
                        //                            ORDER BY type asc,created_at asc)
                        //     END -
                        //     greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number))
                        //     as rate"),

                        DB::raw("(($currentRate - greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number)))) as rate")
                    )
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->whereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value])
                    ->where(function($query) use($goldAccount){
                        $query->where('credit_account_id->value',$goldAccount->value)
                        ->orWhere('debit_account_id->value',$goldAccount->value);
                    })
                    ->whereNull('manual_type')
                    ->orderBy("created_at")
                    ->get();
                    $result->charts->scores=collect($charts)->map(function($chart,$key){
                        return [
                            'identifier'  =>$key,
                            'type'  =>$chart->type,
                            'date'  =>$chart->created_date,
                            'amount'  =>$chart->amount,
                            'buy'   =>$chart->rate,
                            'sell'   =>$chart->rate,
                        ];
                    })->toArray();
                }

                if(($request->header('appVersion')??0)>=200){
                    $profit=DB::query()
                    ->fromSub(
                        Gold::select(
                            DB::raw("SUM(
                                CASE
                                    WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                                    OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                                    THEN CAST(json_value(EXCHANGE_RATE, '$.\"amount\".\"limit\".\"amount\"') AS NUMBER)
                                    ELSE 0
                                END
                            ) AS sum_sell_amount"),

                            DB::raw("SUM(
                                CASE
                                    WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                                    OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                                    THEN 0
                                    ELSE CAST(json_value(EXCHANGE_RATE, '$.\"amount\".\"limit\".\"amount\"') AS NUMBER)
                                END
                            ) AS sum_buy_amount"),

                            DB::raw("SUM(
                                CASE
                                    WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                                    OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                                    THEN CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                                    ELSE 0
                                END
                            ) AS sum_sell_qty"),

                            DB::raw("SUM(
                                CASE
                                    WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                                    OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                                    THEN 0
                                    ELSE CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                                END
                            ) AS sum_buy_qty"),


                            DB::raw("SUM(
                                CASE
                                    WHEN manual_type='storage'
                                    THEN 0
                                    ELSE (
                                        CASE
                                        WHEN json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%21G%'
                                        OR json_value(DEBIT_ACCOUNT_ID, '$.\"value\"') LIKE '%24G%'
                                        THEN -1*CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                                        ELSE CAST(json_value(AMOUNT, '$.\"amount\"') AS NUMBER)
                                        END
                                    )
                                END
                            ) AS balance")
                            )
                            ->where('status',TransactionStatusEnum::COMPLETED->value)

                            ->where(function($query) use($goldAccount){
                                $query->whereNull('limit_type')
                                ->orWhereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value]);
                            })
                            ->where(function($query) use($goldAccount){
                                $query->where('credit_account_id->value',$goldAccount->value)
                                ->orWhere('debit_account_id->value',$goldAccount->value);
                            })
                    ,"dual")
                    ->select(
                        //DB::raw("(($currentRate-(sum_buy_amount/sum_buy_qty))*(sum_buy_qty-sum_sell_qty))+(sum_sell_amount-(sum_buy_amount/sum_buy_qty)*(sum_buy_qty-(sum_buy_qty-sum_sell_qty))) as profit"),
                        DB::raw("(($currentRate-(sum_buy_amount/NULLIF(sum_buy_qty,0)))*(sum_buy_qty-sum_sell_qty))+(sum_sell_amount-(sum_buy_amount/NULLIF(sum_buy_qty,0))*(sum_buy_qty-(sum_buy_qty-sum_sell_qty))) as profit"),
                        DB::raw("balance"),
                        DB::raw("sum_buy_amount/NULLIF(sum_buy_qty,0) as price"),
                    )
                    ->first();

                    $result->charts->profits=[
                        'avg'  =>collect($profit)->toArray()
                    ];
                }
            }
        }


       // $result=json_decode(json_encode($result),true);
        $result=collect($result)->toArray();
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INDEX-GOLD",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional($result));
    }

    public function chart(Request $request,$resultType='json')
    {
        $user=auth()->user();

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));

        $goldAccount=null;
        $linkedAccount=null;

        if(in_array($debitAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$debitAccountId;
            $linkedAccount=$creditAccountId;
        }else if(in_array($creditAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$creditAccountId;
            $linkedAccount=$debitAccountId;
        }

        $charts=new \stdClass();
        $areaBranches=app(\App\Settings\ConfigSettings::class)->exchangeAreaConfig->areaBranches;

        $area=collect($areaBranches)->filter(function ($branches,string $key) use($linkedAccount) {
            return in_array($linkedAccount->branchId(),$branches);
        })->keys()->first();

        //if()

        switch ($request->chartFilter) {
            case 'daily':
                $start=Carbon::now()->subDays(6)->toDateTimeString();
                $end=Carbon::now()->toDateTimeString();
                $createdAt=$this->getFormatedDateByConnection('created_at','YYYY-MM-DD');
                $indentifier=$this->getFormatedDateByConnection('created_at','DD');
                break;
            case 'weekly':
                $start=Carbon::now()->subMonths(1)->toDateTimeString();
                $end=Carbon::now()->endOfMonth()->toDateTimeString();
                $createdAt=$this->getFormatedDateByConnection('created_at','YYYY-MM-DD');
                $indentifier=$this->getFormatedDateByConnection('created_at','IW');
                break;
            case 'monthly':
                $start=Carbon::now()->subYears(1)->addMonth()->startOfMonth()->toDateTimeString();
                $end=Carbon::now()->endOfMonth()->toDateTimeString();
                $createdAt=$this->getFormatedDateByConnection('created_at','YYYY-MM');
                $indentifier=$this->getFormatedDateByConnection('created_at','MM');
                break;
            case 'yearly':
                $start=Carbon::createFromFormat("Y-m-d g:i:s","2023-01-01 00:00:00")->toDateTimeString();
                $end=Carbon::now()->endOfYear()->toDateTimeString();
                $createdAt=$this->getFormatedDateByConnection('created_at','YYYY');
                $indentifier=$this->getFormatedDateByConnection('created_at','YYYY');
                break;
            default:
                $start=Carbon::now()->startOfDay()->toDateTimeString();
                $end=Carbon::now()->endOfDay()->toDateTimeString();
                $createdAt=$this->getFormatedDateByConnection('created_at','YYYY-MM-DD HH24');
                $indentifier=$this->getFormatedDateByConnection('created_at','HH24');
                break;
        }

        $charts->prices=DB::query()
        ->fromSub(
            ExchangeRate::select("area",
                DB::raw("created_at"),
                DB::raw("FIRST_VALUE (buy_rate)
                            OVER (PARTITION BY $createdAt
                            ORDER BY created_at DESC)
                        as end_buy"),
                DB::raw("FIRST_VALUE (sell_rate)
                            OVER (PARTITION BY $createdAt
                            ORDER BY created_at DESC)
                        as end_sell")
            )
            ->where('currency1',$goldAccount->currencyId())
            ->where('area',$area)
            ->whereNotNull('area')
            ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
        ,"dual")
        ->select(
           "area",
            DB::raw("MIN($createdAt) as created_date"),
            DB::raw("$indentifier as indentifier"),
            DB::raw('avg(end_buy) as sell'),
            DB::raw('avg(end_sell) as buy')
        )
        ->groupBy("area")
        //->groupBy("buy_rate")
        //->groupBy("sell_rate")
        ->groupBy(DB::raw("$indentifier"))
        ->orderBy("created_date")
        ->get();


        // $charts->prices=ExchangeRate::select("area",
        // DB::raw("$createdAt as created_date"),
        // DB::raw("$indentifier as indentifier"),
        //  DB::raw('avg(buy_rate) as buy'), DB::raw('avg(sell_rate) as sell'))
        // ->where('currency1',$goldAccount->currencyId())
        // ->where('area',$area)
        // ->whereNotNull('area')
        // ->whereRaw("created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')",[$start,$end])

        // ->groupBy("area")
        // //->groupBy("buy_rate")
        // //->groupBy("sell_rate")
        // ->groupBy(DB::raw("$indentifier"))
        // ->orderBy("created_date")
        // ->get();


        $charts->prices=collect($charts->prices)->map(function($chart){
            return [
                'area'  =>$chart->area,
                //'date'  =>Carbon::parse($chart->created_at)->toDateTimeString(),
                'identifier'  =>$chart->indentifier,
                'date'  =>$chart->created_date,
                'buy'   =>$chart->buy,
                'sell'   =>$chart->sell,
            ];
        })->toArray();
        if($resultType=="array"){
            return $charts;
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INDEX-GOLD",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "charts"=>$charts,
        ]));
    }


    public function changesChart(Request $request,$resultType='json')
    {
        $user=auth()->user();

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));

        $goldAccount=null;
        $linkedAccount=null;

        if(in_array($debitAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$debitAccountId;
            $linkedAccount=$creditAccountId;
        }else if(in_array($creditAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$creditAccountId;
            $linkedAccount=$debitAccountId;
        }

        if($user->id!=$goldAccount->partyId()){
            return  abort(response()->json(GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_100",
                        "type"    => "ERROR"
                    ]
                ]
            ))));
        }

        $charts=new \stdClass();
        $charts->changes=new \stdClass();
        $areaBranches=app(\App\Settings\ConfigSettings::class)->exchangeAreaConfig->areaBranches;

        $area=collect($areaBranches)->filter(function ($branches,string $key) use($linkedAccount) {
            return in_array($linkedAccount->branchId(),$branches);
        })->keys()->first();

        //if()
        $whareDate=$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS');
        $changes=[
            'hourly'=>[
                'start'=>Carbon::now()->startOfDay()->toDateTimeString(),
                'end'=>Carbon::now()->endOfDay()->toDateTimeString(),
                'createdAt'=>$this->getFormatedDateByConnection('created_at','YYYY-MM-DD HH24'),
                'indentifier'=>$this->getFormatedDateByConnection('created_at','HH24'),
                'pCreatedAt'=>$this->getSubDateByConnection('created_at','1','HOUR'),
                'pRangeAt'=>"created_at between {$this->getSubDateByConnection($whareDate,'1','DAY')} and {$this->getSubDateByConnection($whareDate,'1','DAY')}",

            ],
            'daily'=>[
                'start'=>Carbon::now()->subDays(6)->startOfDay()->toDateTimeString(),
                'end'=>Carbon::now()->endOfDay()->toDateTimeString(),
                'createdAt'=>$this->getFormatedDateByConnection('created_at','YYYY-MM-DD'),
                'indentifier'=>$this->getFormatedDateByConnection('created_at','DD'),
                'pCreatedAt'=>$this->getSubDateByConnection('created_at','1','DAY'),
                'pRangeAt'=>"created_at between {$this->getSubDateByConnection($whareDate,'1','DAY')} and {$this->getSubDateByConnection($whareDate,'6','DAY')}",
            ],
            'monthly'=>[
                'start'=>Carbon::now()->subYears(1)->startOfMonth()->startOfDay()->toDateTimeString(),
                'end'=>Carbon::now()->endOfYear()->toDateTimeString(),
                'createdAt'=>$this->getFormatedDateByConnection('created_at','YYYY-MM'),
                'indentifier'=>$this->getFormatedDateByConnection('created_at','MM'),
                'pCreatedAt'=>$this->getSubDateByConnection('created_at','1','MONTH'),
                'pRangeAt'=>"created_at between {$this->getSubDateByConnection($whareDate,'1','MONTH')} and {$this->getSubDateByConnection($whareDate,'12','MONTH')}",
            ],
        ];
        foreach ($changes as $key => $value) {
            $start=$value['start'];
            $end=$value['end'];
            $createdAt=$value['createdAt'];
            $indentifier=$value['indentifier'];
            $pCreatedAt=$value['pCreatedAt'];
            $pRangeAt=$value['pRangeAt'];

            // $chart=DB::query()
            // ->fromSub(
            //     // DB::query()
            //     // ->select(
            //     //     "area",
            //     //     "created_at",
            //     //     DB::raw("FIRST_VALUE (buy_difference)
            //     //                 OVER (PARTITION BY $createdAt
            //     //                 ORDER BY created_at DESC)
            //     //             as buy_difference"),
            //     //     DB::raw("FIRST_VALUE (sell_difference)
            //     //                 OVER (PARTITION BY $createdAt
            //     //                 ORDER BY created_at DESC)
            //     //             as sell_difference")
            //     // )
            //     // ->fromSub(

            //     //,"dual")
            // ,"dual")
            // ->select(
            //    "area",
            //     DB::raw("MIN($createdAt) as created_date"),
            //     DB::raw("$indentifier as indentifier"),
            //     DB::raw('SUM(coalesce(buy_difference,0)) as buy'),
            //     DB::raw('SUM(coalesce(sell_difference,0)) as sell')
            // )
            // ->groupBy("area")
            // //->groupBy("buy_rate")
            // //->groupBy("sell_rate")
            // ->groupBy(DB::raw("$indentifier"))
            // ->orderBy("created_date")
            // ->get();



            $chart=DB::query()
            // ->select(
            //     "area",
            //     "created_date",
            //     "indentifier",
            //     DB::raw("(end_buy-LAG(end_buy)
            //                 OVER (PARTITION BY area ORDER BY created_date)) as buy"),
            //     DB::raw("(end_sell-LAG(end_sell)
            //                 OVER (PARTITION BY area ORDER BY created_date)) as sell")
            // )
            ->fromSub(
                DB::query()
                ->select(
                    "area",
                    "created_date",
                    "indentifier",
                    DB::raw("(end_buy-LAG(end_buy)
                                OVER (PARTITION BY area ORDER BY created_date)) as sell"),
                    DB::raw("(end_sell-LAG(end_sell)
                                OVER (PARTITION BY area ORDER BY created_date)) as buy")
                )
                ->fromSub(
                    DB::query()
                        ->select(
                            "area",
                            DB::raw("$createdAt as created_date"),
                            DB::raw("MIN($indentifier) as indentifier"),
                            DB::raw('MAX(end_buy) as end_buy'),
                            DB::raw('MAX(end_sell) as end_sell')
                        )
                        ->fromSub(
                            ExchangeRate::select("area",
                                DB::raw("created_at"),
                                DB::raw("FIRST_VALUE (buy_rate)
                                            OVER (PARTITION BY $createdAt
                                            ORDER BY created_at DESC)
                                        as end_buy"),
                                DB::raw("FIRST_VALUE (sell_rate)
                                            OVER (PARTITION BY $createdAt
                                            ORDER BY created_at DESC)
                                        as end_sell")
                            )
                            ->where('currency1',$goldAccount->currencyId())
                            ->where('area',$area)
                            ->where('type','ATM')
                            ->whereNotNull('area')
                            ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                            ->union(
                                ExchangeRate::select("area",
                                    DB::raw("created_at"),
                                    DB::raw("FIRST_VALUE (buy_rate)
                                                OVER (PARTITION BY $createdAt
                                                ORDER BY created_at DESC)
                                            as end_buy"),
                                    DB::raw("FIRST_VALUE (sell_rate)
                                                OVER (PARTITION BY $createdAt
                                                ORDER BY created_at DESC)
                                            as end_sell")
                                )
                                ->where('currency1',$goldAccount->currencyId())
                                ->where('area',$area)
                                ->where('type','ATM')
                                ->whereNotNull('area')
                                ->whereRaw("$pRangeAt",[$start,$end])
                                ->orderBy("created_at")
                                ->take(1)
                            )
                        ,"dual")
                        ->groupBy("area")
                        ->groupBy(DB::raw("$createdAt"))
                        ->orderBy("created_date")
                ,"dual")
                ->orderBy("created_date")
            ,"dual")
            ->whereNotNull('buy')
            ->orderBy("created_date")
            ->get();
            // $chart=ExchangeRate::select("area",
            //     DB::raw("MIN($createdAt) as created_date"),
            //     DB::raw("$indentifier as indentifier"),
            //     DB::raw('avg(buy_difference) as buy'), DB::raw('avg(sell_difference) as sell'))
            // ->where('currency1',$goldAccount->currencyId())
            // ->where('area',$area)
            // ->whereNotNull('area')
            // ->whereRaw("created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')",[$start,$end])

            // ->groupBy("area")
            // //->groupBy("buy_rate")
            // //->groupBy("sell_rate")
            // ->groupBy(DB::raw("$indentifier"))
            // ->orderBy("created_date")
            // ->get();

            $charts->changes->{"$key"}=collect($chart)->map(function($chart){
                return [
                    'area'  =>$chart->area,
                    //'date'  =>Carbon::parse($chart->created_at)->toDateTimeString(),
                    'identifier'  =>$chart->indentifier,
                    'date'  =>$chart->created_date,
                    'buy'   =>$chart->buy,
                    'sell'   =>$chart->sell,
                ];
            })->toArray();
        }

        if($resultType=="array"){
            return $charts;
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INDEX-GOLD",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "charts"=>$charts,
        ]));
    }

    public function charts(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'debitAccountId.value'=>"required|max:20|min:20",
            'creditAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
               'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "INDEX-GOLD",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_HOME_100",
                        "type"    => "INFO"
                    ]
                ]
            ]));
        }

        $user=auth()->user();

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));
        $amount=CurrencyAmountData::from($request->input("amount"));

        $result=new \stdClass();
        $goldAccount=null;
        $linkedAccount=null;

        if(in_array($debitAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$debitAccountId;
            $linkedAccount=$creditAccountId;
        }else if(in_array($creditAccountId->currencyId(),[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $goldAccount=$creditAccountId;
            $linkedAccount=$debitAccountId;
        }
        //$linkedAccount->value=str_replace('');
        $linkedAccount->value=substr_replace($linkedAccount->value,CurrencyTypeEnum::YER->value,10, 3);

        $result->exchangeRates=[];
        if(!is_null($goldAccount) && !is_null($linkedAccount)){
            $result->exchangeRates[]=UtilsService::exchange(
                $amount,
                $goldAccount,
                $linkedAccount
            );
            $result->exchangeRates[]=UtilsService::exchange(
                $amount,
                $linkedAccount,
                $goldAccount
            );
        }

        if(count($result->exchangeRates)!=2 || $user->id!=$goldAccount->partyId()){
            return  response()->json(GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_100",
                        "type"    => "ERROR"
                    ]
                ]
            )));
        }

        $result->charts=$this->chart($request,'array');
        $result->charts->changes=$this->changesChart($request,'array')->changes;

        if(env('DB_CONNECTION')=='oracle' && auth()->user()->id=="0183415"){
            $currentRate=0;//collect($result->exchangeRates);//->sum('rateNet.amount');
            foreach ($result->exchangeRates as $key => $value) {
                if($value->debitRate->amount>$value->creditRate->amount && $value->debitRate->currency==CurrencyTypeEnum::YER->value){
                    $currentRate+=$value->debitRate->amount;
                }
                // else{
                //     $currentRate+=$value->creditRate->amount;
                // }
            }
            //$currentRate=$currentRate/2;
            $charts=Gold::select('id',DB::raw("json_value(\"AMOUNT\", '$.\"amount\"') as amount"),
                DB::raw("{$this->getFormatedDateByConnection('created_at','YYYY-MM-DD HH24:MI:SS')} as created_date"),
                DB::raw("case when json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%21G%' then 'sell' when json_value(\"DEBIT_ACCOUNT_ID\", '$.\"value\"') like '%24G%' then 'sell' ELSE 'buy' END as type"),
                // DB::raw("coalesce(cast(json_value(\"EARN\", '$.\"amount\"') as number),(($currentRate - greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number)))))
                // as rate"),
                // DB::raw("case when json_value(\"CREDIT_ACCOUNT_ID\", '$.\"value\"') like '%21G%' or json_value(\"CREDIT_ACCOUNT_ID\", '$.\"value\"') like '%24G%'
                //             then $currentRate
                //             ELSE
                //             LAG (greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number))) OVER (
                //                                    PARTITION BY type
                //                                    ORDER BY type asc,created_at asc)
                //             END -
                //             greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number))
                //             as rate"),

                DB::raw("(($currentRate -greatest(cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"debitRate\".\"amount\"') as number),cast(json_value(\"EXCHANGE_RATE\", '$.\"amount\".\"creditRate\".\"amount\"') as number)))) as rate")
            )
            ->where('status',TransactionStatusEnum::COMPLETED->value)
            ->whereIn('limit_type',[LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value])
            ->where(function($query) use($goldAccount){
                $query->where('credit_account_id->value',$goldAccount->value)
                ->orWhere('debit_account_id->value',$goldAccount->value);
            })
            ->whereNull('manual_type')
            ->orderBy("created_at")
            ->get();
            $result->charts->scores=collect($charts)->map(function($chart,$key){
                return [
                    'identifier'  =>$key,
                    'type'  =>$chart->type,
                    'date'  =>$chart->created_date,
                    'amount'  =>$chart->amount,
                    'buy'   =>$chart->rate,
                    'sell'   =>$chart->rate,
                ];
            })->toArray();
        }
       // $result=json_decode(json_encode($result),true);
        $result=collect($result)->toArray();
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INDEX-GOLD",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional($result));
    }

        /**
     * Display the specified resource.
     *
     * This method generates a PDF document with the title "Hello World" and content "Hello World".
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transfer $transfer
     * @return void
     */
    public function receipt(Request $request,Gold $gold)
    {
        $validator=validator()->make($request->all(),[
            'showAccountInfo' => 'Nullable|numeric|in:0,1',
            'showAccountNumber' => 'Nullable|numeric|in:0,1',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "RECEIPT-GOLD",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_GOLD_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $this->generateReceipt($this->getReceiptData($gold));
    }
    protected function getReceiptData(Gold $gold,?GoldTransaction $transaction=null): ReceiptData
    {
        $transaction??=$gold->transactions->last();

        $creditAccountId=AccountIdData::from($gold->credit_account_id);
        $debitAccountId=AccountIdData::from($gold->debit_account_id);

        $amount=$gold->exchange_rate->amount->total;
        $title="";
        if($debitAccountId->isGold() && $creditAccountId->isGold()){
            $amount=$gold->amount;
            $title=__("Gold transfer");
        }else if($creditAccountId->isGold()){
            $title=__("Buy gold");
        }else{
            $title=__("Sell gold");
        }


        return ReceiptData::from([
            "id"=> $gold->id,
            "date"=> date_format(date_create($gold->created_at), "Y-m-d H:i:s"),
            "title"=>$title,
            "beneficiary"=> $creditAccountId->value,
            "statement"=>$transaction->remarks,
            "details"=> [
                "referenceId"=> $transaction->external_reference_id,
                "debitAccountId"=> $debitAccountId->toArray()??null,
                //"remarks"=>$transaction->remarks,
                "amount"=> $amount,
                "fee"=>$transaction->debit_fee??null,
            ]
        ]);
    }
    /**
     * Store a newly created resource in storage.
     *
     * Initate gold buying/selling process
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        //$icons = File::get(base_path('resources/mocks/utils/icons.json'));
        $this->available([
            ServiceTagEnum::GOLD_PLATFORM
        ]);

        $validator=validator()->make($request->all(),[
            'creditAccountId.value'=>"required|max:20|min:20",
            'debitAccountId.value'=>"required|max:20|min:20",
            'feeAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|integer",
            'amount.currency'=>"required|max:3|min:3|in:21G,24G"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GOLD",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_GOLD_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));
        $feeAccountId=AccountIdData::from($request->input("feeAccountId"));
        $amount=CurrencyAmountData::from($request->input("amount"));

        if(($request->header('appVersion')??0)<181 && $amount->currency==CurrencyTypeEnum::G24->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GOLD",
                    "message"   => [
                        "title"   => __("Please update app to the lastest version to complete this transaction!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $user=$request->user()->userProfile;

        // Load gold config
        $goldConfig=app(\App\Settings\ConfigSettings::class)->goldConfig;
        $goldConfig->term=NameData::from([
            "ar"=>"",
            "en"=>"",
        ]);
        //$debitAccountId->allowedService(AccountConfigData::goldTransfer);
        $valid=false;
        $message="You don't have the correct account!";
        // valdite debitAccountId is not gold & in allawed branches,products,currencies
        if(!in_array($debitAccountId->productId(),ProductData::gold()) &&
            !$debitAccountId->allowedService(AccountConfigData::goldTransfer)
        ){
            $message="You don't have the correct account to buy gold!";
        // valdite creditAccountId is not gold & in allawed branches,products,currencies
        } else if(!in_array($creditAccountId->productId(),ProductData::gold())  &&
            !$creditAccountId->allowedService(AccountConfigData::goldTransfer)
        ){
            $message="You don't have the correct account to sell gold!";
        // valdite feeAccountId is in allawed branches,products,currencies
        } else if(
            !$feeAccountId->allowedService(AccountConfigData::goldTransfer)
        ){
            $message="You don't have the correct account to buy/sell gold!";
        }else{
            $result=CustomerService::account($request, $debitAccountId->value);
            if($result instanceof AccountData){
                $account=$result;
                if($account->status!="ACTIVE"){
                    $message="Your account not active!";
                }else if($account->currencyId!=$amount->currency && $creditAccountId->currencyId()!=$amount->currency){
                    $message="Account currency not match!";
                }else if($account->isGold() && !in_array($creditAccountId->productId(),ProductData::gold()) && $creditAccountId->value!=$feeAccountId->value){
                    $message="Account of credit & fee not match!";
                }else if(!$account->isGold() && in_array($creditAccountId->productId(),ProductData::gold()) && $debitAccountId->value!=$feeAccountId->value){
                    $message="Account of debit & fee not match!";
                }else if($account->isGold() && in_array($creditAccountId->productId(),ProductData::gold()) && in_array($feeAccountId->productId(),ProductData::gold())){
                    $message="Account of debit/credit & fee not match!";
                }else{
                    if($debitAccountId->value!=$feeAccountId->value){
                        $result=CustomerService::account($request, $feeAccountId->value);
                        if($result instanceof AccountData){
                            $accountFee=$result;
                            if($accountFee->status!="ACTIVE"){
                                $message="Your fee account not active!";
                            }else{
                                $valid=true;
                            }
                        }
                    }else{
                        $valid=true;
                    }
                }
            }
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GOLD",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        if($account->isGold()){
            $goldAccount=$account->id;
            //$notGoldAccount=$creditAccountId;
        }else{
            $goldAccount=$creditAccountId;
            //$notGoldAccount=$account->id;
        }


        // Getting exchange rate of amount to equlevent local currency
        $exchangeRate= UtilsService::exchange(
            CurrencyAmountData::from($request->input("amount")),
            $debitAccountId,
            $creditAccountId
        );

        // Start getting charge fee
        $object=new \stdClass();
        if($debitAccountId->currencyId()==$creditAccountId->currencyId()){
            $object->chargeProductId=$goldConfig->chargeTransferProductId;
        }else{
            if(in_array($debitAccountId->branchId()??"",BranchData::south()) || in_array($creditAccountId->branchId()??"",BranchData::south())){
                $object->chargeProductId=$goldConfig->chargeProductIdSouth;
            }else{
                $object->chargeProductId=$goldConfig->chargeProductId;
            }
        }
        $object->branchId= $account->branchId;
        $object->productId=$goldAccount->productId();
        $object->amount= $amount->amount;
        $object->currency= $amount->currency;

        $result=FlexService::chargeFee($object);
        if($result->status->message->code!="0"){
            return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }
        $chargeFee=$result->getAdditionalData()['fee'];

        if($debitAccountId->currencyId()==$creditAccountId->currencyId()){
            $chargeFee->fee->amount=0;
        }

        // Getting exchange rate of fee to equlevent local currency
        $feeAccount=AccountIdData::from([
            "value"=>substr_replace($feeAccountId->value,$chargeFee->fee->currency,10, 3)
        ]);
        $exchangeChargeRate= UtilsService::exchange(
            $chargeFee->fee,
            $feeAccountId,
            $feeAccount
        );

        $creditExchangeChargeRate= UtilsService::exchange(
            $chargeFee->fee,
            $feeAccount,
            $feeAccountId
        );

        $limitType=
            $debitAccountId->currencyId()==$creditAccountId->currencyId()?
                LimitTypeEnum::GOLD_INTERNAL:(
                    $debitAccountId->isGold()?
                    LimitTypeEnum::GOLD_SELL:LimitTypeEnum::GOLD_BUY
                );
        $gold=Gold::create([
            "party_id"          =>$user->partyId->value,
            "status"            =>TransactionStatusEnum::INIT->value,
            "limit_type"        =>$limitType->value,
            "credit_account_id" =>$creditAccountId->toArray(),
            "debit_account_id"  =>$debitAccountId->toArray(),
            "fee_account_id"    =>$feeAccountId->toArray(),
            "amount"            =>$amount->toArray(),
            "fee"               =>$chargeFee->fee->toArray(),
            "exchange_rate"=>[
                "amount"        =>$exchangeRate->toArray(),
                "fee"           =>[
                    "debit"           =>$exchangeChargeRate->toArray(),
                    "credit"          =>$creditExchangeChargeRate->toArray(),
                ]
            ],
            "remarks"=>$request->remarks
        ]);
        LogItem::store($gold);


        $fee=$gold->exchange_rate->fee->debit->total;
        $feeRounded=round($fee->amount,2);

        $rate=$gold->exchange_rate->amount->rateNet??null;

        $debit_amount=new \stdClass();
        $credit_amount=new \stdClass();
        if($debitAccountId->currencyId()==$creditAccountId->currencyId()){
            // Set net amount to debit
            $debit_amount->net=$gold->amount;
            // Set total amount to debit in this case it equal net amount
            $debit_amount->total=$debit_amount->net;
            // Set total amount to debit in this case it equal local amount
            $debit_amount->local=$debit_amount->net;

            // Set net amount to credit in this case it equal local amount
            $credit_amount->net=$gold->amount;
            // Set total amount to credit in this case it equal net amount + fee amount
            $credit_amount->total=$credit_amount->net;
            // Set local amount to credit in this case it equal net
            $credit_amount->local=$credit_amount->net;

            $statement=sprintf(trans("transfer_gold_remark"),
                "{$gold->amount->amount} ".__($gold->amount->currency),
                substr($gold->amount->currency,0,2),
                "({$creditAccountId->value})",
                //"{$feeRounded} {$fee->currency}",
                empty($gold->remarks)?"":"\n[{$gold->remarks}]"
            );

        }else if($debitAccountId->currencyId()==$gold->amount->currency){
            // Set net amount to debit
            $debit_amount->net=$gold->amount;
            // Set total amount to debit in this case it equal net amount
            $debit_amount->total=$debit_amount->net;
            // Set total amount to debit in this case it equal local amount
            $debit_amount->local=$gold->exchange_rate->amount->total;

            // Set net amount to credit in this case it equal local amount
            $credit_amount->net=$gold->exchange_rate->amount->total;
            // Set total amount to credit in this case it equal net amount + fee amount
            $credit_amount->total=CurrencyAmountData::from([
                "amount"=>$credit_amount->net->amount -$fee->amount,
                "currency"=>$credit_amount->net->currency,
            ]);
            // Set local amount to credit in this case it equal net
            $credit_amount->local=$credit_amount->net;

            $statement=sprintf(trans("sell_gold_remark"),
                "{$gold->amount->amount} ".__($gold->amount->currency),
                substr($gold->amount->currency,0,2),
                round($credit_amount->net->amount,2)." {$credit_amount->net->currency}",
               // "{$feeRounded} {$fee->currency}",
                round($rate->amount,2)." {$rate->currency}",
                __($gold->amount->currency),
                empty($gold->remarks)?"":"\n[{$gold->remarks}]"
            );

        }else if($creditAccountId->currencyId()==$gold->amount->currency){
            // Set net amount to debit in this case it equal local amount
            $debit_amount->net=$gold->exchange_rate->amount->total;
            // Set total amount to debit in this case it equal net amount + fee amount
            $debit_amount->total=CurrencyAmountData::from([
                "amount"=>$debit_amount->net->amount + $fee->amount,
                "currency"=>$debit_amount->net->currency,
            ]);
            // Set local amount to debit in this case it equal net
            $debit_amount->local=$debit_amount->net;

            // Set net amount to credit
            $credit_amount->net=$gold->amount;
            // Set total amount to credit in this case it equal net amount
            $credit_amount->total=$credit_amount->net;
            // Set total amount to credit in this case it equal local amount
            $credit_amount->local=$gold->exchange_rate->amount->total;

            $statement=sprintf(trans("buy_gold_remark"),
                "{$gold->amount->amount} ".__($gold->amount->currency),
                substr($gold->amount->currency,0,2),
                round($debit_amount->net->amount,2)." {$debit_amount->net->currency}",
                //"{$feeRounded} {$fee->currency}",
                round($rate->amount,2)." {$rate->currency}",
                __($gold->amount->currency),
                empty($gold->remarks)?"":"\n[{$gold->remarks}]"
            );
        }else{
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GOLD",
                    "message"   => [
                        "title"   => "No account match currency",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        // $debit->amount=round($debit->amount,3);
        // $credit->amount=round($credit->amount,3);
        // $debitLocal->amount=round($debitLocal->amount,3);
        // $creditLocal->amount=round($creditLocal->amount,3);

        GoldTransaction::create([
            "gold_id"       =>$gold->id,
            "type"          =>InvoiceTransactionsTypeEnum::Payment,
            "status"        =>TransactionStatusEnum::INIT->value,
            "debit_amount"   =>collect($debit_amount)->toArray(),
            "credit_amount"  =>collect($credit_amount)->toArray(),
            "debit_fee"      =>$fee,
            "credit_fee"     =>$gold->exchange_rate->fee->credit->total,
            //"reference_id"  =>Controller::generateNumericOTP(3)."q",
            "reference_id"   =>round(microtime(true)),
            "remarks"        =>$statement//$request->remarks
        ]);



        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-GOLD-$gold->id",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "paymentId"=>$gold->id,
            //"transferDetails"=>$gold
        ]));

    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $debitAmount=\DB::raw("json_query(debit_amount,'$.total') as debit_amount");
        $creditAmount=\DB::raw("json_query(credit_amount,'$.total') as credit_amount");
        if(env('DB_CONNECTION')!='oracle'){
            $debitAmount="debit_amount->total as debit_amount";
            $creditAmount="credit_amount->total as credit_amount";
        }
        $gold=Gold::with(["transaction"=>function($query) use($debitAmount,$creditAmount){
            $query->select('gold_id',$debitAmount,$creditAmount,"remarks");
        }])->select('id','party_id','status','credit_account_id','debit_account_id','amount','fee','exchange_rate','remarks')
        //->where('party_id',auth()->user()->id)
        ->where('status',TransactionStatusEnum::INIT->value)
        ->find($id);

        if(is_null($gold)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "GOLD-DETAILS",
                    "message"   => [
                        "title"   => __("Transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "GOLD-DETAILS",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "transferDetails"=>$gold,
        ]));

    }
    public static function limits($limitType){
        if(!in_array($limitType,[LimitTypeEnum::GOLD_SELF,LimitTypeEnum::GOLD_SELL,LimitTypeEnum::GOLD_BUY,LimitTypeEnum::GOLD_INTERNAL])){
            return;
        }

        $limits=[];
        $trxLimit=[];

        $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        $assignedLimits=$targetLimitLinkages
        ->filter(function ($element, $key) use($limitType){
            return $element->target->value==$limitType->value &&
             $element->target->area==(request()->input('area')??"") &&
              $element->target->currency==(request()->input('currency')??"");
        })
        //->where('target.value',$limitType->value) &&
        //->where('target.area',request()->input('area')??"")
        //->where('target.currency',request()->input('currency')??"")
        ->first();

        $limitTypes=$limitType==LimitTypeEnum::GOLD_INTERNAL?
            [LimitTypeEnum::GOLD_INTERNAL->value]:
            [LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value];

        // if(request()->filled('area') && request()->filled('currancy')){
        //     if(in_array($limitType,[LimitTypeEnum::GOLD_BUY,LimitTypeEnum::GOLD_SELL,LimitTypeEnum::GOLD_INTERNAL])){
        //         $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        //        // $limitPackageConfig=LimitPackageConfigData::from($limitPackageConfig);
        //         $assignedLimits=$targetLimitLinkages->where('target.value',$limitType->value)
        //         ->where('target.area',request()->input('area')??"")
        //         ->where('target.currancy',request()->input('currancy')??"")
        //         ->first();
        //         $limitTypes=$limitType==LimitTypeEnum::GOLD_INTERNAL?
        //             [LimitTypeEnum::GOLD_INTERNAL->value]:
        //             [LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value];
        //     }else{
        //         return;
        //     }
        // }else if(in_array($limitType,[LimitTypeEnum::GOLD_SELF,LimitTypeEnum::GOLD_INTERNAL])){
        //     $limitTypes[]=$limitType->value;
        //     $assignedLimits=CustomerService::assignedLimits(
        //         $limitType==LimitTypeEnum::GOLD_INTERNAL?
        //         LimitTypeEnum::INTERNATIONAL:
        //         LimitTypeEnum::SELF
        //     );
        // }else{
        //     return;
        // }
        $controller=new Controller();
        $whareDate="updated_at between {$controller->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$controller->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}";

        foreach ($assignedLimits->limits as $item) {
            switch ($item->periodicity??"") {
                case 'DAILY':
                    $limitUtilization=Gold::select("exchange_rate->amount->limit->amount as limits")
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->whereIn('limit_type',$limitTypes)
                    ->whereRaw($whareDate,[Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()])
                    ->whereNull('manual_type')
                    //->whereBetween('updated_at',[\Carbon\Carbon::now()->startOfDay()->format("d/m/Y H:i:s"),\Carbon\Carbon::now()->endOfDay()->format("d/m/Y H:i:s")])
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                case 'MONTHLY':
                    $limitUtilization=Gold::select("exchange_rate->amount->limit->amount as limits")
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->whereIn('limit_type',$limitTypes)
                    ->whereRaw($whareDate,[Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()])
                    ->whereNull('manual_type')
                    //->whereBetween('updated_at',[\Carbon\Carbon::now()->startOfMonth()->format("d/m/Y H:i:s"),\Carbon\Carbon::now()->endOfMonth()->format("d/m/Y H:i:s")])
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                default:
                    if($item->limitType=="TXN" && isset($item->amountRange->minTransaction) && isset($item->amountRange->maxTransaction)){
                        $trxLimit=[
                            "minAmount"=>$item->amountRange->minTransaction,
                            "maxAmount"=>$item->amountRange->maxTransaction,
                        ];
                    }
            }
        }
        $trxLimit["limits"]=$limits;
        //return $this->verifyUser($request,$customer);
        return abort(response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional($trxLimit)));
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Gold  $gold
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request,  Gold $gold)
    {
        $this->available([
            ServiceTagEnum::GOLD_PLATFORM
        ]);

        if(($request->header('appVersion')??0)<181 && $gold->amount->currency==CurrencyTypeEnum::G24->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GOLD",
                    "message"   => [
                        "title"   => __("Please update app to the lastest version to complete this transaction!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $user=$request->user()->userProfile;
        LogItem::store($gold);

        $date=Carbon::parse($gold->created_at)->addSeconds(30);
        if($gold->status!=TransactionStatusEnum::INIT->value || $date->isBefore(Carbon::now())){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GOLD",
                    "message"   => [
                        "title"   => __("This transaction already expired"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $creditAccountId=AccountIdData::from($gold->credit_account_id);
        $debitAccountId=AccountIdData::from($gold->debit_account_id);
        $feeAccountId=AccountIdData::from($gold->fee_account_id);

        $goldTransaction=$gold->transactions()->where("type",InvoiceTransactionsTypeEnum::Payment)->first();
        if(is_null($goldTransaction) || $goldTransaction->status != TransactionStatusEnum::INIT->value){
            return response()->json(GeneralResponseData::from([
                'status' => [
                    "result" => "ERROR",
                    "contextID" => "REFUND-GOLD-$gold->id",
                    "message" => [
                        "title" => __("This transaction not in the correct status!"),
                        "detail" => "",
                        "code" => "DIGX_SWITCH_GOLD_101",
                        "type" => "ERROR"
                    ]
                ]
            ]));
        }

        $equlivent= $gold->amount->currency==CurrencyTypeEnum::G24->value?
        31.1:1;
        $dateTime=Carbon::now();

        $valid=false;
        $message="You don't have enough balance in your account!";
        $result=CustomerService::account($request, $debitAccountId->value);
        if($result instanceof AccountData){
            $account=$result;
            if($account->status!="ACTIVE"){
                $message="Your account not active!";
            }else if($account->currencyId!=$goldTransaction->debit_amount->net->currency){
                $message="Account currency not match!";
            }else if($account->balance->amount<$goldTransaction->debit_amount->net->amount || ($goldTransaction->debit_amount->net->currency==CurrencyTypeEnum::G24->value && $account->balance->amount<round($goldTransaction->debit_amount->net->amount*$equlivent,3)) || ($debitAccountId->value==$feeAccountId->value && $account->balance->amount<$goldTransaction->debit_amount->total->amount)){
                $message="You don't have enough balance in your account!";
            }else{
                if($debitAccountId->value!=$feeAccountId->value){
                    $result=CustomerService::account($request, $feeAccountId->value);
                    if($result instanceof AccountData){
                        $accountFee=$result;
                        if($accountFee->status!="ACTIVE"){
                            $message="Your fee account not active!";
                        }else if($accountFee->currencyId!=$goldTransaction->debit_fee->currency){
                            $message="Account currency not match!";
                        }else if($debitAccountId->currencyId()==$creditAccountId->currencyId() && $accountFee->balance->amount<$goldTransaction->debit_fee->amount && $goldTransaction->debit_fee->amount>0){
                            $message="You don't have enough balance in your account!";
                        }else{
                            $valid=true;
                        }
                    }
                }else{
                    $valid=true;
                }
            }
        }
        $invalidResult=response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-GOLD",
                "message"   => [
                    "title"   => __($message),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_GOLD_101",
                    "type"    => "ERROR"
                ]
             ]
        ]));
        if(!$valid){
            return $invalidResult;
        }


            $limitTypes=$gold->limit_type==LimitTypeEnum::GOLD_INTERNAL?
            [LimitTypeEnum::GOLD_INTERNAL->value]:
            [LimitTypeEnum::GOLD_SELF->value,LimitTypeEnum::GOLD_BUY->value,LimitTypeEnum::GOLD_SELL->value];

            $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
            $result=$targetLimitLinkages
            ->filter(function ($element, $key) use($gold,$feeAccountId){
                return $element->target->value==$gold->limit_type &&
                 $element->target->area==($feeAccountId->isNorth()?'N':'S') &&
                  $element->target->currency==$gold->amount->currency;
            })
            ->first();

            $trxLimit=$result->limits->filter(function ($element, $key){
                return $element->limitType=='TXN';
            })->first();
            if(!is_null($trxLimit) && ($gold->exchange_rate->amount->limit->amount < $trxLimit->amountRange->minTransaction->amount || $gold->exchange_rate->amount->limit->amount > $trxLimit->amountRange->maxTransaction->amount)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CONFIRM-GOLD",
                        "message"   => [
                            "title"   =>sprintf( __("Your transaction amount exceed the allowed limit! Min %s Max %s per transaction"),$trxLimit->amountRange->minTransaction->amount,$trxLimit->amountRange->maxTransaction->amount),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_GOLD_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }

            $whareDate="updated_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}";

            $dailyLimit=$result->limits->filter(function ($element, $key){
                return $element->periodicity=='DAILY';
            })->first();
            if(!is_null($dailyLimit)){
                $limit=Gold::select("exchange_rate->amount->limit->amount as limits")
                ->where(function($query) use($gold){
                    return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->orWhere('id',$gold->id);
                })
                ->whereIn('limit_type',$limitTypes)
                ->whereRaw($whareDate,[Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()])
                ->whereNull('manual_type')
                //->whereBetween('updated_at',[\Carbon\Carbon::now()->startOfDay()->toDateTimeString(),\Carbon\Carbon::now()->endOfDay()->toDateTimeString()])
                ->get();

                if(!is_null($limit) && ($limit->count()>$dailyLimit->maxCount || $limit->sum("limits")>$dailyLimit->maxAmount->amount)){
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "CONFIRM-GOLD",
                            "message"   => [
                                "title"   => __("Your daily account limit exceed the allowed limit!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_GOLD_101",
                                "type"    => "ERROR"
                            ]
                        ]
                    ]));
                }
            }

            $monthlyLimit=$result->limits->filter(function ($element, $key){
                return $element->periodicity=='MONTHLY';
            })->first();
            if(!is_null($monthlyLimit)){
                $limit=Gold::select("exchange_rate->amount->limit->amount as limits")
                ->where(function($query) use($gold){
                    return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->orWhere('id',$gold->id);
                })
                ->whereIn('limit_type',$limitTypes)
                ->whereRaw($whareDate,[Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()])
                ->whereNull('manual_type')
                //->whereBetween('updated_at',[\Carbon\Carbon::now()->startOfMonth()->toDateTimeString(),\Carbon\Carbon::now()->endOfMonth()->toDateTimeString()])
                ->get();

                if(!is_null($limit) && ($limit->count()>$monthlyLimit->maxCount || $limit->sum("limits")>$monthlyLimit->maxAmount->amount)){
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "CONFIRM-GOLD",
                            "message"   => [
                                "title"   => __("Your monthly account limit exceed the allowed limit!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_GOLD_101",
                                "type"    => "ERROR"
                            ]
                         ]
                    ]));
                }
            }


      //  $goldConfig=app(\App\Settings\ConfigSettings::class)->goldConfig;

        $object=new \stdClass();
        // if(env('APP_HOSTING', 'remote')=='local' && env('APP_ENV', 'production')!='production' && auth()->user()->id=="0183415"/*$debitAccountId->currencyId()==$creditAccountId->currencyId()*/){
        //     $object->journals= [
        //         "genericPayee" =>[
        //             "nickName" =>'',
        //             "accountName" =>'temp',
        //             "accountNumber" => $creditAccountId->value,
        //             "transferMode"=> 'ACC'
        //         ],
        //         "genericPayout" =>[
        //             "amount" =>[
        //                 "amount" =>$gold->amount->amount*$equlivent,
        //                 "currency" =>$gold->amount->currency
        //             ],
        //             "purpose" =>'FAML',
        //             "purposeText" => null,
        //             "debitAccountId" => $debitAccountId,
        //             "remarks"=>$goldTransaction->remarks
        //         ],
        //         "paymentType" => 'INTERNALFT'
        //     ];
        //     $result=CustomerService::internalTransefer($object);
        // }else{
            $object->reference_id= $goldTransaction->reference_id;
            $object->journals= [
                "fr_cust_accountid" => $debitAccountId->value,
                "to_cust_accountid" => $creditAccountId->value,
                "amount"            => round($gold->amount->amount*$equlivent,3),
                "remarks"           => $goldTransaction->remarks,
                "cust_accountid_fee"=> $feeAccountId->value,
                "amount_fee"        => $goldTransaction->debit_fee->amount,
                "remarks_fee"       => sprintf(trans("gold_fee_remark"),
                    substr($goldTransaction->remarks,0, strpos($goldTransaction->remarks,"\n"))
                ),
                "serv_name"         => "GOLD"
            ];
            $result=FlexService::recordEntries($object);
        //}
        // $object->journals= [
        //     [
        //         "id"=> 1,
        //         "voucherno"=> "1",
        //         "location"=> $debitAccountId->branchId(),
        //         "accountcode"=> $debitAccountId->value,
        //         "amount"=> $goldTransaction->debit_amount->net->amount,
        //         //"lcy_amount"=> round($goldTransaction->debit_amount->local->amount,2),
        //         "type"=> "D",
        //         "currency"=>$goldTransaction->debit_amount->net->currency,
        //         "remarks"=> $goldTransaction->remarks,
        //         "date"=> $dateTime->format("Y-m-d"),
        //         "createdtime"=> $dateTime->format("g:i:s")
        //     ],
        //     [
        //         "id"=> 2,
        //         "voucherno"=> "2",
        //         "location"=> $feeAccountId->branchId(),
        //         "accountcode"=> $feeAccountId->value,
        //         "amount"=> $goldTransaction->debit_fee->amount,
        //         //"lcy_amount"=> round($goldTransaction->fee->amount,2),
        //         "type"=> "D",
        //         "currency"=> $goldTransaction->debit_fee->currency,
        //         "remarks"=>sprintf(trans("gold_fee_remark"),
        //             $goldTransaction->remarks
        //         ),
        //         "date"=> $dateTime->format("Y-m-d"),
        //         "createdtime"=> $dateTime->format("g:i:s")
        //     ],
        //     [
        //         "id"=> 3,
        //         "voucherno"=> "3",
        //         "location"=> $creditAccountId->branchId(),
        //         "accountcode"=> $creditAccountId->value,
        //         "amount"=> $goldTransaction->credit_amount->net->amount,
        //         //"lcy_amount"=> round($goldTransaction->credit_amount->local->amount,2),
        //         "type"=> "C",
        //         "currency"=> $goldTransaction->credit_amount->net->currency,
        //         "remarks"=>  $goldTransaction->remarks,
        //         "date"=> $dateTime->format("Y-m-d"),
        //         "createdtime"=> $dateTime->format("g:i:s")
        //     ],
        //     [
        //         "id"=> 4,
        //         "voucherno"=> "4",
        //         "location"=> $debitAccountId->branchId(),
        //         "accountcode"=> $debitAccountId->currencyId()==$creditAccountId->currencyId()?$goldConfig->chargeTransferAccountId:$goldConfig->chargeAccountId,
        //         "amount"=> $goldTransaction->credit_fee->amount,
        //         //"lcy_amount"=> round($goldTransaction->fee->amount,2),
        //         "type"=> "C",
        //         "currency"=> $goldTransaction->credit_fee->currency,
        //         "remarks"=>sprintf(trans("gold_fee_remark"),
        //             $goldTransaction->remarks
        //         ),
        //         "date"=> $dateTime->format("Y-m-d"),
        //         "createdtime"=> $dateTime->format("g:i:s")
        //     ]
        // ];


        if($result->status->message->code=="0"){
            //$gold->external_reference_id= $result->getAdditionalData()["externalReferenceId"];
            $gold->status=TransactionStatusEnum::COMPLETED->value;
            $gold->save();

            $goldTransaction->external_reference_id= $result->getAdditionalData()["externalReferenceId"];
            $goldTransaction->status=TransactionStatusEnum::COMPLETED->value;
            $goldTransaction->save();

            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Gold"),
                    'body'=>$goldTransaction->remarks,
                    'type'=>'operation',
                ]
            ]);
            $result->additional([
                "externalReferenceId"=>$result->getAdditionalData()["externalReferenceId"],
                "receipt"=>$this->getReceiptData($gold,$goldTransaction)->toArray()
            ]);
            return response()->json($result);
        }else{
            $gold->status=TransactionStatusEnum::ERROR->value;
            $gold->save();
            $goldTransaction->status=TransactionStatusEnum::ERROR->value;
            $goldTransaction->save();
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);

    }
}
