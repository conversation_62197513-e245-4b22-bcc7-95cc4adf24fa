<?php

namespace App\Telescope\Watchers;

use Illuminate\Http\Client\Events\ConnectionFailed;
use Illuminate\Http\Client\Events\ResponseReceived;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Lara<PERSON>\Telescope\IncomingEntry;
use Lara<PERSON>\Telescope\Telescope;
use Symfony\Component\HttpFoundation\File\File;
use <PERSON><PERSON>\Telescope\Watchers\Watcher;

class ClientRequestWatcher extends Watcher
{
    /**
     * Register the watcher.
     *
     * @param  \Illuminate\Contracts\Foundation\Application  $app
     * @return void
     */
    public function register($app)
    {
        $app['events']->listen(ConnectionFailed::class, [$this, 'recordFailedRequest']);
        $app['events']->listen(ResponseReceived::class, [$this, 'recordResponse']);
    }

    /**
     * Record a HTTP Client connection failed request event.
     *
     * @param  \Illuminate\Http\Client\Events\ConnectionFailed  $event
     * @return void
     */
    public function recordFailedRequest(ConnectionFailed $event)
    {
        if (! Telescope::isRecording()) {
            return;
        }

        Telescope::recordClientRequest(IncomingEntry::make([
            'method' => $event->request->method(),
            'uri' => $event->request->url(),
            'headers' => $this->headers($event->request->headers()),
            'payload' => $this->payload($this->input($event->request)),
        ]));
    }

    /**
     * Record a HTTP Client response.
     *
     * @param  \Illuminate\Http\Client\Events\ResponseReceived  $event
     * @return void
     */
    public function recordResponse(ResponseReceived $event)
    {
        if (! Telescope::isRecording()) {
            return;
        }
        $startTime =isset($event->response->transferStats)?optional($event->response->transferStats)->getTransferTime():null;

        Telescope::recordClientRequest(IncomingEntry::make([
            'method' => $event->request->method()." - [".($startTime ? floor($startTime * 1000) : "")."]",
            'uri' => $event->request->url(),
            'headers' => $this->headers($event->request->headers()),
            'payload' => $this->payload($this->input($event->request)),
            'response_status' => $event->response->status(),
            'response_headers' => $this->headers($event->response->headers()),
            'duration' => $startTime ? floor($startTime * 1000) : null,
            'response' => $this->response($event->response),
        ]));
    }

    /**
     * Determine if the content is within the set limits.
     *
     * @param  string  $content
     * @return bool
     */
    public function contentWithinLimits($content)
    {
        $limit = $this->options['size_limit'] ?? 64;

        return mb_strlen($content) / 1000 <= $limit;
    }

    /**
     * Format the given response object.
     *
     * @param  \Illuminate\Http\Client\Response  $response
     * @return array|string
     */
    protected function response(Response $response)
    {
        $content = $response->body();

        $stream = $response->toPsrResponse()->getBody();

        if ($stream->isSeekable()) {
            $stream->rewind();
        }

        if (is_string($content)) {
            if (is_array(json_decode($content, true)) &&
                json_last_error() === JSON_ERROR_NONE) {
                return $this->contentWithinLimits($content)
                        ? $this->hideParameters(json_decode($content, true), Telescope::$hiddenResponseParameters)
                        : 'Purged By Telescope';
            }

            if (Str::startsWith(strtolower($response->header('Content-Type') ?? ''), 'text/plain')) {
                return $this->contentWithinLimits($content) ? $content : 'Purged By Telescope';
            }
        }

        if ($response->redirect()) {
            return 'Redirected to '.$response->header('Location');
        }

        if (empty($content)) {
            return 'Empty Response';
        }

        return 'HTML Response';
    }

    /**
     * Format the given headers.
     *
     * @param  array  $headers
     * @return array
     */
    protected function headers($headers)
    {
        $headerNames = collect($headers)->keys()->map(function ($headerName) {
            return strtolower($headerName);
        })->toArray();

        $headerValues = collect($headers)->map(function ($value) {
            return $value[0];
        })->toArray();

        $headers = array_combine($headerNames, $headerValues);

        return $this->hideParameters($headers,
            Telescope::$hiddenRequestHeaders
        );
    }

    /**
     * Format the given payload.
     *
     * @param  array  $payload
     * @return array
     */
    protected function payload($payload)
    {
        return $this->hideParameters($payload,
            Telescope::$hiddenRequestParameters
        );
    }

    /**
     * Hide the given parameters.
     *
     * @param  array  $data
     * @param  array  $hidden
     * @return mixed
     */
    protected function hideParameters($data, $hidden)
    {
        foreach ($hidden as $parameter) {
            if (data_get($data, $parameter)) {
                $this->dataSet($data, $parameter, '********');
            }
        }

        return $data;
    }

    /**
     * Set an array or object value using "dot" notation.
     *
     * @param  mixed  $target
     * @param  string|array  $key
     * @param  mixed  $value
     * @param  bool  $overwrite
     * @return mixed
     */
    function dataSet(&$target, $key, $value, $overwrite = true) {
        $segments = is_array($key) ? $key : explode('.', $key);

        if (($segment = array_shift($segments)) === '*') {
            if (Arr::accessible($target)) {
                if ($segments) {
                    foreach ($target as &$inner) {
                        $this->dataSet($inner, $segments, $value, $overwrite);
                    }
                } else {
                    foreach ($target as &$inner) {
                        // Only overwrite if the key exists
                        if ($overwrite &&  $inner==$segment) {
                            $inner = $value;
                        }
                        // if (is_array($inner) && Arr::exists($inner, $segments[0])) {
                        //     $inner[$segments[0]] = $value;
                        // }
                    }
                }
            }


        } elseif (Arr::accessible($target)) {
            if ($segments) {
                if (Arr::exists($target, $segment)) {
                  //  $target[$segment] = [];
                    $this->dataSet($target[$segment], $segments, $value, $overwrite);
                }

            } elseif ($overwrite && Arr::exists($target, $segment)) {
                $target[$segment] = $value;
            }
        } elseif (is_object($target)) {
            if ($segments) {
                if (isset($target->{$segment})) {
                    //$target->{$segment} = [];
                    $this->dataSet($target->{$segment}, $segments, $value, $overwrite);
                }
            } elseif ($overwrite && isset($target->{$segment})) {
                $target->{$segment} = $value;
            }
        }

        return $target;
    }
    /**
     * Extract the input from the given request.
     *
     * @param  \Illuminate\Http\Client\Request  $request
     * @return array
     */
    protected function input(Request $request)
    {
        if (! $request->isMultipart()) {
            return $request->data();
        }

        return collect($request->data())->mapWithKeys(function ($data) {
            if ($data['contents'] instanceof File) {
                $value = [
                    'name' => $data['filename'] ?? $data['contents']->getClientOriginalName(),
                    'size' => ($data['contents']->getSize() / 1000).'KB',
                    'headers' => $data['headers'] ?? [],
                ];
            } elseif (is_resource($data['contents'])) {
                $filesize = @filesize(stream_get_meta_data($data['contents'])['uri']);

                $value = [
                    'name' => $data['filename'] ?? null,
                    'size' => $filesize ? ($filesize / 1000).'KB' : null,
                    'headers' => $data['headers'] ?? [],
                ];
            } elseif (json_encode($data['contents']) === false) {
                $value = [
                    'name' => $data['filename'] ?? null,
                    'size' => (strlen($data['contents']) / 1000).'KB',
                    'headers' => $data['headers'] ?? [],
                ];
            } else {
                $value = $data['contents'];
            }

            return [$data['name'] => $value];
        })->toArray();
    }
}
