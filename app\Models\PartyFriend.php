<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartyFriend extends Model
{
    use HasFactory;

    protected $primaryKey = null;      // no primary key
    public $incrementing = false;      // disable auto-increment
    public $timestamps = false;        // optional, if no created_at/updated_at

    protected $fillable = [
        "friend_id",
        "party_id",
        "username"
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
        static::addGlobalScope(new UsernameScope);

         // auto-sets values on creation
         static::creating(function ($query) {
            if(is_null($query->party_id))
                $query->party_id = auth()->user()->id;
            if(is_null($query->username) && isset(auth()->user()?->username))
                $query->username = auth()->user()->username;
        });
    }

    public function friends()
    {
        return $this->hasMany(Party::class,'friend_id')
        ->select('id','name','image');
    }

    public function party()
    {
        return $this->belongsTo(Party::class,'friend_id')
        ->select('id','name','image','party_id');
    }
}
