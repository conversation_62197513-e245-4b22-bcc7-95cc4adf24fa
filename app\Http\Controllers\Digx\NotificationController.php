<?php

namespace App\Http\Controllers\Digx;
use App\Data\GeneralResponseData;
use App\Enums\TransactionStatusEnum;
use App\Models\CustomerNotification;
use App\Models\Notification;

use App\Http\Controllers\Controller;
use App\Traits\NotificationCount;
use Illuminate\Http\Request;


class NotificationController extends Controller
{

    use NotificationCount;
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user=auth()->user();

        $notifications=Notification:: with(["messageUserMappings"=>function($query) use($user){
            $query->where("customer_id",$user->id);
        }])
        ->select('id','title','body','image','action','url','service_id','created_at')
        ->where(function ($query) use($user){
           // $query->where('party_id',$user->id)
            $query->whereHas('messageUserMappings', function ($query) use($user) {
                return $query->where('customer_id',$user->id);
            })
            ->orWhere(function ($query) use($user){
                $query->where('type','brodcast')
                ->where(function ($query) use($user){
                    $query->whereHas('customerType', function ($query) use($user) {
                        return $query->where('id',$user->customerType);
                    })
                    ->orWhere('topic','main');
                });
            });
        })
        ->where(function ($query) use($user,$request){
            return $query->whereHas('service', function ($query) use($user,$request) {
                return $query->whereNotNull('service_id')
                ->where(function ($query)  use($request){
                    return $query->where('status',1)
                    ->orWhere(function ($query) use($request){
                        return $query->where('status',3)
                        ->where('app_version','<=',($request->header('appVersion')??0));
                    });
                })
                ->whereHas('serviceCustomerType', function ($query) use($user) {
                     $query->where('customer_type_id',$user->customerType)
                    ->whereNull('page_service_id');

                    if(isset($user->customerRole)){
                        $query->whereHas('customerRole', function ($query) use($user) {
                            return $query->where('role_identifer',$user->customerRole);
                        });
                    }
                    return $query;
                });
            })
            ->orWhereNull('service_id');
        })
        ->where('status',1)
        ->skip( $request->from??0)
        ->take($request->limit??20)
        ->orderBy("created_at","DESC")
        ->get();

        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'notifications'=>$user->id=="0231291"?[]:$notifications
        ]));

    }
    /**
     * Store new resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $user=auth()->user();

        $customerNotifications=CustomerNotification::
        where('customer_id',$user->id)
        ->whereIn('notification_id',$request->ids)
        ->where(function ($query) use($user){
            $query->where(function ($query) use($user){
                $query->where('status','<>','R')
                ->where('customer_type_id',$user->customerType);
            });
        })
        ->get();

        if(!empty($customerNotifications)){
            CustomerNotification::
                where('customer_id',$user->id)
                ->where(function ($query) use($user){
                    $query->where(function ($query) use($user){
                        $query->where('status','<>','R')
                        ->where('customer_type_id',$user->customerType);
                    });
                })
                ->whereIn('notification_id',$request->ids)
                ->update([
                    'customer_type_id'=>$user->customerType,
                    'status'=>'R',
                ]);
        }


        $customerNotifications=CustomerNotification::where('customer_id',$user->id)
        ->where('customer_type_id',$user->customerType)
        ->whereIn('notification_id',$request->ids)
        ->get();

        $unReadMessages=collect($request->ids)->diff($customerNotifications->pluck("notification_id"));

        if(!empty($unReadMessages)){
            $items=collect($unReadMessages)->map(function($id) use($user){
                return [
                    'customer_id'       =>$user->id,
                    'customer_type_id'  =>$user->customerType,
                    'notification_id'   =>$id,
                    'status'   =>'R'
                ];
            })->toArray();

            CustomerNotification::insert($items);
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully delete device from list"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Notification  $notification
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Notification $notification)
    {
        $user=auth()->user();
        $customerNotification=CustomerNotification::
        where('customer_id',$user->id)
        ->where('customer_type_id',$user->customerType)
        ->where('notification_id',$notification->id)
        ->first();

        if(is_null($customerNotification)){
            CustomerNotification::insert([[
                'customer_id'       =>$user->id,
                'customer_type_id'  =>$user->customerType,
                'notification_id'   =>$notification->id,
                'status'   =>'R'
            ]]);
        }else if($customerNotification->status!='R'){
            // $customerNotification->status="R";
            CustomerNotification::
                where('customer_id',$user->id)
                ->where('customer_type_id',$user->customerType)
                ->where('notification_id',$notification->id)
                ->update([
                   'status'=>'R'
                ]);
            //$customerNotification->save();
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully delete device from list"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }

    /**
     * Count of notification available.
     *
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function count(Request $request)
    {
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully delete device from list"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
           ]
        ])->additional([
            'notification'=>$this->getNotificationCount()
        ]));
    }

}
