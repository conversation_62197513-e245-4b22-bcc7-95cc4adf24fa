<?php

namespace App\Models;

use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
class SplitPaymentTransaction extends Model
{
    use HasFactory;
    protected $fillable = ['party_id','split_payment_id','debit_account_id','amount','fee','exchange_rate','percent','remarks','status','type','payment_result','created_at'];
    protected $casts = [
        'debit_account_id'   => 'object',
        'amount'                => 'object',
        'fee'                   => 'object',
        'exchange_rate'         => 'object',
        'payment_result'        => 'object',
        'created_at'            => 'datetime:Y-m-d H:i:s',
        'updated_at'            => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            if(is_null($query->party_id))
                $query->party_id = auth()->user()->id;
        });
    }

    protected function serializeDate(DateTimeInterface $date){
        return $date->timezone('Asia/Aden')->format('Y-m-d H:i:s');
    }
    public function splitPayment()
    {
        return $this->belongsTo(SplitPayment::class, 'split_payment_id');
    }
    public function parties()
    {
        return $this->hasManyThrough(Party::class, PartyFriend::class,'party_id','id','party_id','friend_id')
        ->select('id','name','image');
    }
    public function party()
    {
        // return $this->hasOneThrough(Party::class, PartyFriend::class,'party_id','id','party_id','friend_id')
        // ->select('id','name','image');
        return $this->hasOne(Party::class, 'party_id','party_id')
        ->select('id','name','image','party_id');
    }

    public static function list(int $from=0,int $limit=20,string $filter="",?int $status=null)
    {
        $items= static::with(['splitPayment'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class)
            ->select('id','credit_account_id','bank_code','remarks','status','type','party_id')
            ->with(['party'=>function($query){
                return $query->withoutGlobalScope(UsernameScope::class);
            }]);
        }])
        ->select('id','split_payment_id','amount','fee','status','type','created_at as date')
        ->whereNotNull("type")
        ->skip($from)
        ->take($limit)
        ->orderBy("created_at","DESC");

        if(!empty($filter)){
            $items=$items->whereHas('splitPayment', function ($query) use($filter) {
                return $query->withoutGlobalScope(CustomerScope::class)
                ->where('remarks','like',"%$filter%")
                ->orWhere('credit_account_id->value','like',"%$filter%")
                ->orWhere('credit_account_id->displayValue','like',"%$filter%");
            });
        }
        if(!is_null($status)){
            $items=$items->where('status', $status);
        }
        return $items->get();
    }
    public static function show(?int $id=null)
    {
        return static::with(['splitPayment'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class)
            ->select('id','credit_account_id','bank_code','remarks','status','type','party_id')
            ->with(['party'=>function($query){
                return $query->withoutGlobalScope(UsernameScope::class);
            }]);
        }])
        ->select('id','split_payment_id','debit_account_id','amount','fee','status','type','created_at as date')
        ->where("status",TransactionStatusEnum::INIT->value)
        ->where("type",InvoiceTransactionsTypeEnum::Payment->value)
        ->where('id',$id)
        ->first();
    }
}
