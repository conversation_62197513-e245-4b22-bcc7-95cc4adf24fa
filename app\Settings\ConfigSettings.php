<?php

namespace App\Settings;

use App\Data\AccountConfigData;
use App\Data\AppConfigData;
use App\Data\BranchConfigData;
use App\Data\ExchangeAreaConfigData;
use App\Data\GeneralConfigData;
use App\Data\GoldConfigData;

use App\Data\Limit\LimitPackageConfigData;
use App\Data\PNPLConfigData;
use App\Data\ProductConfigData;
use App\Data\ThirdPartyData;
use Spatie\LaravelSettings\Settings;

class ConfigSettings extends Settings
{
    public AppConfigData $appConfig;
    public GoldConfigData $goldConfig;

    public GeneralConfigData $generalConfig;

    public ProductConfigData $productConfig;
    public BranchConfigData $branchConfig;

    public AccountConfigData $accountConfig;

    public ExchangeAreaConfigData $exchangeAreaConfig;

    public LimitPackageConfigData $limitPackageConfig;

    public PNPLConfigData $pnplConfig;

    public static function group(): string
    {
        return 'config';
    }
}
