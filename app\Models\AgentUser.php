<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Str;

class AgentUser extends Model
{
    use HasFactory;
    //protected $connection= env('DB_AGENT_CONNECTION','agent');

    protected $table = 'users';

    public function __construct(array $attributes = [])
    {
        $this->connection = env('DB_AGENT_CONNECTION','agent');

        parent::__construct($attributes);
    }

    public function transactions()
    {
        return $this->hasMany('App\Models\AgentTransaction', "party_id", "party_id");
    }
}

