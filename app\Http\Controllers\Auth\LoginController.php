<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Session;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function showLoginForm()
    {
        if(!session()->has('url.intended'))
        {
            session(['url.intended' => url()->previous()]);
        }
        Session::put('locale',config('general.language.default')??config('app.locale'));
        \App::setLocale(session('locale'));

        return view(config('general.theme.selected').'.auth.login');
    }

    protected function credentials(Request $request){
        $user = \App\Models\User::where('email', $request->email)->first();

        // Check if user was successfully loaded, that the password matches
        // and dosn't have any roles. If so, override the default error message.

        if ($user && (count($user->roles->pluck('id')->toArray())==0)) {
            return [
                'email' => $request->email,
                'password' => $request->password,
                'is_admin' => '-1'
            ];
        }
        return ['email' => $request->email, 'password' => $request->password];
    }
    protected function authenticated(Request $request, $user){

        if(session()->has('url.intended')){
            $redirectTo=session('url.intended');
            return redirect($redirectTo);
        }
        return redirect("$this->redirectTo");
    }

}
