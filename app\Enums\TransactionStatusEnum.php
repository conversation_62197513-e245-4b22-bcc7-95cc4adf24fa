<?php

namespace App\Enums;

enum TransactionStatusEnum: int
{
    // ----------------------------------------
    // 🚫 Failure and Error States (Negative)
    // ----------------------------------------
    case FAILED    = -3; // Transaction failed due to system or external reason (e.g., payment declined)
    case REJECTED  = -4; // Transaction was rejected due to invalid data, lack of authorization, or mismatch
    case CANCELED  = -2; // Transaction was canceled by the user or system
    case ERROR     = -1; // Technical/system error occurred

    // ----------------------------------------
    // ✅ Standard Automated Processing Flow
    // ----------------------------------------
    case INIT        = 1; // Transaction created but not yet processed
    case PENDING     = 2; // Awaiting automatic processing or confirmation
    case AUTHORIZED  = 3; // Authorized but not yet captured or executed
    case COMPLETED   = 4; // Successfully completed
    case REFUNDED    = 5; // Refunded after completion
    case EXPIRED     = 6; // Expired due to timeout or inactivity
    case REVERSED    = 7; // Reversed after authorization
    case ON_HOLD     = 8; // Temporarily paused (e.g., for verification or compliance)

    // ----------------------------------------
    // 🛠️ Manual Processing Flow
    // ----------------------------------------
    case MANUAL_PENDING      = 10; // Transaction submitted for manual processing
    case MANUAL_VERIFICATION = 11; // Customer verification and data validation
    case MANUAL_DATA_INPUT   = 12; // Manual entry of remittance or additional data
    case MANUAL_ON_HOLD      = 13; // Temporarily on hold during manual processing
    case MANUAL_REVIEW       = 14; // Under final review and compliance checks
    case MANUAL_ESCALATED    = 15; // Escalated to higher-level approval or compliance team
    case MANUAL_APPROVED     = 16; // Approved and ready for execution
    case MANUAL_COMPLETED    = 17; // Manual processing completed successfully
    case MANUAL_REJECTED     = 18; // Manual processing failed or was rejected

    public function label(): string
    {
        return match($this) {
            self::FAILED => 'Failed',
            self::REJECTED => 'Rejected',
            self::CANCELED => 'Canceled',
            self::ERROR => 'Error',
            self::INIT => 'Initialized',
            self::PENDING => 'Pending',
            self::AUTHORIZED => 'Authorized',
            self::COMPLETED => 'Completed',
            self::REFUNDED => 'Refunded',
            self::EXPIRED => 'Expired',
            self::REVERSED => 'Reversed',
            self::ON_HOLD => 'On Hold',
            self::MANUAL_PENDING => 'Manual Pending',
            self::MANUAL_VERIFICATION => 'Manual Verification',
            self::MANUAL_DATA_INPUT => 'Manual Data Input',
            self::MANUAL_ON_HOLD => 'Manual On Hold',
            self::MANUAL_REVIEW => 'Manual Review',
            self::MANUAL_ESCALATED => 'Manual Escalated',
            self::MANUAL_APPROVED => 'Manual Approved',
            self::MANUAL_COMPLETED => 'Manual Completed',
            self::MANUAL_REJECTED => 'Manual Rejected',
        };
    }

    public function description(): string
    {
        return match($this) {
            self::FAILED => 'Transaction failed due to system or external reason (e.g., payment declined)',
            self::REJECTED => 'Transaction was rejected due to invalid data, lack of authorization, or mismatch',
            self::CANCELED => 'Transaction was canceled by the user or system',
            self::ERROR => 'Technical/system error occurred',
            self::INIT => 'Transaction created but not yet processed',
            self::PENDING => 'Awaiting automatic processing or confirmation',
            self::AUTHORIZED => 'Authorized but not yet captured or executed',
            self::COMPLETED => 'Successfully completed',
            self::REFUNDED => 'Refunded after completion',
            self::EXPIRED => 'Expired due to timeout or inactivity',
            self::REVERSED => 'Reversed after authorization',
            self::ON_HOLD => 'Temporarily paused (e.g., for verification or compliance)',
            self::MANUAL_PENDING => 'Transaction submitted for manual processing',
            self::MANUAL_VERIFICATION => 'Customer verification and data validation',
            self::MANUAL_DATA_INPUT => 'Manual entry of remittance or additional data',
            self::MANUAL_ON_HOLD => 'Temporarily on hold during manual processing',
            self::MANUAL_REVIEW => 'Under final review and compliance checks',
            self::MANUAL_ESCALATED => 'Escalated to higher-level approval or compliance team',
            self::MANUAL_APPROVED => 'Approved and ready for execution',
            self::MANUAL_COMPLETED => 'Manual processing completed successfully',
            self::MANUAL_REJECTED => 'Manual processing failed or was rejected',
        };
    }

    public function isManual(): bool
    {
        return $this->value >= 10 && $this->value <= 18;
    }

    public function isAutomated(): bool
    {
        return $this->value >= 1 && $this->value <= 8;
    }

    public function isFailure(): bool
    {
        return in_array($this, [
            self::FAILED,
            self::REJECTED,
            self::CANCELED,
            self::ERROR,
            self::MANUAL_REJECTED,
        ]);
    }

    public function isCompleted(): bool
    {
        return in_array($this, [
            self::COMPLETED,
            self::REFUNDED,
            self::REVERSED,
            self::MANUAL_COMPLETED,
        ]);
    }

    public function isPending(): bool
    {
        return in_array($this, [
            self::INIT,
            self::PENDING,
            self::AUTHORIZED,
            self::ON_HOLD,
            self::MANUAL_PENDING,
            self::MANUAL_VERIFICATION,
            self::MANUAL_DATA_INPUT,
            self::MANUAL_ON_HOLD,
            self::MANUAL_REVIEW,
            self::MANUAL_ESCALATED,
            self::MANUAL_APPROVED,
        ]);
    }

    public static function findByValue(int $value): ?self
    {
        return collect(self::cases())->first(fn($case) => $case->value === $value);
    }

    public static function values(): array
    {
        return collect(self::cases())->pluck('value')->toArray();
    }
}
