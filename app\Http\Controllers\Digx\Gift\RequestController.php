<?php

namespace App\Http\Controllers\Digx\Gift;

use App\Data\GeneralResponseData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Services\NotificationService;
use App\Enums\TransactionStatusEnum;
use App\Helpers\JsonCamel\JsonCamelHelperFacade;
use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\GiftRequest;
use App\Scopes\CustomerScope;

use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RequestController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::GIFT
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $transactions=GiftRequest::with(['gift'=>function($query){
            return $query->select('id','status','amount','created_at as date')
            ->with(['transactions'=>function($query){
                return $query->select('id','status','gift_id','amount','payee')
                ->with(['sharing'=>function($query){
                    return $query->select('data_id','txn_token','data','type');
                }])
                ->where('status','<>', 0)
                ->where('payee->transferValue', auth()->user()->phone);
            }])
            ->where('status','<>', 0);
        }])
        ->with(['sharing'=>function($query){
            return $query->select('data_id','txn_token','data','type');
        }])
        ->select('id','status','amount',"type",'sender','remarks','created_at as date','gift_id');

        if(in_array($request->currencyId??'',[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $transactions=$transactions->whereIn('amount->currency',[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value]);
        }else{
            $transactions=$transactions->whereNotIn('amount->currency',[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value]);
        }
        $transactions=$transactions->skip($request->from)
        ->take($request->limit)
        ->orderBy("id","DESC")
        ->get();

        return response()->json($transactions);

    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $user=$request->user();
        $validator=validator()->make($request->all(),[
            'amount.amount'=>"nullable|numeric",
            'amount.currency'=>"nullable",
            'type'=>"required|in:friday,eid,general,eidAlfitr,eidAladha,engagement,success,other",
            'sender.displayValue'=>"required",
            'sender.value'=>"required|numeric",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $params=[
            "status"        =>TransactionStatusEnum::INIT->value,
            "party_id"      =>$user->id,
            "party_name"    =>html_entity_decode("{$user->userProfile->firstName} {$user->userProfile->lastName}", ENT_QUOTES, "UTF-8"),
            "party_phone"   =>$user->phone,
            "type"      =>$request->type,
            "sender"=>[
                "displayValue"  => html_entity_decode($request->input("sender.displayValue"), ENT_QUOTES, "UTF-8"),
                "value"         => $request->input("sender.value"),
            ],
            "remarks"   =>$request->remarks??""
        ];
        if($request->filled("amount.amount") && $request->filled("amount.currency")){
            $params["amount"]=[
                "amount"        => $request->input("amount.amount"),
                "currency"      =>  $request->input("amount.currency"),
            ];
        }
        $giftRequest=GiftRequest::create($params);

        LogItem::store($giftRequest);

        $sharing = \App\Models\Sharing::create([
            "txn_token" => (string)Str::orderedUuid(),
            "type" => "giftRequest",
            "party_id" => $user->id,
            "data" => [
                "id" => $giftRequest->id
            ]
        ]);

        // $is_eid=Carbon::parse($giftRequest->created_at)->betweenIncluded('2024-04-10', '2024-04-17');
        // if($is_eid){
        //     $smsMessage = sprintf(trans("gift_request_sms_message_new_eid"),
        //         html_entity_decode($giftRequest->sender->displayValue, ENT_QUOTES, "UTF-8"),
        //         html_entity_decode($userShortName, ENT_QUOTES, "UTF-8"),
        //         $giftRequest->type=='other'?$giftRequest->remarks:trans("$giftRequest->type"),
        //         env('APP_URL').route('sharing',[$sharing->txn_token],false)
        //     );
        // }else{
            $smsMessage =is_null($giftRequest->amount)? sprintf(trans("gift_request_sms_message_new"),
                $giftRequest->sender->displayValue,
                $giftRequest->party_name,
                $giftRequest->type=='other'?$giftRequest->remarks:trans("$giftRequest->type"),
                env('APP_URL').route('sharing',[$sharing->txn_token],false)
            ):sprintf(trans("gift_request_sms_message_new_with_amount"),
                $giftRequest->sender->displayValue,
                $giftRequest->party_name,
                $giftRequest->type=='other'?$giftRequest->remarks:trans("$giftRequest->type"),
                $giftRequest->amount->amount . " " . $giftRequest->amount->currency,
                env('APP_URL').route('sharing',[$sharing->txn_token],false)
            );
       // }


        // $smsMessage =is_null($giftRequest->amount)? sprintf(trans("gift_request_sms_message"),
        //     html_entity_decode($giftRequest->party_name, ENT_QUOTES, "UTF-8"),
        //     $giftRequest->type=='other'?$giftRequest->remarks:trans("$giftRequest->type"),
        //     env('APP_URL').route('sharing',[$sharing->txn_token],false)
        // ):sprintf(trans("gift_request_sms_message_with_amount"),
        //     html_entity_decode($giftRequest->party_name, ENT_QUOTES, "UTF-8"),
        //     $giftRequest->type=='other'?$giftRequest->remarks:trans("$giftRequest->type"),
        //     $giftRequest->amount->amount . " " . $giftRequest->amount->currency,
        //     env('APP_URL').route('sharing',[$sharing->txn_token],false)
        // );
        if(env('APP_ENV', 'production')!='local'|| in_array($giftRequest->sender->value,['771555999','779495900'])){
            NotificationService::sendSMS([
                "mobile" => $giftRequest->sender->value,
                "message" => $smsMessage
            ]);
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-GIFT-REQUEST",
                "message"   => [
                    "title"   => "Successfully send gift request!",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        // ])->additional([
        //     "externalReferenceId"=>$giftRequest->id,
        ]));

    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $giftRequest=GiftRequest::withoutGlobalScope(CustomerScope::class)
        ->select('id','amount',"type",'party_name','party_phone','remarks','created_at as date')
        ->where("id",$id)
        ->where("status",TransactionStatusEnum::INIT->value)
        ->where("sender->value",auth()->user()->phone)
        ->first();

        if(is_null($giftRequest)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "GIFT-REQUEST-DETAILS",
                    "message"   => [
                        "title"   => __("Gift request not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GIFT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));

        }
        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INFO-GIFT-REQUEST-$giftRequest->id",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "giftRequest"=>$giftRequest
        ])->transform());
    }

    /**
     * Delete the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        $giftRequest=GiftRequest::withoutGlobalScope(CustomerScope::class)
        ->where("id",$id)
        ->where("status",TransactionStatusEnum::INIT->value)
        ->where("sender->value",auth()->user()->phone)
        ->first();

        if(is_null($giftRequest)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "GIFT-REQUEST-DETAILS",
                    "message"   => [
                        "title"   => __("Gift request not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GIFT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));

        }
        $giftRequest->status=TransactionStatusEnum::ERROR->value;
        $giftRequest->save();

        $user=auth()->user();
        NotificationService::sendMessagesToParty([
            [
                'title'=>__("Gift rejected"),
                'body'=>sprintf(__("Dear %s\nYour gift request that you sent to %s was rejected!"),
                    $giftRequest->party_name,
                    html_entity_decode("{$user->userProfile->firstName} {$user->userProfile->lastName}", ENT_QUOTES, "UTF-8"),
                ),
                'type'=>'operation',
            ]
        ],$giftRequest->party_id,false);
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INFO-GIFT-REQUEST-$giftRequest->id",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }
}
