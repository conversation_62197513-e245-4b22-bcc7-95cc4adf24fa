<?php

namespace App\Data;

use Illuminate\Support\Collection;
use App\Models\CustomerType;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;

class AppConfigData extends Data
{
    public const defaultCacheSignature=[
        CustomerType::RETAIL=>['0'=>[],'148'=>[],'172'=>[],'175'=>[],'182'=>[]],
        CustomerType::CORPORATE=>['0'=>[],'148'=>[],'172'=>[],'175'=>[],'182'=>[]],
        CustomerType::AGENT=>['0'=>[],'148'=>[],'172'=>[],'175'=>[],'182'=>[]],
        CustomerType::GUEST=>['0'=>[],'148'=>[],'172'=>[],'175'=>[],'182'=>[]],
        CustomerType::UNAUTHENTICATED=>['0'=>[]],
        CustomerType::BUSINESS=>['0'=>[],'148'=>[],'172'=>[],'175'=>[],'182'=>[]],

    ];

    public ?string $reportType;

    public ?array $cacheSignature;

    public ?int $tokenValidation;
    public ?App2FASettingData $app2FASetting;

    public function __construct(
        public ?int $notificationEnabled=0,
        public ?int $pushNotificationEnabled=0,
        public string $dormantActivateType="none",
        public ?int $allowBankyActivation=0,
        public ?int $allowAnaWeb=0,
        public ?int $allowGuest=0,
        public ?AppVersionSettingData $appVersionSetting=null,
    ) {
    }
    // public static function prepareForPipeline(array $properties) : array
    // {
    //     // $idRetail=CustomerType::RETAIL;
    //     // $idCorporate=CustomerType::CORPORATE;
    //     // $idAgent=CustomerType::AGENT;
    //     // data_fill($properties, "cacheSignature.$idRetail", static::defaultCacheSignature["$idRetail"]);
    //     // data_fill($properties, "cacheSignature.$idCorporate", static::defaultCacheSignature["$idCorporate"]);
    //     // data_fill($properties, "cacheSignature.$idAgent", static::defaultCacheSignature["$idAgent"]);
    //     data_fill($properties, "cacheSignature.*", static::defaultCacheSignature);
    //     return $properties;
    // }

    public function toApp($customerType,?int $appVersion,$customerRole):object {
        if(!isset($this->cacheSignature["$customerType"]) || !is_array($this->cacheSignature["$customerType"])){
            $versions=collect(static::defaultCacheSignature["$customerType"]);
        }else{
            $versions=collect($this->cacheSignature["$customerType"]);//??static::defaultCacheSignature["$customerType"]);
        }

        $filteredVersions=$versions
        ->map(function ($value,string $key){
            return ["k"=>$key,"v"=>$value];
        })
        ->filter(function ( $element) use($appVersion){
            //only for appVersion.
            return ((int)$element['k'])<=($appVersion??0);
        });
        $filteredVersion=$filteredVersions->last();
        //\Log::info("{filteredVersions}:",["filteredVersions"=>$filteredVersions]);

        return json_decode(json_encode([
            "reportType"=> $this->reportType,
            "dormantActivateType"=> $this->dormantActivateType,
            "notificationEnabled"=> $this->notificationEnabled,
            "allowAnaWeb"=> (request()->header('appVersion')??0)<177?0:($this->allowAnaWeb??0),
            "cacheSignature"=> isset($filteredVersion['v']["$customerRole"])?$filteredVersion['v']["$customerRole"]:'',
            "appVersion"=> $filteredVersion['k']??''
        ]));
    }

    public static function resetCacheSignature() {
        $settings=app(\App\Settings\ConfigSettings::class);
        //\Log::info($settings);
        $appConfig=$settings->appConfig;
        $appConfig->cacheSignature=static::defaultCacheSignature;
        $settings->appConfig=$appConfig;
        $settings->save();
    }
}
