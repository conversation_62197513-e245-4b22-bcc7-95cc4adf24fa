<?php

namespace App\Services;
use App\Data\AccountIdData;
use App\Data\GeneralResponseData;
use App\Data\TokenData;

use Illuminate\Support\Facades\Http;

use App;
use App\Models\User;

/**
 * Service to create and update orders
 */
class AnaService
{
    protected $settings;

    /**
     * Service to create and update orders
     */
    public function __construct()
    {
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->ana;
        if($this->settings->is_test){
            $this->settings=$this->settings->test_data;
        }
    }
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }
        if(is_null($token->access_token)||empty($token->access_token)){
            $response = Http::timeout(10)
            ->withBasicAuth($this->settings->client_id, $this->settings->client_secret)

            ->asForm()
            ->post("{$this->settings->url}/oauth/token",[
                "grant_type"=>"client_credentials"
            ]);

            if ($response->failed()) {
                return $response;
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->access_token)){
                $this->settings->token=TokenData::from($response);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $ana=$settings->ana;
                if($ana->is_test){
                    $test_data=$ana->test_data;
                    $test_data->token=$this->settings->token;
                    $ana->test_data=$test_data;
                }else{
                    $ana->token=$this->settings->token;
                }
                $settings->ana=$ana;
                $settings->save();
            }
           // dd($response);

        }
        return $status;
    }

    public static function registerAccount($request)
    {
        App::setLocale($request->header("lang")??"ar");
        $anaService=new AnaService();
        $status=$anaService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $response = Http::timeout(120)
        ->withToken($anaService->settings->token->access_token)
        ->post($anaService->settings->url."/banky/ana_registration",
            [
                "req_ref_id"                    => $request->reference_id??round(microtime(true)*1000),
                "instid"                        => $anaService->settings->instid,
                "brn_cd"                        => $request->brn_cd,
                "account_ccy"                   => $request->account_ccy,
                "customer_category"             => $request->customer_category,
                "incoming_fund"                 => $request->incoming_fund,
                "ovrsincsrc"                    => $request->ovrsincsrc??null,
                "account_prps"                  => $request->account_prps,
                "media_type"                    => $request->media_type,
                "lang"                          => $request->lang,
                "residnt"                       => $request->residnt,
                "us_res_status"                 => $request->us_res_status,
                "vst_us_prev"                   => $request->vst_us_prev??null,
                "maritalstat"                   => $request->maritalstat??null,
                "spouse_name"                   => $request->spouse_name??null,
                "spouse_emp_status"             => $request->spouse_emp_status??null,
                "dependents_children"           => $request->dependents_children??null,
                "emp_status"                    => $request->emp_status??null,
                "emp_country"                   => $request->emp_country??null,
                "emp_current_desig_job_nm"      => $request->emp_current_desig_job_nm??null,
                "emp_current_desig_company_nm"  => $request->emp_current_desig_company_nm??null,
                "emp_years_num"                 => $request->emp_years_num??null,
                "emp_telephone"                 => $request->emp_telephone??null,
                "emp_salary"                    => $request->emp_salary??null,
                "emp_currency_salary"           => $request->emp_currency_salary??null,
                "emp_salary_frequency"          => $request->emp_salary_frequency??null,
                "emp_previous_desig_job_nm"     => $request->emp_previous_desig_job_nm??null,
                "emp_previous_desig_company_nm" => $request->emp_previous_desig_company_nm??null,
                "expense_rent"                  => $request->expense_rent??null,
                "expense_insurance"             => $request->expense_insurance??null,
                "expense_loan_payments"         => $request->expense_loan_payments??null,
                "expense_othexp"                => $request->expense_othexp??null,
                "expense_house_value"           => $request->expense_house_value??null,
                "expense_no_of_credit_cards"    => $request->expense_no_of_credit_cards??null,
                "authId"                        => $request->authId
            ]
        );
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::registerAccount($request);
        }

        $result=$response->object();

        if(!$response->failed() && isset($result->result_code)){
            if($result->result_code=="0"){
                $accountId=AccountIdData::from([
                    "value"=>$result->accountid
                ]);
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("Successfully register new account your username:").$accountId->userName(),
                            "detail"  => $result->accountid??"",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    "response"=>$result
                ]);
            }

            if($result->result_code=="10" || $result->result_code=="20"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "[REGISTER_SERVER]",
                        "message"   => [
                            "title"   => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                            "detail"  => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                            "code"    => "DIGX_SWITCH_REGISTER_002",
                            "type"    => "ERROR"
                        ]
                     ]
                ))->additional([
                    "response"=>$result
                ]);
            }
        }


        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[REGISTER_SERVER]",
                "message"   => [
                    "title"   => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                    "detail"  => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                    "code"    => "DIGX_SWITCH_REGISTER_001",
                    "type"    => "ERROR"
                ]
             ]
        ))->additional([
            "response"=>$result
        ]);
    }

    public static function activateOBDXAccount($request)
    {
        App::setLocale($request->header("lang")??"ar");
        $anaService=new AnaService();
        $status=$anaService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $response = Http::withToken($anaService->settings->token->access_token)
        ->post(
            $anaService->settings->url."/banky/banky_activation",
            [
                "req_ref_id"    => $request->reference_id,
                "instid"        => $anaService->settings->instid,
                "brn_cd"        => $request->brn_cd,
                //"account_ccy"   => $request->account_ccy,
                "terminal_id"   => "********",
                "custno"        => $request->custno,
                "authid"        => $request->authId
            ]
        );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::activateOBDXAccount($request);
        }

        $result=$response->object();

        if(!$response->failed() && isset($result->result_code)){
            if($result->result_code=="0"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("Successfully register new account your username:").$result->custno,
                            "detail"  => $result->accountid??"",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                        ]
                ))->additional([
                    "response"=>$result
                ]);
            }

            if(isset($result->result_code) && ($result->result_code=="10" || $result->result_code=="20"|| $result->result_code=="11"|| $result->result_code=="40")){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "[REGISTER_SERVER]",
                        "message"   => [
                            "title"   => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                            "detail"  => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                            "code"    => "DIGX_SWITCH_REGISTER_002",
                            "type"    => "ERROR"
                        ]
                     ]
                ))->additional([
                    "response"=>$result
                ]);
            }
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[REGISTER_SERVE]",
                "message"   => [
                    "title"   => $result->result_desc??"Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->result_desc??"Can't process your order, please connect help desk to fix problem!",
                    "code"    => "DIGX_SWITCH_REGISTER_001",
                    "type"    => "ERROR"
                ]
             ]
        ))->additional([
            "response"=>$result
        ]);
    }

    public static function updateKYC($request)
    {
        App::setLocale($request->header("lang")??"ar");
        $anaService=new AnaService();
        $status=$anaService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ));
        }

        $response = Http::withToken($anaService->settings->token->access_token)
        ->post(
            $anaService->settings->url."/banky/update_custdata",
            [
                "req_ref_id"    => $request->reference_id,
                "instid"        => $anaService->settings->instid,
                "brn_cd"        => $request->brn_cd,
                "custno"        => $request->custno,
                "authid"        => $request->authId
            ]
        );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::updateKYC($request);
        }

        $result=$response->object();

        if(!$response->failed() && isset($result->result_code)){
            if($result->result_code=="0"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("Successfully update your kyc"),
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                        ]
                ))->additional([
                    "response"=>$result
                ]);
            }
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[REGISTER_SERVE]",
                "message"   => [
                    "title"   => $result->result_desc??"Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->result_desc??"Can't process your order, please connect help desk to fix problem!",
                    "code"    => "DIGX_SWITCH_REGISTER_001",
                    "type"    => "ERROR"
                ]
             ]
        ))->additional([
            "response"=>$result
        ]);
    }
}
