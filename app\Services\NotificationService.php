<?php

namespace App\Services;
use App\Data\GeneralResponseData;
use App\Enums\TransactionStatusEnum;
use App\Jobs\ProcessSubscribeToTopics;
use App\Jobs\SendBroadcastPushNotification;
use App\Jobs\SendMailNotification;
use App\Jobs\SendPushNotification;
use App\Jobs\SendSmsNotification;
use App\Mail\PasswordSent;
use App\Models\Cardless;
use App\Models\Notification;
use App\Models\PartyVerify;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\Http;
use Kreait\Firebase\Messaging\SendReport;
use Mail;

use App\Models\User;
use Kreait\Firebase\Messaging\CloudMessage;
use Throwable;

/**
 * Service to handle notifications
 */
class NotificationService
{
    /**
     * Register customer to specific topics
     *
     * @param  array  $messages
     * @param  ?string  $partyId
     * @return void
     */
    public static function sendMessagesToParty($messages,$partyId=null,$checkAppSettings=true) {
        if($checkAppSettings){
            $appConfig=app(\App\Settings\ConfigSettings::class)->appConfig;
            if(!$appConfig->pushNotificationEnabled){
                return;
            }
        }

        $partyVerifies=PartyVerify::where('status',TransactionStatusEnum::COMPLETED->value);
        if(!is_null($partyId)){
            $partyVerifies=$partyVerifies->withoutGlobalScope(CustomerScope::class)
            ->withoutGlobalScope(UsernameScope::class)
            ->where('party_id',$partyId);
        }
        $partyVerifies=$partyVerifies->get();

        if(!count($partyVerifies)){
            return;
        }
        $tokens=$partyVerifies->map(function ($element,int $key){
            if(isset($element->terminal->registrationToken)){
                return $element->terminal->registrationToken;
            }
            return null;
        })->filter()->unique()->toArray();

        if(!count($tokens)){
            return;
        }

        foreach ($messages as $message) {
            $message['tokens']=$tokens;
            static::send($message);
        }
    }
    /*
     *
    array(
        'tokens'=>[],
        'title'=>'',
        'body'=>'',
        'type'=>'',
        'extra_id'=>'',
        'photo'=>'',
        'sound'=>'',
    );
     */

    /**
     * Send the push notification with the given arguments.
     *
     * @param  mixed  ...$arguments
     * @return \Illuminate\Foundation\Bus\PendingDispatch
     */
    public static function send($data) {
       // if(env('APP_HOSTING', 'remote')=='local' && env('APP_ENV', 'production')!='production'){
        //    SendPushNotification::dispatchSync($data);
        //}else{
            return SendPushNotification::dispatch($data)
            ->onQueue('low');
       // }

    }

    /*
     *
    array(
        'user_type'=>'',
        'title'=>'',
        'body'=>'',
        'type'=>'',
        'extra_id'=>'',
        'photo'=>'',
    );
     */
    /**
     * Send the broadcast push notification with the given arguments.
     *
     * @param  array $data
     * @param  string $topic
     * @return \Illuminate\Foundation\Bus\PendingDispatch
     */
    public static function broadcast($data,$topic='main') {
        return SendBroadcastPushNotification::dispatch($data,$topic)
        ->onQueue('low');

    }


     /**
     * Register customer to specific topics
     *
     * @param  array  $topics
     * @param  array  $tokens
     * @return mixed
     */
    public static function subscribeToTopics(array $topics,array $tokens) {
        $tokens=collect($tokens)->filter()->unique()->toArray();

        if(!count($tokens)){
            return;
        }
        ProcessSubscribeToTopics::dispatch($topics,$tokens)->onQueue('default');
    }

    /*
     *
    array(
        'mobile'=>'',
        'message'=>''
    );
     */
    public static function sendSMS($data) {
        //SendMailNotification::dispatchSync('<EMAIL>',new PasswordSent("ddddd"));
        SendSmsNotification::dispatch($data)
        ->onQueue('critical');
    }

     /**
     * Create a new content definition.
     *
     * @param  string|null  $mail
     * @param  Mailable  $data
     *
     * @named-arguments-supported
     */
    public static function email(string $mail,Mailable $data) {
        //$mail??=auth()->user()->mail;
        SendMailNotification::dispatch($mail,$data)
        ->onQueue('critical');
    }
}
