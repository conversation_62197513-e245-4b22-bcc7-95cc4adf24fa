<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\BaseNonNullableDataCollection;
use App\Data\CurrencyAmountData;
use App\Data\NameData;
use App\Data\StatusData;
use App\Models\BillPaymentItem;
use Carbon\Carbon;
use Lang;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\DataCollection;


class BillPaymentQueryYemenMobileData extends BillPaymentQueryBaseData
{
    public function __construct(
        public ?StatusData $status=null,
        #[MapInputName('SD')]
        public ?BillPaymentQuerySdData $subscribeDetails=null,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties=parent::prepareForPipeline($properties);
        return $properties;
    }

    public function toAppResponse(): BillPaymentQueryResponseData
    {
        $parent=parent::toAppResponse();
        if(!is_null($parent)){
            return $parent;
        }

        $data=BillPaymentItem::
        with(['service' => function ($query) {
            $query->where('id', request()->service);
        }])
        ->with(['options'=>function($query){
            $query->with('filter');
        }])
        ->where('payload->mt', $this->subscribeDetails->type)
        ->where('bill_payment_service_id',request()->service)
        ->where('status',1);

        $notZeros=[];
        if(!is_null($this->subscribeDetails->offers) && count($this->subscribeDetails->offers)){
            $notZeros=$this->subscribeDetails->offers?->filter(function($item){
                return $item->amount>0;
            });
        }

        if(count($notZeros)){
            $data=$data->withWhereHas('bundles',function ($query) {
                $query->where('amount->amount','>',0)->whereIn('payload->denId', $this->subscribeDetails->offers->toCollection()->pluck('id'));
            });
        }else{
            $data=$data->with(['bundles' => function ($query) {
                $query->where('amount->amount','>',0);
            }]);
        }

        $data=$data->first();
        //abort(response()->json($data->options));
        foreach ($data->options as $key=>$option) {
            $filled[$option->filter->key_name]??=$option->key_value;
        }
        $filled["item"]=$data->id;

        $offerIds=$this->subscribeDetails->offers->toCollection()->pluck('id');
        $selectedBundle=$data->bundles->first();
        $selectedBundle->load(['options'=>function($query)use($offerIds){
            if(env('DB_CONNECTION')!='oracle'){
                $query->with('filter')->where(function ($query) use ($offerIds) {
                    foreach ($offerIds as $id) {
                        $query->orWhereJsonContains('payload->denId', $id);
                    }
                    $query->orWhereNull('payload->denId');
                });

            }else{
                $placeholders = join(',',collect( $offerIds)->map(function($offerId){
                    return "'$offerId'";
                })->toArray());
                $query->whereRaw("
                    EXISTS (
                        SELECT 1
                        FROM JSON_TABLE(
                            payload,
                            '$'
                            COLUMNS denId PATH '$.denId[*]'
                        ) jt
                        WHERE jt.denId IN ($placeholders)
                    )")->orWhereNull('payload->denId');
            }
        }]);

        foreach ($selectedBundle->options as $option) {
            $filled[$option->filter->key_name]=$option->key_value;
        }
        $filled['bundle']=$selectedBundle->id;

        $config=[
            "bundle"=>[
                "s"=>$data->bundles->pluck('id')->reverse()->values()->toArray()
            ],
        ];

        $items=[
            new BillPaymentQueryResponseItemData(
                item:new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Balance', locale: 'ar'),
                        en:"Balance"
                    ),
                    subtitle: new NameData(
                        ar:"{$this->subscribeDetails->balance}",
                        en:"{$this->subscribeDetails->balance}"
                    )
                ),
                subitem:!is_null($this->subscribeDetails?->loan?->status)?new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Lend', locale: 'ar'),
                        en:"Lend"
                    ),
                    subtitle: new NameData(
                        ar:$this->subscribeDetails?->loan?->status?Lang::get('Borrower', locale: 'ar'):Lang::get('Not borrowing', locale: 'ar'),
                        en:$this->subscribeDetails?->loan?->status?"Borrower":"Not borrowing"
                    )
                ):null
            )
        ];
        if(!is_null($this->subscribeDetails?->loan?->amount)){
            if(in_array($this->subscribeDetails?->loan?->amount,[0,100,200])){
                $filled['payload:lend']="{$this->subscribeDetails?->loan?->amount}";
            }
            $items[]=new BillPaymentQueryResponseItemData(
                item:new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Lend amount', locale: 'ar'),
                        en:"Lend amount"
                    ),
                    subtitle: new NameData(
                        ar:"{$this->subscribeDetails?->loan?->amount}",
                        en:"{$this->subscribeDetails?->loan?->amount}"
                    )
                ),
                subitem:!is_null($this->subscribeDetails?->loan?->desc)?new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Lend description', locale: 'ar'),
                        en:"Lend description"
                    ),
                    subtitle: new NameData(
                        ar:"{$this->subscribeDetails?->loan?->desc}",
                        en:"{$this->subscribeDetails?->loan?->desc}"
                    )
                ):null

            );
        }
        if(count($notZeros)){
            $items=array_merge( $items,[
                new BillPaymentQueryResponseItemData(
                    foregroundColor: "#ffffff",
                    backgroundColor: "#0069A7",
                    item:new BillPaymentQueryResponseSubitemItemData(
                        foregroundColor: "#ffffff",
                        title:new NameData(
                            ar:Lang::get('Offer', locale: 'ar'),
                            en:"Offer"
                        )
                    ),
                    subitem:new BillPaymentQueryResponseSubitemItemData(
                        foregroundColor: "#ffffff",
                        title:new NameData(
                            ar:Lang::get('Expire date', locale: 'ar'),
                            en:"Expire date"
                        )
                    )

                ),
                ...$notZeros->map(function($item,$key){
                    return new BillPaymentQueryResponseItemData(
                        item:new BillPaymentQueryResponseSubitemItemData(
                            title:new NameData(
                                ar:$item->name,
                                en:$item->name
                            )
                        ),
                        subitem:new BillPaymentQueryResponseSubitemItemData(
                            title:new NameData(
                                ar:Lang::get('Expire date', locale: 'ar'),
                                en:"Expire date"
                            ),
                            subtitle: new NameData(
                                ar:$item->expiryDate?->toDateTimeString(),
                                en:$item->expiryDate?->toDateTimeString()
                            )
                        )

                    );
                })
            ]);
        }

        return new BillPaymentQueryResponseData(
            status: $this->status,
            filled:$filled,
            config:$config,
            items: BillPaymentQueryResponseItemData::collect($items, BaseNonNullableDataCollection::class)
        );

    }
}
