<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillPaymentFilter extends Model
{
    use HasFactory;

    protected $fillable = ['id','key_name','title','image','sort','show_title','status','payload'];
    protected $casts = [
        'title' => 'object',
        'payload'           => 'object',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    public function getNameAttribute()
    {
        $local=app()->getLocale();
        return  $this->title?->{"$local"};
    }
    public function filters()
    {
        return $this->hasManyThrough(BillPaymentFilter::class,BillPaymentModelFilter::class,'parent_filter_id','id','id','bill_payment_filter_id');
    }
    public function options()
    {
        return $this->hasMany(BillPaymentFilterOption::class,'bill_payment_filter_id' );
    }
    public function services()
    {
        return $this->morphedByMany(BillPaymentService::class, "model","bill_payment_model_filters");
    }
    public function items()
    {
        return $this->morphedByMany(BillPaymentItem::class, "model","bill_payment_model_filters");
    }
}
