<?php

namespace App\Services;
use App\Data\AccountIdData;
use App\Data\CreditCardData;
use App\Data\GeneralResponseData;
use App\Data\Pass\PassCreateRequestData;
use App\Data\TokenData;

use App\Enums\InvoiceTransactionsTypeEnum;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;

use App;
use App\Models\User;
use <PERSON><PERSON><PERSON><PERSON>n\Type\TrueType;

/**
 * Service to create and update orders
 */
class PassService
{
    public FlexService $flexService;
    public ?GeneralResponseData $error=null;

    /**
     * Service to create and update orders
     */
    public function __construct($request)
    {
        $this->flexService=new FlexService();
        $status=$this->flexService->getAccessToken($request->retry>=0);

        if($status!=200 || $request->retry>0){
            $this->error= GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".$request->retry,
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        //$this->updateInstid();
    }
    /**
     * Register a stub callable that will intercept requests and be able to return stub responses.
     *
     * @param  string  $method
     * @param  string  $url
     * @param  callable|array $params
     * @param  callable|array $fake
     * @param  int $retry
     * @return mixed $response
     */
    function getHttpRequest(string $method,string $url,array $params=[],array $fake=[],int $retry=0){
        $request=Http::withOptions([
            //'verify'=>env('APP_ENV', 'production')!='production'?false:"/u01/banky/ykbank.crt"
        ]);
        if(in_array($url,['banky/cby/mpt_verify_rmt','banky/cby/mpt_get_trx_by_ref','banky/cby/mpt_find_rmt'])){
            $request=$request->timeout(4);//->retry(3, 100);
        }

        $request=$request->withToken($this->flexService->settings->token->access_token);
        if($method=='GET' && !empty($params)){
            $params=[
                'query' => $params,
            ];
        }else if($method!='GET' && !empty($params)){
            $params=[
                'json' => $params,
            ];
        }
        $response = rescue(function () use($request,$method,$url,$params){
            try {
                return $request->send( $method,"{$this->flexService->settings->url}/$url",$params );
            } catch (\Illuminate\Http\Client\ConnectionException $e) {
                return $e->getMessage();
            }
        }, function ($e) {
            return $e->getMessage();
        });
        // if(is_string($response) && in_array($url,['banky/cby/mpt_verify_rmt','banky/cby/mpt_get_trx_by_ref','banky/cby/mpt_find_rmt']) && $retry<3){
        //     return $this->getHttpRequest( $method,$url,$params,$fake);
        // }

        //$response=$request->send( $method,"{$this->settings->url}/$url",$params );
        return $response;

    }
    // public function updateInstid()
    // {
    //     if(env('APP_FLAG',"banky.commercial")!="banky.islamic"){
    //         $settings=app(\App\Settings\ThirdPartySettings::class);
    //         //\Log::info($settings);
    //         $cardless=$settings->cardless;
    //         $test_data=$cardless->test_data;
    //         $test_data->instid="BANKY";
    //         $cardless->test_data=$test_data;
    //         $cardless->instid="BANKY";
    //         $settings->cardless=$cardless;
    //         $settings->save();
    //     }

    // }
    public static function verify(PassCreateRequestData $request) {

        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }

        $response=$passService->getHttpRequest('POST',
        'banky/cby/mpt_verify_rmt',
        $request->toVerifyRemoteApi([
            "instid"    => $passService->flexService->settings->instid,
            "agentid"    => $passService->flexService->settings->instid
        ]));

        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::verify($request);
            }

            $result=$response->object();
            if(isset($result->result_code) &&  $result->result_code=="0"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    "verifyStatus"=> isset($result->cancel_reference)?
                        InvoiceTransactionsTypeEnum::Refund->value:
                        InvoiceTransactionsTypeEnum::Payment->value,
                    "extra"=>$result
                ]);
            }else if($result->result_code=="20"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "",
                        "message"   => [
                            "title"   =>__("Can't verify this remittance, customer service will review this transaction & call you back soon!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_WASIL_NOT_FOUND_001",
                            "type"    => "INFO"
                        ]
                    ]
                ));
            }

        }

        return GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" =>"[WASIL-VERIFY]",
                "message"   => [
                    "title"   => $result?->result_desc??__("Can't connect with service provider, please try again later!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_WASIL_102",
                    "type"    => "ERROR"
                ]
            ]
        ]);

    }
    public static function commision(PassCreateRequestData $request)
    {

        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }

        $response=$passService->getHttpRequest('POST',
        'banky/cby/get_commission',
        $request->toCommisionRemoteApi([
            "req_ref_id"    => uniqid(),
            "instid"        => $passService->flexService->settings->instid,
            "company_nm"    => "CBY_PASS"
        ]));

        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::commision($request);
            }

            $result=$response->object();
            if(isset($result->result_code) &&  $result->result_code=="0"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'commission'=>$result->commission,
                ]);
            }
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[WASIL-COMMISSION]",
                "message"   => [
                    "title"   => $result?->result_desc??__("Can't connect with service provider, please try again later!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_WASIL_001",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }

    public static function findByReference(PassCreateRequestData $request)
    {

        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }
        $response=$passService->getHttpRequest('POST',
        'banky/cby/mpt_get_trx_by_ref',
        [
            "req_ref_id"    => $request->referenceId,
            "instid"        => $passService->flexService->settings->instid,
            "agentid"    => $passService->flexService->settings->instid,
        ]);

        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::findByReference($request);
            }

            $result=$response->object();
            if(isset($result->result_code) && $result->result_code=="0" && isset($result->response)){
                $resultResponse=collect($result->response)->first();
                if(!is_null($resultResponse) &&
                    isset($resultResponse->req_cby_mpt_trans_ref) &&
                    !is_null($resultResponse->req_cby_mpt_trans_ref) &&
                    !empty($resultResponse->req_cby_mpt_trans_ref)
                ){
                    $verifyResult=PassService::verify(PassCreateRequestData::from([
                        "reference_id"=> uniqid(),
                        "external_reference_id"=> $request->externalReferenceId?? $resultResponse->req_cby_mpt_trans_ref,
                        "row_version" => $request->operationReference??$resultResponse->req_cby_mpt_trans_ref,
                    ]));

                    if($verifyResult->status->message->code=="0" || $verifyResult->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
                        return $verifyResult;
                    }
                    return GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "[WASIL-VERIFYBYID]",
                            "message"   => [
                                "title"   => __("Can't verify this remittance, customer service will review this transaction & call you back soon!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_WASIL_101",
                                "type"    => "ERROR"
                            ]
                            ]
                    ]);
                }
            }
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[WASIL-VERIFYBYID]",
                "message"   => [
                    "title"   => $result?->result_desc??__("Can't connect with service provider, please try again later!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_WASIL_001",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }
    public static function send(PassCreateRequestData $request)
    {

        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }

        $response=$passService->getHttpRequest('POST',
        'banky/cby/mpt_create_rmt',
        $request->toSendRemoteApi([
            //"ref_id"    => uniqid(),
            "instid"    => $passService->flexService->settings->instid,
            "agentid"    => $passService->flexService->settings->instid,
            "show_sender_name"=> true
        ]));

        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::send($request);
            }

            $result=$response->object();
            if($result->result_code=="0"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("Successfully send money to mobile number"),
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'extra'=>$result,
                    //'externalReferenceId'=>$result->mpt??"",
                ]);
            }
        }
        // else{
        //     $response=PassService::findByReference($request);
        //     $result=$response->getAdditionalData()['extra']??null;
        //     if($response->status->message->code=="0" && isset($result->financial_reference) && !isset($result->cancel_reference)){
        //         $response->status->message->title=__("Successfully send money to mobile number");
        //         return $response;
        //     }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
        //         return $response;
        //     }else{
        //         return GeneralResponseData::from(array(
        //             'status'=>[
        //                 "result"    => "ERROR",
        //                 "contextID" => "[WASIL-SEND]",
        //                 "message"   => [
        //                     "title"   => __("Service provider take long time to response, customer service will review this transaction & call you back soon!"),
        //                     "detail"  => "",
        //                     "code"    => "DIGX_SWITCH_WASIL_TIMEOUT",
        //                     "type"    => "ERROR"
        //                 ]
        //             ]
        //         ));
        //     }

        // }
        $msg=$result?->result_desc??null;

        $response=PassService::findByReference($request);
        $result=$response->getAdditionalData()['extra']??null;
        if($response->status->message->code=="0" && isset($result->financial_reference) && !isset($result->cancel_reference)){
            $response->status->message->title=__("Successfully send money to mobile number");
            return $response;
        }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
            $response->status->message->title=$msg??$response->status->message->title;
            return $response;
        }else{
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[WASIL-SEND]",
                    "message"   => [
                        "title"   => __("Service provider take long time to response, customer service will review this transaction & call you back soon!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_TIMEOUT",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

    }
    public static function find($request)
    {

        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }

        $response=$passService->getHttpRequest('POST',
        'banky/cby/mpt_find_rmt',
        $request->toStatusRemoteApi([
            "instid"    => $passService->flexService->settings->instid,
            "agentid"    => $passService->flexService->settings->instid
        ]));

        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::find($request);
            }

            $result=$response->object();
            if(isset($result->result_code)){
                if($result->result_code=="0" && isset($result->status) && $result->status==1){
                    return GeneralResponseData::from(array(
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   =>__("Remittance not recieved!"),
                                "detail"  => "",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ))->additional([
                        'details'=>$result,
                    ]);
                }else if($result->result_code=="20"){
                    return GeneralResponseData::from(array(
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   =>__("Remittance recieved!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_WASIL_NOT_FOUND_001",
                                "type"    => "INFO"
                            ]
                        ]
                    ));
                }else if($result->result_code=="21"){
                    return GeneralResponseData::from(array(
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   =>__("This remittance expired!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_WASIL_EXPIRED_001",
                                "type"    => "INFO"
                            ]
                        ]
                    ))->additional([
                        'details'=>json_decode(json_encode([
                            "message"    => $result?->result_desc??"Expired",
                            "mpt"=>$request->externalReferenceId
                        ])),
                    ]);
                }
            }

        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[WASIL-FIND]",
                "message"   => [
                    "title"   => $result?->result_desc??__("Can't connect with service provider, please try again later!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_WASIL_001",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }

    public static function receive($request)
    {
        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }
        $response=$passService->getHttpRequest('POST',
        'banky/cby/mpt_collect_rmt',
        $request->toReceiveRemoteApi([
            "instid"    => $passService->flexService->settings->instid,
            "agentid"    => $passService->flexService->settings->instid
        ]));

        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::receive($request);
            }

            $result=$response->object();
            if($result->result_code=="0"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("Remittance successfully harvest to your account."),
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'extra'=>$result,
                ]);
            }
        }
        // else{
        //     $response=PassService::findByReference($request);
        //     $result=$response->getAdditionalData()['extra']??null;
        //     if($response->status->message->code=="0" && isset($result->collected_by)){
        //         $response->status->message->title=__("Remittance successfully harvest to your account.");
        //         return $response;
        //     }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
        //         return $response;
        //     }else{
        //         return GeneralResponseData::from(array(
        //             'status'=>[
        //                 "result"    => "ERROR",
        //                 "contextID" => "[WASIL-RECEIVE]",
        //                 "message"   => [
        //                     "title"   => sprintf(__("Service provider take long time to response, if your remittance received & not entered to your account please contact customer service and provide them with this reference [%s]!"),$request->referenceId),
        //                     "detail"  => "",
        //                     "code"    => "DIGX_SWITCH_WASIL_TIMEOUT",
        //                     "type"    => "ERROR"
        //                 ]
        //             ]
        //         ));
        //     }

        // }
        $response=PassService::findByReference($request);
        $result=$response->getAdditionalData()['extra']??null;
        if($response->status->message->code=="0" && isset($result->collected_by)){
            $response->status->message->title=__("Remittance successfully harvest to your account.");
            return $response;
        }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
            return $response;
        }else{
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[WASIL-RECEIVE]",
                    "message"   => [
                        "title"   => sprintf(__("Service provider take long time to response, if your remittance received & not entered to your account please contact customer service and provide them with this reference [%s]!"),$request->referenceId),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_TIMEOUT",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }
        // return GeneralResponseData::from(array(
        //     'status'=>[
        //         "result"    => "ERROR",
        //         "contextID" => "[WASIL-RECEIVE]",
        //         "message"   => [
        //             "title"   => $result?->result_desc??__("Can't connect with service provider, please try again later!"),
        //             "detail"  => "",
        //             "code"    => "DIGX_SWITCH_WASIL_001",
        //             "type"    => "ERROR"
        //         ]
        //     ]
        // ));
    }
    public static function cancel($request)
    {
        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }

        $response=$passService->getHttpRequest('POST',
        'banky/cby/mpt_cancel_rmt',
        $request->toCancelRemoteApi([
            //"ref_id"    => uniqid(),
            "instid"    => $passService->flexService->settings->instid,
            "agentid"    => $passService->flexService->settings->instid,
        ]));

        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::cancel($request);
            }

            $result=$response->object();

            if(isset($result->result_code) && $result->result_code == "0"){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => __("Successfully cancel the remittance"),
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'extra'=>$result,
                ]);
            }
        }

        $response=PassService::findByReference($request);
        $result=$response->getAdditionalData()['extra']??null;
        if($response->status->message->code=="0" && isset($result->cancel_reference) && !empty($result->cancel_reference??"")){
            $response->status->message->title=__("Successfully cancel the remittance");
            return $response;
        }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
            return $response;
        }else{
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[WASIL-CANCEL]",
                    "message"   => [
                        "title"   => __("Service provider take long time to response, customer service will review this transaction & call you back soon!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_TIMEOUT",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        // return GeneralResponseData::from(array(
        //     'status'=>[
        //         "result"    => "ERROR",
        //         "contextID" =>"[WASIL-CANCEL]",
        //         "message"   => [
        //             "title"   => $result?->result_desc??__("Can't connect with service provider, please try again later!"),
        //             "detail"  => "",
        //             "code"    => "DIGX_SWITCH_WASIL_104",
        //             "type"    => "ERROR"
        //         ]
        //     ]
        // ));
    }

    public static function expired($request)
    {
        $passService=new PassService($request);

        if(!is_null($passService->error)){
            return $passService->error;
        }

        $response=$passService->getHttpRequest('POST',
        'banky/cby/mpt_get_expired_rmts',[
            "req_ref_id"    =>$request->referenceId,
            "instid"    => $passService->flexService->settings->instid,
            "agentid"    => $passService->flexService->settings->instid,
            "getdate"=>\Carbon\Carbon::now()->format("Y-m-d")
        ]);

        $transactions=[];
        $result=null;
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=$request->retry+1;
                return static::expired($request);
            }

            $result=$response->object();

            if(isset($result->result_code) && $result->result_code == "0" && isset($result->transactions)){
                $transactions=$result->transactions;
            }
        }
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'transactions'=>$transactions,
        ]);
    }
}
