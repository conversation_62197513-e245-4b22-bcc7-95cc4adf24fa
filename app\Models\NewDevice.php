<?php

namespace App\Models;

use App\Data\AppConfigData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewDevice extends Model
{
    protected $table = 'new_devices';
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['party_id','party_name','user_id','note','devices'];
    protected $casts = [
        'devices' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    public function user()
    {
        return $this->belongsTo('App\Models\User',"user_id");
    }


}
