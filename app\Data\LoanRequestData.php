<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Numeric;
use Spatie\LaravelData\Attributes\Validation\Required;
use <PERSON><PERSON>\LaravelData\Data;

class LoanRequestData extends Data
{
    #[Nullable]
    public ?string $customer_no;
    #[Required]
    public string $product_code;
    #[Required,Numeric]
    public string $amount;
    #[Required]
    public string $currency_code;
    #[Required]
    public string $sector_code;
    #[Required]
    public string $activity_id;
    #[Required]
    public string $activity_name;
    #[Required]
    public string $activity_phone;
    #[Required]
    public string $activity_address;
    #[Required]
    public string $closest_branch_id;
    #[Nullable]
    public string $payment_reason;
}