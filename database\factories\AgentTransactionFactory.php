<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\AgentTransaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\AgentTransaction>
 */
final class AgentTransactionFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var string
    */
    protected $model = AgentTransaction::class;

    /**
    * Define the model's default state.
    *
    * @return array
    */
    public function definition(): array
    {
        return [
            'party_id' => \App\Models\AgentUser::factory(),
        ];
    }
}
