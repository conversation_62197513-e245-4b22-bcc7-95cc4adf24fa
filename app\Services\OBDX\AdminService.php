<?php

namespace App\Services\OBDX;
use App\Data\GeneralResponseData;
use App\Data\TokenData;
use App\Models\PartyVerify;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Crypt;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Support\Facades\Http;
use App;

/**
 * Service to create and update orders
 */
class AdminService
{
    protected $url;

    protected $cookies;
    protected $headers;

    public $settings;
    public $isTest=false;

     /**
     * Service to handle customer requestes
     *
     */
    public function __construct()
    {
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->obdxAdmin;
        if($this->settings->is_test){
            $this->isTest=true;
            $this->settings=$this->settings->test_data;
        }
        $this->url="{$this->settings->url}/digx/v1";
        $this->setCookies();
        $this->headers =  collect(request()->headers->all())->except([
            'host',
            'cookie',
            'content-length',
            "user-agent",
            "postman-token",
            "content-type",
            "token",
            "x-original-url",
            "x-challenge-response"
        ])->all();
        $this->headers+=["Accept-Encoding"=>"identity"];
    }

    /**
     * Register a stub callable that will intercept requests and be able to return stub responses.
     *
     * @param  string  $method
     * @param  string  $url
     * @param  callable|array $params
     * @param  callable|array $fake
     * @return \Illuminate\Http\Client\Response
     */
    function getHttpRequest(string $method,string $url,array $params=[],array $fake=[]){
        if(!empty($fake)){
            $request=Http::fake($fake);
        }else{
            $request=Http::withOptions([
                'cookies' => $this->cookies
            ]);
        }
        //$request=$request->withHeaders($this->headers);
        if($method=='GET' && !empty($params)){
            $params=[
                'query' => $params,
            ];
        }else if($method!='GET' && !empty($params)){
            $params=[
                'json' => $params,
            ];
        }
        $response=$request->withHeaders($this->headers)->send( $method,"{$this->url}/$url", $params);
        return $response;

    }
    public static function getCookies()
    {
        return collect(request()->cookies->all())
            ->map(function ($cookie,$key) {
                return $key . '=' . ($cookie ?? '') . '; ';
            })->toArray();
    }

    /**
     * Inject cookies to current request session
     *
     * @param  array  $cookies
     * @return void
     */
    protected function setCookies()
    {
        $cookies =json_decode($this->settings->token?->access_token??null,true);
        if(!is_null($cookies)){
            $this->cookies=CookieJar::fromArray($cookies, parse_url($this->settings->url, PHP_URL_HOST) /*"{$_ENV['OBDX_URL']}"*/);
        }else{
            $this->cookies=$cookies;
        }

    }

     /**
     * request access token.
     *
     * @param  bool $forceUpdate
     * @return ?int|?string
     */
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
            $this->cookies=null;
        }
        if(!isset($token->access_token) || is_null($token->access_token)||empty($token->access_token)){
            $response = rescue(function (){
                return Http::withOptions([
                    'verify' => false,
                ])
                ->withoutRedirecting()
                ->timeout(10)
                ->asForm()
                ->post(
                    "{$this->url}/j_security_check",
                    [
                        'j_username' => $this->settings->client_id,
                        'j_password' => $this->settings->client_secret,
                    ]
                );
            }, function ($e) {
                return $e->getMessage();
            });

            if(is_string($response)){
                return null;
            }

            if ($response->failed()) {
                return null;
            }
            $status=$response->status();
            if(in_array($response->status(),[303,302])){
                $status=200;

                $cookies=collect($response->cookies()->toArray())
                ->flatMap(function ($values) {
                    //Cookie::queue($values["Name"], $values["Value"]);
                    return array_map(function ($value){
                        return $value;
                    }, [$values["Name"]=>$values["Value"]]);
                })->toArray();

                $this->settings->token=TokenData::from([
                    "access_token"=>json_encode($cookies),
                    "token_type"=>"cookies"
                ]);

                //Add cookies to current request
                $this->setCookies();

                $settings=app(\App\Settings\ThirdPartySettings::class);
                $obdxAdmin=$settings->obdxAdmin;
                if($obdxAdmin->is_test){
                    $test_data=$obdxAdmin->test_data;
                    $test_data->token=$this->settings->token;
                    $obdxAdmin->test_data=$test_data;
                }else{
                    $obdxAdmin->token=$this->settings->token;
                }
                $settings->obdxAdmin=$obdxAdmin;
                $settings->save();
            }
        }
        return $status;
    }
    static function checkUsername($user){
        $pattern = "/^[A-Za-z0-9@._-]*$/";
        return preg_match($pattern, $user);
    }
    static function checkEmail($email) {
        $find1 = strpos($email, '@');
        $find2 = strpos($email, '.');
        return ($find1 !== false && $find2 !== false);
    }
    static function checkPhone($phone) {
        $find1 = strlen($phone);
        $find2 = str_starts_with($phone, '70')||
                    str_starts_with($phone, '71')||
                    str_starts_with($phone, '73')||
                    str_starts_with($phone, '77')||
                    str_starts_with($phone, '78');
        return ($find1==9 && $find2);
    }
    public static function usernames($request,$filter)
    {
        if (!static::checkUsername($filter) ) {
            return abort(response(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "LOGIN-UNVALID",
                    "message"   => [
                        "title"   => __("Username must be in one of the following formats"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_UNVALID_001",
                        "type"    => "ERROR"
                    ]
                ]
            ])->toArray()));
        }
        $filters=[];
        if (static::checkPhone($filter) ) {
            $filters["mobileNumber"]=$filter;
        }

        $filters["username"]=$filter;

        if (static::checkEmail($filter) ) {
            $filters["emailId"]=$filter;
        }
        // $filters=[
        //     "username"=>$filter,
        //     "mobileNumber"=>$filter,
        //     "emailId"=>$filter,
        //     //"username"=>$filter,
        // ];
        //https://online.yk-bank.com:8080/digx/v1/users?emailId=<EMAIL>&mobileNumber=*********&username=183415&partyId=0183415&userType=retailuser
        // $responses = Http::
        // pool(fn (Pool $pool) => [
        //     $pool->as('username')
        //         ->withOptions([
        //             'cookies' => $service->cookies,
        //         ])
        //         ->get("{$service->url}/users",[
        //             "username"=>$filter,
        //             "userType"=>'retailuser'
        //         ]),

        //     $pool->as('email')
        //         ->withOptions([
        //             'cookies' => $service->cookies,
        //         ])
        //         ->get("{$service->url}/users",[
        //             "emailId"=>$filter,
        //             "userType"=>'retailuser'
        //         ]),
        //         $pool->as('mobile')
        //         ->withOptions([
        //             'cookies' => $service->cookies,
        //         ])
        //         ->get("{$service->url}/users",[
        //             "mobileNumber"=>$filter,
        //             "userType"=>'retailuser'
        //         ]),
                // $pool->as('party')
                // ->withOptions([
                //     'cookies' => $service->cookies,
                // ])
                // ->get("{$service->url}/users",[
                //     "partyId"=>$filter,
                //     "userType"=>'retailuser'
                // ]),


        //]);
        foreach ($filters as $key => $filter) {
            $object=new \stdClass();
            $object->withValidation=true;
            $customerInfo = static::customerInfo($object,[
                "$key"=>$filter,
                "userType"=>'retailuser'
            ]);

            if(!is_object($customerInfo) && is_int($customerInfo) && $customerInfo==-1){
                return null;
            }

            if(!is_null($customerInfo)){
                return $customerInfo->username;
            }
        }
        return null;
    }

    public static function customerInfo($request,$params)
    {
        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return -1;
        }

        $response = Http::withOptions([
            'cookies' => $service->cookies,
            //'debug' => true,
        ])
        //->dump()
        ->get("{$service->url}/users",$params);

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::customerInfo($request,$params);
        }
        $result=$response->object();
        if(isset($result->userDTOList) && count($result->userDTOList)){
            if(isset($params['username'])){
                $userDTOList=collect($result->userDTOList)->where('username',$params['username'])->last();
            }else{
                $userDTOList=collect($result->userDTOList)->whereNotNull('partyId')->last();
                if(is_null($userDTOList)){
                    $userDTOList=collect($result->userDTOList)->last();
                }
            }
            if(isset($request->withValidation) && isset($userDTOList->lockStatus) && $userDTOList->lockStatus=="LOCK"){
                return abort(response(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "LOGIN-UNVALID",
                        "message"   => [
                            "title"   => __("Your account is locked, please try again in 30 minutes."),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_UNVALID_002",
                            "type"    => "ERROR"
                        ]
                    ]
                ])->toArray()));
            }
            return $userDTOList;
        }
        return null;
    }


    public static function userInfo($request,$username)
    {
        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return -1;
        }

        $response = Http::withOptions([
            'cookies' => $service->cookies,
            //'debug' => true,
        ])
        //->dump()
        ->get("{$service->url}/users/{$username}",[]);

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::userInfo($request,$username);
        }
        $result=$response->object();
        if(isset($result->userDTO)){
            return $result->userDTO;
        }
        return null;
    }

    public static function approvalRules($request,$partyId,$username):bool
    {
        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return false;
        }

        $response = Http::withOptions([
            'cookies' => $service->cookies,
            //'debug' => true,
        ])
        //->dump()
        ->get("{$service->url}/approvalRules",[
            "partyId"=>$partyId
        ]);

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::approvalRules($request,$partyId,$username);
        }
        $result=$response->object();
        if(isset($result->ruleDTOs)){
            $ruleDTOs=collect($result->ruleDTOs)->filter(function ($rule) use($username) {
                return !$rule->approvalRequired && collect($rule->initiatorUserGroup->users)->contains('userId', $username);
            })->values()->all();
            if(count($ruleDTOs)){
                return true;
            }
        }
        return false;
    }

    public static function preferences($request,$partyId):bool
    {
        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return false;
        }

        $response = Http::withOptions([
            'cookies' => $service->cookies,
            //'debug' => true,
        ])
        //->dump()
        ->get("{$service->url}/parties/$partyId/preferences",[]);

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::preferences($request,$partyId);
        }

        $result=$response->object();
        if(in_array($response->status(),[400]) && isset($result->message->code) && $result->message->code=="DIGX_PROD_DEF_0000" && isset($result->message->relatedMessage)){
            $relatedMessage=collect($result->message->relatedMessage)->first();
            if(!is_null($relatedMessage) && isset($relatedMessage->code) && $relatedMessage->code=="FC_SM_025"){
                $request->retry=($request->retry??-1)+1;
                return static::preferences($request,$partyId);
            }
        }

        if(isset($result->partyPreferencesDTOs)){
            return $result->partyPreferencesDTOs->approvalType=="ZE";
        }
        return false;
    }

    public static function accountAccess($request,$partyId)
    {
        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return false;
        }

        $response = Http::withOptions([
            'cookies' => $service->cookies,
            'verify' => false,
            //'debug' => true,
        ])
        //->dump()
        ->get("{$service->url}/accountAccess",[
            "accountType"=>"CSA",
            "partyId"=>$partyId,
            "partySpecificSetup"=>true,
            "locale"=>"en"
        ]);

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::accountAccess($request,$partyId);
        }
        $result=$response->object();
        if(isset($result->accounts[0]->accountsList)){
            // $ruleDTOs=collect($result->accounts->accountsList)
            // ->filter(function ($rule) use($username) {
            //     return !$rule->approvalRequired && collect($rule->initiatorUserGroup->users)->contains('userId', $username);
            // })->values()->all();
            // if(count($ruleDTOs)){
            //     return true;
            // }
            return $result->accounts[0]->accountsList;
        }
        return false;
    }

    public static function sharingProfile(string $signature)
    {
        $service=new static();
        try {
            $profile=new \stdClass();
            $partyId=Crypt::decrypt($signature);

            if (strpos($partyId, ':') !== false) {
                $parts = explode(':', $partyId);
                $partyId=$parts[0];
                $username=$parts[1];
                // $parts[1] will be 'value'
            }

            $partyVerify=PartyVerify::withoutGlobalScope(CustomerScope::class)
            ->withoutGlobalScope(UsernameScope::class)
            //->withTrashed()
            ->where(function($query) use($partyId,$service){
                $query->where("party_id",$service->getPartyId($partyId))
                ->orWhere("party_id",$partyId);
            });
            if(isset($username)){
                $partyVerify=$partyVerify->where("username",$username);
            }
           // ->where('party_id',$partyId)
           $partyVerify=$partyVerify->first();

            if(!is_null($partyVerify)){
                $object=new \stdClass();
                //mobileNumber
                $params=[
                    "userType"=>'retailuser'
                ];
                if(isset($username)){
                    $params["userType"]='corporateuser';
                    $params["username"]=$username;
                }
                if(static::checkPhone($partyId) ) {
                    $customerInfo = static::customerInfo($object,$params+[
                        "mobileNumber"=>$partyId
                    ]);
                }else{
                    $customerInfo = static::customerInfo($object,$params+[
                        "partyId"=>$partyId,
                    ]);
                }



                if(!is_null($customerInfo) && is_object($customerInfo)){
                    $profile->id=isset($username)?$username:$partyId;
                    $profile->name=html_entity_decode($customerInfo->firstName)." ".html_entity_decode($customerInfo->lastName);
                    $profile->phone=$partyVerify->phone_number??$customerInfo->mobileNumber??null;

                    return GeneralResponseData::from([
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   => "",
                                "detail"  => "",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ])->additional([
                        "profile"=>$profile
                    ]);

                }
            }
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {}

        return GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "INFO-SHARING-PROFILE",
                "message"   => [
                    "title"   => __('Transaction not exist!'),
                    "detail"  => __('Transaction not exist!'),
                    "code"    => "DIGX_SWITCH_SHARING_PROFILE_100",
                    "type"    => "ERROR"
                ]
            ]
        ]);
    }
    protected function getPartyId($id)
    {
        if(substr($id,0,1)!="0"){
            $partyId="0{$id}";
        }else{
            $partyId=substr($id,1);
        }
        return $partyId;
    }
}
