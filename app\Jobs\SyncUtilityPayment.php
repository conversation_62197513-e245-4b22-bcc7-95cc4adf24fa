<?php

namespace App\Jobs;

use App\Data\BaseNonNullableDataCollection;
use App\Data\CurrencyAmountData;
use App\Data\Form\FormBaseData;
use App\Data\Form\FormFieldData;
use App\Data\Form\FormGeneralData;
use App\Data\Form\FormOptionData;
use App\Data\Form\FormValidationData;
use App\Data\NameData;
use App\Enums\BillPaymentServiceCodeEnum;
use App\Models\BillPaymentBundle;
use App\Models\BillPaymentFilter;
use App\Models\BillPaymentItem;
use App\Models\BillPaymentModelFilter;
use App\Models\BillPaymentModelFilterOption;
use App\Models\BillPaymentService;
use App\Models\SplitPayment;
use App\Services\UtilityPayementService;
use File;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Lang;
use Spatie\LaravelData\DataCollection;
class SyncUtilityPayment implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $translateNames;
    protected $billerCategories;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->translateNames=include(base_path().'/resources/mocks/up/translate_names.php');
        $this->billerCategories=[
            "SABAFON"=> "MOBILE",
            "SABAFON2"=> "MOBILE",
            "MTN"=> "MOBILE",
            "YEMENMOBILE"=> "MOBILE",
            "YEMENMOBILE2"=> "MOBILE",
            "YGSM"=> "MOBILE",
            "POST ADSL"=> "ADSL&#x2f;LANDLINE&#x2f;WATER",
            "POST LANDLINE"=> "ADSL&#x2f;LANDLINE&#x2f;WATER",
            "WATER"=> "ADSL&#x2f;LANDLINE&#x2f;WATER",
            "ELECTRICYBOARD"=> "ELECTRIYBOARD",
            "ELECTRIYBOARD"=> "ELECTRIYBOARD",
            "COMMERCIALELE"=> "COMMERCIALELE",
            "INTERNET_CARD"=> "Internet Cards",
            "MUASALATCARD"=> "Internet Cards",
            "QC_Wallet"=> "WALLET",
            "MUASALAT"=> "WALLET",
            "ENTERTAINMENT"=> "VARIOUS",
            "POST_YEMEN_4G"=> "ADSL&#x2f;LANDLINE&#x2f;WATER",
            //eslamic
            "Sabafon"=> "MOBILE",
            "Yemen-Mobile"=> "MOBILE",
            "Y-GSM"=> "MOBILE",
            "Water"=> "POST",
            "LAND-Line"=> "POST",
            "ADSL"=> "POST",
            "ADENNET"=> "ADENNET",
            "Floosak"=> "WALLET",
            "POSTUNIVERSITYFEE"=> "UNIVERSITY",
            "POSTUNIVERSITY"=> "UNIVERSITY",
            ""=> null,
        ];

    }
    protected function isGrid($sc): bool
    {
        return in_array($sc, [BillPaymentServiceCodeEnum::games->value,BillPaymentServiceCodeEnum::government->value]);

    }
    /**
     * Execute the job.
     */
    public function handle(): void
    {

        // if(env('APP_HOSTING', 'remote')=='local' && env('APP_ENV', 'production')!='production'){
        //     Http::fake([
        //         'https://ubp.yk-bank.com:3443/api/tp/v1/catalog*' => Http::response(
        //             json_decode(\File::get(base_path( "/resources/mocks/up/catalog.json")),true),
        //             200)
        //     ]);
        // }
        $result=UtilityPayementService::catalog();
        if(!isset($result->ExtData)){
          return;
        }
        $billPaymentServices=BillPaymentService:: with(['filters' => function ($query) {
            $query->with('options');
        }])
        ->with(['items' => function ($query) {
            $query->with(['filters' => function ($query) {
                $query->with('options');
            }])->with(['bundles' => function ($query) {
                $query->with("options");
            }]);
        }])->get();

        $filters=BillPaymentFilter::orderBy('sort','ASC')->get();

        foreach( $result->ExtData as $service){
            $this->getActionCode($service);
            // $billPaymentService=BillPaymentService::where('payload->sc',$service->sc)
            // ->first();
            $billPaymentService = $billPaymentServices->first(function ($billPaymentService) use($service) {
                return $billPaymentService->payload->sc==$service->sc;
            });
            //$fields=new DataCollection(FormFieldData::class,[]);
            //$fields=collect([]);
            if(is_null($billPaymentService)){
                //$fields=$this->buildFieldsService($fields,$service,$item);
                $billPaymentService =BillPaymentService::create([
                    'id'=>$service->sc,
                    'title'=>NameData::from([
                        "ar"=>$service->scName,
                        "en"=>$service->scName,
                    ])->toArray(),
                    'view_type'=>$this->isGrid($service->sc)?'grid':null,
                    'url'=>$this->isGrid($service->sc)?'/app/billPayment/%s/%s':null,
                    //'fields'=>$fields,
                    'status'=>1,
                    'payload'=>collect($service)->except(['scItems'])->all(),
                ]);
                //$filters=BillPaymentFilter::whereNotNull('payload->sc')/*whereJsonContains('payload', $service->sc)*/->orderBy('sort','ASC')->get();
                $_filters = $filters->filter(function ($filter) use($service) {
                    return isset($filter->payload->sc) && in_array($service->sc, $filter->payload->sc??[]);
                });

                if(count($_filters)){
                    $filterServices=[];
                    $parentId=null;
                    foreach($_filters as $filter){
                        $filterServices[]=[
                            "model_type"=>BillPaymentService::class,
                            "model_id"  =>$billPaymentService->id,
                            "bill_payment_filter_id"=>$filter->id,
                            "parent_filter_id"=>$parentId,
                            "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                        ];
                        $parentId=$filter->id;
                    }
                    if(!empty($filterServices)){
                        collect($filterServices)->chunk(100)
                        ->each(function ($chunked) {
                            BillPaymentModelFilter::insert($chunked->values()->toArray());
                        });
                    }
                }
            }else{
                if(is_null($billPaymentService->view_type) && $this->isGrid($service->sc)){
                    $billPaymentService->view_type='grid';
                }
                if($this->isGrid($service->sc)){
                    $billPaymentService->url='/app/billPayment/%s/%s';
                }
                $billPaymentService->payload=collect($service)->except(['scItems'])->all();
                //$billPaymentService->save();
            }

            $billPaymentService->loadMissing(['filters' => function ($query) {
                $query->with("options"); // ✅ Filtering authors
            }]);

           // return abort(response()->json([]));
        //    if($service->sc==BillPaymentServiceCodeEnum::government->value){
        //         $fff=collect($service->scItems)->first();
        //         abort(response()->json([
        //             "1"=>count($fff->bundles),
        //             "2"=>!isset(collect($fff->bundles)->first()->denId),
        //             "3"=>!$fff->allowOpenAmount,
        //             "4"=>!(count($fff->bundles) && !isset(collect($fff->bundles)->first()->denId) && !$fff->allowOpenAmount)
        //         ]));
        //    }

            $service->scItems=collect($service->scItems)->filter(function ($item)use($service) {
                return !(
                            count($item->bundles) &&
                            !isset(collect($item->bundles)->first()->denId) &&
                            !$item->allowOpenAmount
                        ) || !is_null($this->getAdditionalFields($service,$item));
            });

           //$billPaymentItems=BillPaymentItem::get();
            foreach( $service->scItems as &$item){
                $billPaymentItem = $billPaymentService->items?->firstWhere(function ($billPaymentItem) use($item) {
                    return $billPaymentItem->payload->item==$item->item;
                });

                // $billPaymentItem=BillPaymentItem::where('payload->item',$item->item)
                // ->first();
                if(is_null($billPaymentItem)){
                    $image=null;
                    if(File::exists(storage_path("app/public/games/{$item->item}.png"))) {
                        $image=Storage::url("games/{$item->item}.png");
                    }

                    $billPaymentItem =BillPaymentItem::create([
                        'id'=>$item->item,
                        'bill_payment_service_id'=>$billPaymentService->id,
                        'title'=>NameData::from([
                            "ar"=>$this->translateNames[$item->item]??$item->itemName,
                            "en"=>$item->itemName,
                        ])->toArray(),
                        'biller'=>$item->externalCode??null,
                        'biller_category'=>$this->billerCategories[$item->externalCode??""]??null,
                        'additional_fields'=>$this->getAdditionalFields($service,$item),
                        'image'=>$image,
                        'view_type'=>$this->isGrid($billPaymentService->payload->sc)?'chip':null,
                        'status'=>1,
                        'payload'=>collect($item)->except(['bundles'])->all(),
                    ]);

                    $this->handleItemFilter($billPaymentService,$billPaymentItem,"mt",$item->mt);
                    $this->handleItemFilter($billPaymentService,$billPaymentItem,"item",$item->item);

                   // $filters=BillPaymentFilter::whereNotNull('payload->item')->orderBy('sort','ASC')->get();
                    $_filters = $filters->filter(function ($filter) use($item) {
                        return isset($filter->payload->item) && in_array($item->item, $filter->payload->item??[]);
                    });
                    if(count($_filters)){
                        $filterItems=[];

                        $parentId=null;
                        foreach($_filters as $filter){
                            $filterItems[]=[
                                "model_type"=>BillPaymentItem::class,
                                "model_id"  =>$billPaymentItem->id,
                                "bill_payment_filter_id"=>$filter->id,
                                "parent_filter_id"=>$parentId,
                                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                            ];
                            $parentId=$filter->id;
                        }
                        if(!empty($filterItems)){
                            collect($filterItems)->chunk(100)
                            ->each(function ($chunked) {
                                BillPaymentModelFilter::insert($chunked->values()->toArray());
                            });
                        }
                    }
                }else{
                    $image=null;
                    if(File::exists(storage_path("app/public/games/{$item->item}.png"))) {
                        $image=Storage::url("games/{$item->item}.png");
                        $billPaymentItem->image=$image;
                    }

                    $billPaymentItem->additional_fields=$this->getAdditionalFields($service,$item);
                    if(is_null($billPaymentItem->view_type) && $this->isGrid($billPaymentService->payload->sc)){
                        $billPaymentItem->view_type='chip';
                    }
                    $billPaymentItem->payload=collect($item)->except(['bundles'])->all();
                    //$billPaymentItem->save();
                }
                $billPaymentItem->loadMissing(['filters' => function ($query) {
                    $query->with("options"); // ✅ Filtering authors
                }]);

                if(count($item->bundles) && isset(collect($item->bundles)->first()->denId) && $item->allowOpenAmount){
                    $newBundle=clone (collect($item->bundles)->first());
                    $newBundle->denId="000000";
                    $newBundle->AltDenId="000000";
                    $newBundle->denName="رصيد مفتوح";
                    $newBundle->denAmount=0;
                    $newBundle->Cost=0;
                    $newBundle->denQty="";

                    $item->bundles=collect($item->bundles)->prepend($newBundle);
                    //$item->bundles[]=$newBundle;
                }else{
                    if(count($item->bundles) && !isset(collect($item->bundles)->first()->denId) && !$item->allowOpenAmount){
                        //abort(response()->json( $item));
                        if(is_null($billPaymentItem->additional_fields)){
                            $billPaymentItem->delete();
                            continue;
                        }
                    }
                }

                // $billPaymentBundles=BillPaymentBundle::where('bill_payment_item_id',$billPaymentItem->id)
                // ->get();

                $notValidateBundles=[];
                foreach( $item->bundles as &$bundle){
                    if(isset($bundle->denId)){
                        $billPaymentBundle = $billPaymentItem->bundles?->firstWhere(function ($item) use ($bundle) {
                            return $item->payload->denId == $bundle->denId;
                        });
                       // $price = Price::where('id',$bundle->denId)->first();

                        $amount=max($bundle->Cost,$bundle->denAmount);
                        if($amount==$bundle->denAmount){
                            // if($item->amountCurrency!='YER'){
                            //     $notValidateBundles[]=$bundle->denId;
                            //     continue;
                            // }
                            $bundle->currency=$item->amountCurrency;
                        }else if($amount==$bundle->Cost){
                            // if($item->costCurrency!='YER'){
                            //     $notValidateBundles[]=$bundle->denId;
                            //     continue;
                            // }
                            $bundle->currency=$item->costCurrency;
                        }
                        if(!isset($bundle->currency)){
                            continue;
                        }
                        if(isset($bundle->denDesc)){
                            $bundle->denDesc=$this->getDesc($bundle->denName,$bundle->denDesc);
                        }

                        if(is_null($billPaymentBundle)){
                            $billPaymentBundle =BillPaymentBundle::create([
                                'bill_payment_item_id'=>$billPaymentItem->id,
                                'amount'=>[
                                    'amount'=>$amount,
                                    'currency'=>$bundle->currency
                                ],
                                'title'=>NameData::from([
                                    "ar"=>$bundle->denName,
                                    "en"=>$bundle->denName,
                                ])->toArray(),
                                'status'=>1,
                                'payload'=>$bundle,
                            ]);
                            if(isset($bundle->denFilter)){
                                $this->handleBundleFilter($billPaymentItem,$billPaymentBundle,"denFilter",json_decode($bundle->denFilter));
                            }
                        }else{
                            $billPaymentBundle->amount=[
                                'amount'=>$amount,
                                'currency'=>$bundle->currency
                            ];
                            $billPaymentBundle->payload=$bundle;
                            $billPaymentBundle->save();
                        }

                    }
                }
                $bls=collect($item->bundles)->pluck('denId')->toArray();
                BillPaymentBundle::where(function($query) use($bls,$notValidateBundles){
                    if(!is_null($bls) && count($bls)){
                        $query->whereNotIn('payload->denId',$bls);
                    }
                    if(!empty($notValidateBundles)){
                        $query->orWhereIn('payload->denId',$notValidateBundles);
                    }
                })->where('bill_payment_item_id',$billPaymentItem->id)->delete();

                $billPaymentItem->fields=$this->buildFieldsItems($billPaymentItem->fields?->toCollection()??collect([]),$service,$item);
                $billPaymentItem->save();
            }

            $billPaymentService->fields=$this->buildFieldsService($billPaymentService->fields?->toCollection()??collect([]),$service,collect($service->scItems)->first());
            $billPaymentService->save();
        }

        BillPaymentService::withCacheUpdate();
    }

    protected function handleItemFilter(?BillPaymentService $billPaymentService=null,?BillPaymentItem $billPaymentItem=null,$key=null,$payload=null): void
    {
        $filters = $billPaymentService->filters->filter(function ($filter) use($key,$payload) {
            return collect($filter->options)->contains(function ($option) use($key,$payload) {
                return isset($option->payload->{"$key"}) && in_array($payload, $option->payload->{"$key"});
            });
        });

        $filterItems=[];
        foreach($filters as $filter){
            $options = $filter->options->filter(function ($option) use($key,$payload) {
                return isset($option->payload->{"$key"}) && in_array($payload, $option->payload->{"$key"});
            });
            foreach ($options as $option) {
                $filterItems[]=[
                    "model_type"=>BillPaymentItem::class,
                    "model_id"  =>$billPaymentItem->id,
                    "bill_payment_filter_option_id"=>$option->id,
                    "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                ];
            }

        }
        if(!empty($filterItems)){
            collect($filterItems)->chunk(100)
            ->each(function ($chunked) {
                BillPaymentModelFilterOption::insert($chunked->values()->toArray());
            });
        }
    }

    protected function handleBundleFilter(?BillPaymentItem $billPaymentItem=null,?BillPaymentBundle $billPaymentBundle=null,$key=null,$payload=null): void
    {
        $filters = $billPaymentItem->filters->filter(function ($filter) use($key,$payload) {
            return collect($filter->options)->contains(function ($option) use($key,$payload) {
                if( is_array($payload)){
                    return (isset($option->payload->{"$key"}) && count(array_intersect($payload, $option->payload->{"$key"})) > 0);

                    // return collect($payload)->contains(function ($p) use($key,$option) {
                    //     return isset($option->payload->{"$key"}) && in_array($p, $option->payload->{"$key"});
                    // });
                }else{
                    return isset($option->payload->{"$key"}) && in_array($payload, $option->payload->{"$key"});
                }
            });
        });
        $filterItems=[];
        foreach($filters as $filter){
            $options = $filter->options->filter(function ($option) use($key,$payload) {
                if( is_array($payload)){
                    return (isset($option->payload->{"$key"}) && count(array_intersect($payload, $option->payload->{"$key"})) > 0);
                    // return collect($payload)->contains(function ($p) use($key,$option) {
                    //     return isset($option->payload->{"$key"}) && in_array($p, $option->payload->{"$key"});
                    // });
                }else{
                    return isset($option->payload->{"$key"}) && in_array($payload, $option->payload->{"$key"});
                }
            });
            foreach ($options as $option) {
                $filterItems[]=[
                    "model_type"=>BillPaymentBundle::class,
                    "model_id"  =>$billPaymentBundle->id,
                    "bill_payment_filter_option_id"=>$option->id,
                    "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                ];
            }

        }
        if(!empty($filterItems)){
            collect($filterItems)->chunk(100)
            ->each(function ($chunked) {
                BillPaymentModelFilterOption::insert($chunked->values()->toArray());
            });
        }
    }

    protected function getDesc(?string $name,?string $desc) {
        $keys=[
            "per"=>"المده",
            "min"=>"دقائق داخل الشبكة",
            "minOut"=>"دقائق خارج الشبكة",
            "sms"=>"الرسائل",
            "data"=>"البيانات",
            "type"=>"النوع",
       ];
       if(!is_null($desc)){
           $data=collect(json_decode($desc))
           ->only(collect( $keys)->keys()->all())->map(function ($value,$key) use($keys){
               return "- ".$keys[$key].": $value";
           })->all();
           return //"$name\n".
            join("\n", $data);
       }
       return null;

    }
    protected function getActionCode(&$service) {
        $serviceCode=BillPaymentServiceCodeEnum::findByValue($service->sc);
        switch ($serviceCode) {
            case BillPaymentServiceCodeEnum::yemenMobile:
                $service->ac=['4006','4007'];
                 break;
            case BillPaymentServiceCodeEnum::electricityCommercial:
            case BillPaymentServiceCodeEnum::adsl:
            case BillPaymentServiceCodeEnum::landPhone:
            case BillPaymentServiceCodeEnum::waterUtility:
            case BillPaymentServiceCodeEnum::yemen4G:
            case BillPaymentServiceCodeEnum::electricity:
            case BillPaymentServiceCodeEnum::government:
            case BillPaymentServiceCodeEnum::education:
                $service->ac=['4001'];
                 break;
            case BillPaymentServiceCodeEnum::internetCards:
                $service->ac=['4008'];
                break;
        }
        return $service;
    }
    public function getAdditionalFields($service,$item):?array
    {
        $serviceCode=BillPaymentServiceCodeEnum::findByValue($service->sc);
        switch ($serviceCode) {
            case BillPaymentServiceCodeEnum::government:
                if(in_array($item->item,[1244])){
                    return ['extra.billno'];
                }
                break;
            default:
                return null;
        }
        return null;
    }
    protected function buildFieldsService(?Collection $fields,$service,$item) {
        $serviceCode=BillPaymentServiceCodeEnum::findByValue($service->sc);
        $fields=$this->modifyField( $fields,$serviceCode,'service',null,[$this, 'serviceField']);

        switch ($serviceCode) {
            case BillPaymentServiceCodeEnum::electricityCommercial:
            case BillPaymentServiceCodeEnum::internetCards:
            case BillPaymentServiceCodeEnum::education:
                $fields=$this->modifyField( $fields,$serviceCode,'item',null,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'amount:amount',$item,[$this, 'amountField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField']);
                break;
            case BillPaymentServiceCodeEnum::yemenMobile:
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'item',null,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'amount:amount',$item,[$this, 'amountField']);
                $fields=$this->modifyField( $fields,$serviceCode,'payload:lend',null,[$this, 'lendField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField']);
                break;
            case BillPaymentServiceCodeEnum::sabafone:
            case BillPaymentServiceCodeEnum::you:
            case BillPaymentServiceCodeEnum::y:
            case BillPaymentServiceCodeEnum::adsl:
            case BillPaymentServiceCodeEnum::landPhone:
            case BillPaymentServiceCodeEnum::waterUtility:
            case BillPaymentServiceCodeEnum::wallet:
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'item',null,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'amount:amount',$item,[$this, 'amountField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField']);
                break;
            case BillPaymentServiceCodeEnum::yemen4G:
            case BillPaymentServiceCodeEnum::adenNet:
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'item',$item,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField']);
                break;
            case BillPaymentServiceCodeEnum::games:
                $fields=$this->modifyField( $fields,$serviceCode,'item',$item,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField']);
                break;
            case BillPaymentServiceCodeEnum::electricity:
                $fields=$this->modifyField( $fields,$serviceCode,'item',null,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'extra:sac',null,[$this, 'sacField']);
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'amount:amount',$item,[$this, 'amountField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField']);
                break;
            case BillPaymentServiceCodeEnum::government:
                $fields=$this->modifyField( $fields,$serviceCode,'item',null,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'extra:sac',null,[$this, 'sacField']);
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'amount:amount',$item,[$this, 'amountField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField'],);
                break;
            default:
                $fields=$this->modifyField( $fields,$serviceCode,'item',null,[$this, 'itemField']);
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',null,[$this, 'subscriberField']);
                $fields=$this->modifyField( $fields,$serviceCode,'amount:amount',$item,[$this, 'amountField']);
                $fields=$this->modifyField( $fields,$serviceCode,'remarks',null,[$this, 'noteField']);
                break;
        }
        $fields=$this->modifyField(  $fields,$serviceCode,'amount:currency',null,[$this, 'currencyField']);
        $fields=$this->modifyField( $fields,$serviceCode,'payload:biller',null,[$this, 'billerField']);
        $fields=$this->modifyField( $fields,$serviceCode,'payload:billerCategory',null,[$this, 'billerCategoryField']);
        $fields=$this->modifyField( $fields,$serviceCode,'action',null,[$this, 'actionField']);

        return FormBaseData::collect($fields,BaseNonNullableDataCollection::class);
    }
    protected function buildFieldsItems(?Collection $fields,$service,$item) {
        $serviceCode=BillPaymentServiceCodeEnum::findByValue($service->sc);
        switch ($serviceCode) {
            case BillPaymentServiceCodeEnum::yemenMobile:
                if(in_array($item->item,[12,1129])){
                    $fields=$this->modifyField( $fields,$serviceCode,'payload:lend',$item,[$this, 'lendField']);
                }
                break;
            case BillPaymentServiceCodeEnum::internetCards:
                if(in_array($item->item,[1083,1195,2414])){
                    $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',$item,[$this, 'subscriberField']);
                }
                break;
            case BillPaymentServiceCodeEnum::electricity:
            case BillPaymentServiceCodeEnum::government:
                if(str_contains($item->extraFieLd,'sac')){
                    $fields=$this->modifyField( $fields,$serviceCode,'extra:sac',$item,[$this, 'sacField']);
                }
                $fields=$this->modifyField( $fields,$serviceCode,'subscriberNumber',$item,[$this, 'subscriberField']);

                break;
            default:
                break;
        }
        if(isset($item->allowOpenAmount) && $item->allowOpenAmount==true){
            $fields=$this->modifyField( $fields,$serviceCode,'amount:amount',$item,[$this, 'amountField']);
        }
        $fields=$this->modifyField(  $fields,$serviceCode,'amount:currency',$item,[$this, 'currencyField']);
        $fields=$this->modifyField(  $fields,$serviceCode,'payload:biller',$item,[$this, 'billerField']);
        $fields=$this->modifyField( $fields,$serviceCode,'payload:billerCategory',$item,[$this, 'billerCategoryField']);

        return FormBaseData::collect($fields,BaseNonNullableDataCollection::class);

    }
     /**
     * Execute the given callback without recording Telescope entries.
     *
     * @param  Collection  $fields
     * @param  BillPaymentServiceCodeEnum  $serviceCode
     * @param  string  $fieldName
     * @param  mixed  $item
     * @param  (callable(): FormBaseData)  $callback
     * @return mixed
     */
    public function modifyField(Collection $fields,BillPaymentServiceCodeEnum $serviceCode,$fieldName,$item,$callback)
    {
        $field = $fields->firstWhere('name', $fieldName);

        if ($field) {
            // ✅ Found: Update by creating a new collection with the updated item
            $fields = $fields->map(function ($field) use ($serviceCode,$fieldName,$callback,$item,$fields) {
                if(is_null($field)){
                    return abort(response()->json($fields));
                }
                if ($field->name === $fieldName) {
                    // Replace with updated value
                    return $callback($fieldName,$serviceCode,$item);
                   // return $field;
                    // return new FormFieldData(
                    //     name: $item->name,
                    //     value: 44 // or your new value
                    // );
                }
                return $field;
            });
        } else {
            // ❌ Not Found: Add new item
            $field= $callback($fieldName,$serviceCode,$item);
            $fields = $fields->push($field);
        }
        return $fields;
    }


    public function serviceField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {
        return new FormFieldData(
            name:$fieldName,
            element:'hidden',
            value: $serviceCode->value
        );
    }
    public  function subscriberField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):FormBaseData
    {
        $field=new FormFieldData(
            label:new NameData(
                ar:Lang::get("Subscriber number", locale: 'ar'),
                en:"Subscriber number"
            ),
            name:$fieldName,
            element:"phone",
            type:"phone",
            value:"",
            helper:new FormGeneralData(
                maxLength:9,
                extra:null,
            ),

        );
        $validations=[
            FormValidationData::requiredValidation(),
        ];
        $validation=new FormValidationData(
            regex: "",
            message: new NameData(
                ar:Lang::get("This field must be in correct format", locale: 'ar'),
                en:"This field must be in correct format"
            ),
            type: "full"
        );
        switch ($serviceCode) {
            case BillPaymentServiceCodeEnum::sabafone:
                $validation->regex= "(^71)\d{7}$";
                $field->helper->extra=["c","f"];
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a mobile number", locale: 'ar'),
                    en:"Subscriber number is a mobile number"
                );
                break;
            case BillPaymentServiceCodeEnum::you:
                $validation->regex= "(^73)\d{7}$";
                $field->helper->extra=["c","f"];
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a mobile number", locale: 'ar'),
                    en:"Subscriber number is a mobile number"
                );
                break;
            case BillPaymentServiceCodeEnum::yemenMobile:
                $validation->regex= "(^77|^78)\d{7}$";
                $field->helper->extra=["b","c","f"];
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a mobile number", locale: 'ar'),
                    en:"Subscriber number is a mobile number"
                );
                break;
            case BillPaymentServiceCodeEnum::y:
                $validation->regex= "(^70)\d{7}$";
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a mobile number", locale: 'ar'),
                    en:"Subscriber number is a mobile number"
                );
                break;
            case BillPaymentServiceCodeEnum::adsl:
            case BillPaymentServiceCodeEnum::landPhone:
                $validation->regex= "(^0)\d{7}$";
                $field->helper->extra=["b","c","f"];
                $field->helper->maxLength=8;
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a phone number", locale: 'ar'),
                    en:"Subscriber number is a phone number"
                );
                break;
            case BillPaymentServiceCodeEnum::yemen4G:
                $validation->regex= "(^1)\d{8}$";
                $field->helper->extra=["b","c","f"];
                break;
            case BillPaymentServiceCodeEnum::wallet:
                $validation->regex= "(^77|^78|^71|^73|^70)\d{7}$";
                $field->helper->extra=["c","f"];
                break;
            case BillPaymentServiceCodeEnum::games:
                $validation->regex= "(^77|^78|^71|^73|^70)\d{7}$";
                $field->helper->extra=["c","f"];
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a mobile number, you will get the service code to your mobile as sms message.", locale: 'ar'),
                    en:"Subscriber number is a mobile number, you will get the service code to your mobile as sms message."
                );
                break;
            case BillPaymentServiceCodeEnum::internetCards:
                if(in_array($item?->item??0,[1083])){
                    $validation->regex= "^(?:519685)[0-9]{10}$";
                    $validation->message= new NameData(
                        ar:Lang::get("Required a valid card pattern", locale: 'ar'),
                        en:"Required a valid card pattern"
                    );
                    $field->hint=new NameData(
                        ar:"5196 85__ ____ ____",
                        en:"5196 85__ ____ ____"
                    );
                }else if(in_array($item?->item??0,[1195])){
                    $validation->regex= "^(?:9967000)[0-9]{9}$";
                    $validation->message= new NameData(
                        ar:Lang::get("Required a valid card pattern", locale: 'ar'),
                        en:"Required a valid card pattern"
                    );
                    $field->hint=new NameData(
                        ar:"9967 000_ ____ ____",
                        en:"9967 000_ ____ ____"
                    );
                    //abort(400);
                }else if(in_array($item?->item??0,[2414])){
                    // $validations[]= [
                    //     "regex"=> "^(?:519685)[0-9]{10}$",
                    //     "message"=> "Required a valid card pattern",
                    //     "type"=> "full"
                    // ];
                    //  $field->hint=new NameData(
                    //     ar:"9967 000_ ____ ____",
                    //     en:"9967 000_ ____ ____"
                    // );
                }
                $field->label=new NameData(
                    ar:Lang::get("Card number", locale: 'ar'),
                    en:"Card number"
                );
                $field->element="creditCard";
                $field->type="number";
                $field->helper= null;
                break;
            case BillPaymentServiceCodeEnum::waterUtility:
            case BillPaymentServiceCodeEnum::electricity:
                $field->helper->extra=["q","f"];
                $field->helper->maxLength=null;
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a counter number", locale: 'ar'),
                    en:"Subscriber number is a counter number"
                );
                break;
            case BillPaymentServiceCodeEnum::teleyemen:
            case BillPaymentServiceCodeEnum::adenNet:
                $field->helper->extra=["f"];
                $field->element="input";
                $field->type="number";
                break;
            case BillPaymentServiceCodeEnum::electricityCommercial:
                $field->helper->extra=["q","f"];
                $field->helper->maxLength=null;
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is a counter/account number, if you query by phone number you need to select the counter number from bill details", locale: 'ar'),
                    en:"Subscriber number is a counter/account number, if you query by phone number you need to select the counter number from bill details"
                );
                break;
            case BillPaymentServiceCodeEnum::government:
                if(in_array($item?->item??0,[1244])){
                    $field->element="pairInput";
                    $field->helper->extra=["q","f"];
                    $field->helper->maxLength=null;
                    $field->label=new NameData(
                        ar:Lang::get("Car plate number", locale: 'ar'),
                        en:"Car plate number"
                    );
                    $field->helper->label=new NameData(
                        ar:Lang::get("Region", locale: 'ar'),
                        en:"Region"
                    );
                    $field->hint=new NameData(
                        ar:Lang::get("Yemen", locale: 'ar'),
                        en:"Yemen"
                    );
                }else{
                    $field->label=new NameData(
                        ar:Lang::get("رقم الحافظه", locale: 'ar'),
                        en:"رقم الحافظه"
                    );
                    $field->helper->extra=["q","f"];
                    $field->helper->maxLength=null;
                }

                break;
            case BillPaymentServiceCodeEnum::education:
                $field->helper->extra=["q","f"];
                $field->helper->maxLength=null;
                $field->hint=new NameData(
                    ar:Lang::get("Subscriber number is the student number", locale: 'ar'),
                    en:"Subscriber number is the student number"
                );
                break;
            default:
                $field->helper->extra=["f"];
                $field->element="input";
                $field->type="number";
                break;
        }
        if(!empty($validation->regex)){
            $validations[]=$validation;
        }
        $field->validations=FormValidationData::collect($validations,BaseNonNullableDataCollection::class);
        return $field;
    }

    public  function itemField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):FormBaseData
    {

        $field=new FormFieldData(
            label: new NameData(
                ar:Lang::get("Type", locale: 'ar'),
                en:"Type"
            ),
            name:$fieldName,
            element:"select"
        );

        // if(is_null($item)){
        //     return $field;
        // }

        $validations=[
            FormValidationData::requiredValidation(),
        ];

        switch ($serviceCode) {
            case BillPaymentServiceCodeEnum::electricityCommercial:
                $field->label=new NameData(
                    ar:Lang::get("Power station", locale: 'ar'),
                    en:"Power station"
                );
                break;
            case BillPaymentServiceCodeEnum::education:
                $field->label=new NameData(
                    ar:Lang::get("University & Institution", locale: 'ar'),
                    en:"University & Institution"
                );
                break;
            case BillPaymentServiceCodeEnum::yemen4G:
                $field->label=null;
                $field->element="filter";
                break;
            case BillPaymentServiceCodeEnum::games:
            case BillPaymentServiceCodeEnum::government:
                $field->label=null;
                $field->element="grid";
                break;
            default:
                $field->label=null;
                $field->element="filter";
                break;
        }
        $field->validations=FormValidationData::collect($validations,BaseNonNullableDataCollection::class);
        return $field;
    }
    public function billerCategoryField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {
        $field=new FormFieldData(
            name:$fieldName,
            element:'hidden'
        );

        if(is_null($item)){
            return $field;
        }
        $field->value=$this->billerCategories[$item->externalCode??""]??null;
        return $field;
    }
    public function billerField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {
        $field=new FormFieldData(
            name:$fieldName,
            element:'hidden'
        );

        if(is_null($item)){
            return $field;
        }
        $field->value=$item->externalCode??null;
        return $field;
    }
    public function currencyField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {
        $field=new FormFieldData(
            name:$fieldName,
            element:'hidden'
        );

        if(is_null($item)){
            return $field;
        }
        $field->value=$item->currency??$item->amountCurrency;
        return $field;
    }
    public  function sacField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {

        $field=new FormFieldData(
            label:new NameData(
                ar:Lang::get("Region", locale: 'ar'),
                en:"Region"
            ),
            name:$fieldName,
            element:"select"
        );

        if(is_null($item)){
            return $field;
        }

        $validations=[
            FormValidationData::requiredValidation(),
        ];
        if(isset($item->extraFieLd) &&
            is_string($item->extraFieLd)
        ){
            $extraField=collect(json_decode($item->extraFieLd))->where('key','sac')->first();
            if(!is_null($extraField)){
                $field->label=new NameData(
                    ar:"$extraField->hin",
                    en:"$extraField->hin"
                );
                $field->name="extra:{$extraField->key}";
                $field->value=collect($extraField->cvs)->first()->code;
                $field->options=FormOptionData::collect(collect($extraField->cvs)->map(function($cvs){
                    return new FormOptionData(
                        id:$cvs->code,
                        name:new NameData(
                            ar:$cvs->name,
                            en:$cvs->name
                        )
                    );
                }),BaseNonNullableDataCollection::class);
                $field->validations=FormValidationData::collect($validations,BaseNonNullableDataCollection::class);
                return $field;
            }
        }
        return null;
    }
    public  function lendField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {

        $field=new FormFieldData(
            label:new NameData(
                ar:Lang::get("Lend", locale: 'ar'),
                en:"Lend"
            ),
            name:$fieldName,
            element:"select"
        );

        if(is_null($item)){
            return $field;
        }

        $validations=[
            FormValidationData::requiredValidation(),
        ];
        $field->name="payload:lend";
        $field->value="0";
        $field->options=FormOptionData::collect([
            new FormOptionData(
                id:"0",
                name:new NameData(
                    ar:Lang::get("Without lend", locale: 'ar'),
                    en:"Without lend"
                )
            ),
            new FormOptionData(
                id:"100",
                name:new NameData(
                    ar:Lang::get("Include lend 100 YER", locale: 'ar'),
                    en:"Include lend 100 YER"
                )
            ),
            new FormOptionData(
                id:"200",
                name:new NameData(
                    ar:Lang::get("Include lend 200 YER", locale: 'ar'),
                    en:"Include lend 200 YER"
                )
            ),
        ],BaseNonNullableDataCollection::class);
        $field->validations=FormValidationData::collect($validations,BaseNonNullableDataCollection::class);
        return $field;
    }
    public  function amountField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item)
    {
        // if(is_null($item)){
        //     abort(response()->json(["a"=>$serviceCode->name]));
        // }
        $field=new FormFieldData(
            label:new NameData(
                ar:Lang::get("Amount", locale: 'ar'),
                en:"Amount"
            ),
            name:$fieldName,
            element:'amount',
            type: 'number',
            value: "",
            helper: new FormGeneralData(
                leading:new FormGeneralData(
                    id: "",
                    name: "amount:currency",
                    value: $item->currency??$item->amountCurrency
                )
            ),
        );
        $validations=[
            FormValidationData::requiredValidation(),
        ];

        $rangeValidation=FormValidationData::rangeValidation($item->minAmount,$item->maxAmount);
        if(!is_null($rangeValidation)){
            $validations[]=$rangeValidation;
        }


        /*if(!is_null($item)){
            $bundle=collect($item->bundles)->first();
            if(isset($bundle->denAmount) && $bundle->denAmount==0 && isset($item->feeType) && isset($item->Cost)){
                if($serviceCode==BillPaymentServiceCodeEnum::sabafone->value && $item->Cost>1){
                    $label=new NameData(
                        ar:Lang::get("Unit", locale: 'ar'),
                        en:"Unit"
                    );
                }
                //$hint=null;
                $feeAmount=$item->Cost;
                if(isset($item->feeAmt)){
                    $feeAmount=$item->feeAmt;
                }

                $field->helper->label=$item->feeType=="A" || $item->Cost==1?new NameData(
                    ar:Lang::get("Fee", locale: 'ar'),
                    en:"Fee"
                ):$field->helper->label;
                $field->helper->name="";
                $field->helper->value=$item->feeType=="A"?$item->feeAmt:0;

                $field->helper->extra=$item->feeType=="A" || (!isset($item->feeAmt) && $item->Cost==1)?0:$feeAmount;
                $field->helper->minValue=$item->feeType=="A"?$item->feeAmt:0;
                $field->options=FormOptionData::collect([
                    new FormOptionData(
                        id:$item->feeType=="A" || (!isset($item->feeAmt) && $item->Cost==1)?"1":"2",
                        name:new NameData(
                            ar:'',
                            en:''
                        ),
                        selected:true
                    )
                ],BaseNonNullableDataCollection::class);
            }
        }*/
        $field->validations=FormValidationData::collect($validations,BaseNonNullableDataCollection::class);
        return $field;
    }
    public   function noteField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {
        return new FormFieldData(
            label:new NameData(
                ar:Lang::get("Note", locale: 'ar'),
                en:"Note"
            ),
            name:$fieldName,
            element:'input',
            type: 'multiline',
            value: "",
            helper: new FormGeneralData(
                maxLength:80
            )
        );
    }


    public function actionField(string $fieldName,BillPaymentServiceCodeEnum $serviceCode,$item):?FormBaseData
    {
        return new FormFieldData(
            label:new NameData(
                ar:Lang::get("متابعة", locale: 'ar'),
                en:"Continue"
            ),
            name:$fieldName,
            element:'action',
            value: "continue"
        );
    }
}
