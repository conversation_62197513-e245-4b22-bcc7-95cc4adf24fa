<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralItemData;
use Spatie\LaravelData\Attributes\MapInputName;


class BillPaymentQueryResponseItemData extends BaseNonNullableData
{
    public function __construct(
        public ?string $backgroundColor=null,
        public ?string $foregroundColor=null,
        public ?BillPaymentQueryResponseSubitemItemData $item=null,
        public ?BillPaymentQueryResponseSubitemItemData $subitem=null,
        //public ?string $type,
    ) {
    }

}
