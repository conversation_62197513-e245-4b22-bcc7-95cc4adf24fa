<?php

namespace App\Data\OBDX\Transfer;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\MapInputName;


class TransferData extends BaseNonNullableData
{
    public function __construct(
        public ?string $id,
        #[MapInputName('reference_id')]
        public ?string $referenceId,
        public ?AccountIdData $debitAccountId,
        public ?CurrencyAmountData $amount,
        public ?CurrencyAmountData $fee,
        public ?string $receiverNumber,
        public ?string $receiverName,
        public ?string $bankCode,
        public ?string $service,
        public ?string $remarks,
        public ?string $type,
        public ?int $status,
        #[MapInputName('created_at')]
        public ?string $date
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['debitAccountId']= data_get($properties,"payload.debitAccountId");
        $properties['amount']= data_get($properties,"payload.amount");
        $properties['fee']=data_get($properties,"payload.fee");
        $properties['receiverNumber']=data_get($properties,"payload.receiverNumber",);
        $properties['receiverName']=data_get($properties,"payload.receiverName");
        $properties['bankCode']=data_get($properties,key: "payload.bankCode");
        $properties['service']=data_get($properties,key: "payload.service");
        $properties['remarks']=data_get($properties,key: "payload.remarks");

        return $properties;
    }

    // public function receipt(): array
    // {
    //     $dataArray = $this->toArray();
    //     $items=[];
    //     foreach ($dataArray as $key=>$value) {
    //         switch($key){
    //             case "debitAccountId":
    //                 $subtitle=$value['value'];
    //                 break;
    //             case "amount":
    //             case "fee":
    //                 $subtitle=number_format($value['amount'], 2)." ".$value['currency'];
    //                 break;
    //             default:
    //                 $subtitle=$value;
    //         }
    //         $items[]=[
    //             "title"=>__(ucwords(preg_replace('/(?<!^)[A-Z]/', ' $0', $key))),
    //             "subtitle"=>$subtitle,
    //         ];
    //     }
    //     return $items;
    // }
}
