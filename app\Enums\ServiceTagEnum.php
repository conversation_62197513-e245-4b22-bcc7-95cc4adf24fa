<?php

namespace App\Enums;
enum ServiceTagEnum: string {
    case FREQUENT_PAYMENTS = 'FREQUENT_PAYMENTS';
    case FAVORITES = 'FAVORITES';
    case GROUPS = 'GROUPS';
    case FACEBOOK_PAGE = 'FACEBOOK_PAGE';
    case CUSTOMER_SERVICE = 'CUSTOMER_SERVICE';
    case SALLY_ASSISTANT = 'SALLY_ASSISTANT';
    case OUR_LOCATIONS = 'OUR_LOCATIONS';
    //case GOLD_PLATFORM2 = 'GOLD_PLATFORM2';
    case GOLD_PLATFORM = 'GOLD_PLATFORM';
    case PNPL = 'PNPL';
    case BAS = 'BAS';
    case GIFT = 'GIFT';
    case WASIL = 'WASIL';
    case CARDLESS = 'CARDLESS';
    case CLAIM_MONEY = 'CLAIM_MONEY';
    case COMMERCIAL_ELECTRICITY = 'COMMERCIAL_ELECTRICITY';
    case MUASALAT_WALLET = 'MUASALAT_WALLET';
    case FLOOSAK = 'FLOOSAK';
    case DOMESTIC = 'DOMESTIC';
    case JAWALI_W = 'JAWALI_W';
    case ONECASH_W = 'ONECASH_W';
    case JAIB_W = 'JAIB_W';
    case SBYB_B = 'SBYB_B';
    case SIB_B = 'SIB_B';
    case YBRD_B = 'YBRD_B';
    case TIIB_B = 'TIIB_B';
    case MHFDTI_W = 'MHFDTI_W';
    case CARD_MANAGMENT = 'CARD_MANAGMENT';
    case MASTERCARD = 'MASTERCARD';
    case MOBILE = 'MOBILE';
    case YEMEN_4G = 'YEMEN_4G';
    case ADSL = 'ADSL';
    case LANDLINE = 'LANDLINE';
    case GAMES = 'GAMES';
    case MUASALAT_CARD = 'MUASALAT_CARD';
    case WATER = 'WATER';
    case ELECTRICYBOARD = 'ELECTRICYBOARD';
    case OPEN_ACCOUNT = 'OPEN_ACCOUNT';
    case ELOAN = 'ELOAN';
    case MY_ACCOUNTS_TRANSFER = 'MY_ACCOUNTS_TRANSFER';
    case MONEY_TRANSFER = 'MONEY_TRANSFER';
    case YEAH_MONEY = 'YEAH_MONEY';
    case CARDLESS_TRANSFER = 'CARDLESS_TRANSFER';
    case GROUP_TRANSFER = 'GROUP_TRANSFER';
    case REPEAT_TRANSFER = 'REPEAT_TRANSFER';
    case REQUEST_MONEY = 'REQUEST_MONEY';
    case QR_SCANNER = 'QR_SCANNER';
    case SHARING_ACCOUNT = 'SHARING_ACCOUNT';
    case TRANSACTIONS = 'TRANSACTIONS';

    case YKP_PAYMENT = 'YKP_PAYMENT';
    case OIL_PAYMENT = 'OIL_PAYMENT';

    case SPLIT_PAYMENT = 'SPLIT_PAYMENT';

}
