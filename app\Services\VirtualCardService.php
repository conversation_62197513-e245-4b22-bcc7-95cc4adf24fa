<?php

namespace App\Services;
use App\Data\AccountIdData;
use App\Data\GeneralResponseData;
use App\Data\TokenData;

use App\Data\VirtualCardData;
use Illuminate\Support\Facades\Http;

use App;
use App\Models\User;
use Spatie\LaravelData\DataCollection;

/**
 * Service to create and update orders
 */
class VirtualCardService
{
    protected $settings;
    protected $isTest=false;

    /**
     * Service to create and update orders
     */
    public function __construct()
    {
        //$this->updateUrl();
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->virtualCard;
        if($this->settings->is_test){
            $this->isTest=true;
            $this->settings=$this->settings->test_data;
        }
    }
    // public function updateUrl()
    // {
    //     $settings=app(\App\Settings\ThirdPartySettings::class);
    //     //\Log::info($settings);
    //     $virtualCard=$settings->virtualCard;
    //     $test_data=$virtualCard->test_data;
    //     $test_data->url='http://**************:9443/ords/ykb/master';
    //     $virtualCard->test_data=$test_data;
    //     $virtualCard->url='https://cardvip.yk-bank.com:9876/ords/api/card';
    //     $settings->virtualCard=$virtualCard;
    //     $settings->save();
    // }
    function str_lreplace($search, $replace, $subject)
    {
        $pos = strrpos($subject, $search);

        if($pos !== false)
        {
            $subject = substr_replace($subject, $replace, $pos, strlen($search));
        }

        return $subject;
    }
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }

        $url=$this->str_lreplace("/card","",$this->str_lreplace("/master","",$this->settings->url));
        if(is_null($token->access_token)||empty($token->access_token)){
            $response = rescue(function () use($url){
                return  Http::timeout(2)
                ->withBasicAuth($this->settings->client_id, $this->settings->client_secret)
                ->asForm()
                ->post("{$url}/oauth/token",[
                    "grant_type"=>"client_credentials"
                ]);
            }, function ($e) {
                return $e->getMessage();
            });

            if(is_string($response)){
                return $response;
            }

            if ($response->failed()) {
                return $response->status();
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->access_token)){
                $this->settings->token=TokenData::from($response);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $virtualCard=$settings->virtualCard;
                if($virtualCard->is_test){
                    $test_data=$virtualCard->test_data;
                    $test_data->token=$this->settings->token;
                    $virtualCard->test_data=$test_data;
                }else{
                    $virtualCard->token=$this->settings->token;
                }
                $settings->virtualCard=$virtualCard;
                $settings->save();
            }
           // dd($response);

        }
        return $status;
    }

    public static function getCards($request) {

        $virtualCardService=new self();
        if(env('APP_ENV', 'production')=='production' && $virtualCardService->isTest){
            return GeneralResponseData::from([
                'status' => [
                    "result" => "SUCCESSFUL",
                    "contextID" => "",
                    "message" => [
                        "code" => "0",
                        "type" => "INFO"
                    ]
                ]
            ]);
        }

        $status=$virtualCardService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]);
        }

        $params=[
            "cardProvider"=>"MC",
        ];
        $cards=[];
        if(!is_null($request->partyId) /*|| !is_null($request->phone)*/){
            if(!is_null($request->partyId)){
                $params["customerNo"]=$request->partyId;//"0145262",
            // }else if(!is_null($request->phone)){
            //     $params["msisdn"]=$request->phone;
            }

            $response = rescue(function () use($virtualCardService,$params){
                return Http::timeout(1)
                ->withToken($virtualCardService->settings->token->access_token)
                ->get("{$virtualCardService->settings->url}/cards",$params);
            }, function ($e) {
                return $e->getMessage();
            });


            if(!is_string($response)){
                if($response->status()==401){
                    $request->retry=($request->retry??-1)+1;
                    return static::getCards($request);
                }
               $result=$response->object();
               $cards=$result->data??[];

            }
        }


        return GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            'virtualCards'=>VirtualCardData::collect($cards,DataCollection::class)
        ]);


    }

}
