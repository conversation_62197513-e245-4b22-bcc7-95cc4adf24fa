<?php

namespace App\Providers;

use App\Guards\OBDXGuard;
use App\Models\User;
use Illuminate\Auth\RequestGuard;
use Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Lara<PERSON>\Passport\ClientRepository;
use Laravel\Passport\Passport;
use Illuminate\Support\Facades\Route;
use Laravel\Passport\TokenRepository;
use League\OAuth2\Server\ResourceServer;
class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        // if(env('DB_CONNECTION')=='oracle'){
        //     Passport::routes();
        // }
        Route::post('/oauth/token', [
            'uses' => '\App\Http\Controllers\Digx\AccessTokenController@issueToken',
            'as' => 'passport.token',
            'middleware' => 'throttle',
        ]);
        Route::post('/oauth/clients', [
            'uses' => '\App\Http\Controllers\Admin\ClientController@store',
            'as' => 'passport.clients.store',
            'middleware' => ['throttle','web'],
        ]);
        Route::put('/oauth/clients', [
            'uses' => '\App\Http\Controllers\Admin\ClientController@update',
            'as' => 'passport.clients.update',
            'middleware' => ['throttle','web'],
        ]);
        Passport::tokensExpireIn(now()->addDays(15));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::useClientModel(\App\Client::class);
        Gate::before(function ($user, $ability) {
            return $user->hasRole('developer') ? true : null;
        });
        Gate::define('thirdParty', function($user){
            return $user->hasRole('thirdParty');
        });
        // Gate::before(function ($user, $ability) {
        //     return $user->hasRole('developer') ? true : null;
        // });
        // Auth::resolved(function ($auth) {
        //     $auth->provider('online',function ($app,$config) {
        //         return new OBDXUserProvider(Auth::createUserProvider('remote'), 'remote');
        //         //return $result;
        //     })->extend('obdx', function ($app, $name, array $config) {
        //         return tap($this->makeGuard($config), function ($guard) {
        //             app()->refresh('request', $guard, 'setRequest');
        //         });
        //     });
        // });
        Auth::provider('online',function ($app,$config) {
            return new OBDXUserProvider($this->app['hash'], $config['model'], $app['request']);
            //return $result;
        })->extend('obdx', function ($app, $name, $config) {
            $guard = new RequestGuard(function ($request,$provider) use ($config) {
                return (new OBDXGuard(
                    $this->app->make(ResourceServer::class),
                    $provider,
                    $this->app->make(TokenRepository::class),
                ))->user($request);
            }, $this->app['request'], $app['auth']->createUserProvider('remote'));

            // $guard = new RequestGuard(function ($request,$provider)  {
            //     return $provider->retrieveById(1);
            // }, $app['request'], $app['auth']->createUserProvider('remote'));

            $app->refresh('request', $guard, 'setRequest');
            return $guard;
        });
        // \Auth::provider('online',function ($app,$config) {
        //     return new OBDXUserProvider($this->app['hash'], $config['model'], $app['request']);
        //     //return $result;
        // })->extend('obdx', function ($app, $name, $config) {
        //     $guard = new RequestGuard(function ($request,$provider)  {
        //         return $provider->retrieveById(1);
        //     }, $app['request'], $app['auth']->createUserProvider('remote'));

        //     $app->refresh('request', $guard, 'setRequest');
        //     return $guard;
        // });
    }

    /**
     * Make an instance of the token guard.
     *
     * @param  array  $config
     * @return \Illuminate\Auth\RequestGuard
     */
    protected function makeGuard(array $config)
    {
        return new RequestGuard(function ($request,$provider) use ($config) {
            return (new OBDXGuard(
                $this->app->make(ResourceServer::class),
                $provider,
                $this->app->make(TokenRepository::class),
                $this->app->make(ClientRepository::class),
                $this->app->make('encrypter')
            ))->user($request);
        }, $this->app['request'], $this->app['auth']->createUserProvider('remote'));
    }

}
