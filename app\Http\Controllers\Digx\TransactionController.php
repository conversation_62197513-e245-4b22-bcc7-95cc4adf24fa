<?php

namespace App\Http\Controllers\Digx;
use App\Data\GeneralResponseData;
use App\Data\ReceiptData;
use App\Services\OBDX\CustomerService;
use Illuminate\Http\Client\Response;

use App\Http\Controllers\Controller;
//use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Cookie;
use GuzzleHttp\Cookie\CookieJar;
use Validator;
use App\Services\FlexService;
class TransactionController extends Controller
{

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id,Request $request)
    {

        $item=CustomerService::transaction($request,$request->account,$id);
        if(is_null($item)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "RECEIPT-TRX",
                    "message"   => [
                        "title"   => __("This transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_TRX_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        if(($item instanceof GeneralResponseData)){
            abort(response()->json($item));
        }
        $isCredit=$item->transactionType=='C' || ($item->transactionType=='D' && ($item->amountInAccountCurrency->amount??0)<0);
        $title=$isCredit?__("Credit"):__("Debit");

        $this->generateReceipt( ReceiptData::from([
            "id"=> $item->key->subSequenceNumber,
            "date"=> date_format(date_create($item->transactionDate), "Y-m-d"),
            "title"=> __("Financial transaction").' - ('.$title.')',
            "beneficiary"=> $item->counterPartyAccountId??null,
            "statement"=> $item->description,
            "details"=> [
                "referenceId"=> $item->key->transactionReferenceNumber,
                "debitAccountId"=> $item->accountId??null,
                "amount"=>$item->amountInAccountCurrency,
            ]
        ]));
    }
    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function status($id,Request $request)
    {
        $object=new \stdClass();
        $object->trxId= $id;
        //$object->trxId= substr_replace($request->externalReferenceId, 'BKOP', 3, 4);

        $result=FlexService::checkTransactionStatus($object);
        if($result->status->message->code!="0"){
            return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }else{
            return response()->json($result);
        }
    }
}
