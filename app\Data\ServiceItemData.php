<?php

namespace App\Data;
use Illuminate\Support\Collection;

use Spa<PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spa<PERSON>\LaravelData\CursorPaginatedDataCollection;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Optional;
use Spatie\LaravelData\PaginatedDataCollection;
use Illuminate\Pagination\AbstractCursorPaginator;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Support\Enumerable;
use Illuminate\Support\LazyCollection;
use Illuminate\Contracts\Pagination\CursorPaginator as CursorPaginatorContract;
use Illuminate\Contracts\Pagination\Paginator as PaginatorContract;
use Illuminate\Validation\Rule;
use Spatie\LaravelData\Support\Validation\ValidationContext;
class ServiceItemData extends BaseNonNullableData
{

    public function __construct(
        public ?int $id,
        public ?string $tag,
        public ?string $type,
        public ?NameData $title,
        public ?NameData $description,
        public ?string $icon,
        public ?object $banner,
        public ?string $url,
        public ?string $help_url,
        public ?int $status,
        public ?string $status_type,
        public ?int $quick_access,
        public ?int $allow_more,
        public ?int $app_version,
        public ?array $os_types,
        public ?int $sort,
        public ?int $service_id,
        public ?int $trending_service,
        #[DataCollectionOf(ServiceItemData::class)]
        public ?DataCollection $items,
    ) {
    }
    // public function excludeProperties() : array
    // {
    //     return [
    //         'id'=>is_null($this->id),
    //         'tag'=>is_null($this->tag),
    //         'type'=>is_null($this->type),
    //         'title'=>is_null($this->title),
    //         'description'=>is_null($this->description),
    //         'icon'=>is_null($this->icon),
    //         'banner'=>is_null($this->banner),
    //         'url'=>is_null($this->url),
    //         'help_url'=>is_null($this->help_url),
    //         'status'=>is_null($this->status),
    //         'status_type'=>is_null($this->status_type),
    //         'quick_access'=>is_null($this->quick_access),
    //         'allow_more'=>is_null($this->allow_more),
    //         'app_version'=>is_null($this->app_version),
    //         'os_types'=>is_null($this->os_types),
    //         'sort'=>is_null($this->sort),
    //         'service_id'=>is_null($this->service_id),
    //         'trending_service'=>is_null($this->trending_service),
    //         'items'=>count($this->items??[]) <= 0,
    //     ];
    // }

    public static function prepareForPipeline(array $properties) : array
    {
        //$locale=app()->getLocale();
        //$properties['title']= data_get($properties,"title.$locale");
        $properties['status']= data_get($properties,"customerType.status",data_get($properties,"customer_type.status",data_get($properties,"service_customer_type.status")));
        $properties['status_type']= data_get($properties,"customerType.status_type",data_get($properties,"customer_type.status_type",data_get($properties,"service_customer_type.status_type")));

        return $properties;
    }

    // public static function rules(ValidationContext $context): array
    // {
    //     return [
    //         'icon' => [Rule::excludeIf(true)],
    //     ];
    // }
    public static function get(array|DataCollection|PaginatedDataCollection|CursorPaginatedDataCollection|Enumerable|AbstractPaginator|PaginatorContract|AbstractCursorPaginator|CursorPaginatorContract|LazyCollection|Collection $services ): array
    {
        $items=[];
        $services=$services->where('status','!=',null);
        foreach ($services as $service) {
            $items[]=$service->toHome();
        }
        return $items;
    }
    public function toHome() : array
    {

        $keys=[
            "id",
            "tag",
            "type",
            "title",
            "description",
            "icon",
            //"banner",
            "url",
            "help_url",
            "service_id",
            "status",
            "quick_access",
            "app_version"
        ];
        $locale=app()->getLocale();
        if((request()->header('appVersion')??0)<148){
            $item= collect($this->toArray())->only(...$keys)->toArray();
            $item["title"]=$item["title"][$locale]??null;
            $item["description"]=$item["description"][$locale]??null;
        }else if((request()->header('appVersion')??0)<172){
            $item= collect($this->toArray())->only(...$keys)->toArray();
            $item["description"]=$item["description"][$locale]??null;
        }else if((request()->header('appVersion')??0)<175){
            $keys[]="os_types";
            $keys[]="status_type";
            $item= collect($this->toArray())->only(...$keys)->toArray();
            $item["description"]=$item["description"][$locale]??null;
        }else if((request()->header('appVersion')??0)<181){
            $keys[]="os_types";
            $keys[]="status_type";
            $keys[]="allow_more";
            $item= collect($this->toArray())->only(...$keys)->toArray();
        }else {
            $keys[]="os_types";
            $keys[]="status_type";
            $keys[]="allow_more";
            $keys[]="trending_service";
            $keys[]="banner";
            $item= collect($this->toArray())->only(...$keys)->toArray();
        }

        if(!is_null($this->items) && !empty($this->items)){
            $item['items']=static::get($this->items);
        }

        return $item;
    }
}
