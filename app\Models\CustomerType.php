<?php

namespace App\Models;

use App\CacheModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerType extends CacheModel
{

    public const RETAIL=1;
    public const CORPORATE=2;
    public const AGENT=3;

    public const GUEST=4;

    public const UNAUTHENTICATED=5;
    public const BUSINESS=6;

    public const mapUserCategory=[
        "SMLE"=> CustomerType::BUSINESS,
        "MEDE"=> CustomerType::BUSINESS,
        "RT013"=> CustomerType::BUSINESS,
        "RT014"=> CustomerType::BUSINESS
    ];


    use HasFactory;
    protected $fillable = [
        "id",
        "name",
        "status",
        "topic",
        "roles"
    ];

    protected $hidden = [
        'created_at','updated_at'
    ];
    protected $casts = [
        'roles' => 'array',
    ];
    public function services()
    {
        return $this->hasManyThrough('App\Service','service_customer_types');
    }
    public static function getCorporateRoles(){
        return static::getWithCache("corporateRoles", function ($query) {
            return static::find(CustomerType::CORPORATE)?->roles;
        });
    }
}
