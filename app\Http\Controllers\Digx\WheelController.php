<?php

namespace App\Http\Controllers\Digx;
use App\Data\GeneralResponseData;
use App\Enums\TransactionStatusEnum;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessUsedWheel;
use App\LogItem;
use App\Models\WheelTransaction;
use App\Scopes\CustomerScope;
use DB;
use Illuminate\Http\Request;

class WheelController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $result=new \stdClass();
        $setting=app(\App\Settings\WheelSettings::class);
        if(!$setting->wheelConfig->allowSpinningWheel){
            abort(404);
        }
        $result->items= $setting->wheelItems->items->filter(function($item){
            return $item->status==1;
        })->only('id','name','type','image','color','weight');
        foreach ($result->items as &$item) {
            $item->weight=0;
        }
//DB::raw("SUBSTR(party_name, 1, INSTR(party_name, ' ') - 1) || ' ' || SUBSTR(party_name, INSTR(party_name, ' ', -1) + 1) AS name")

        $start=\Carbon\Carbon::now()->startOfDay()->toDateTimeString();
        $whareDate=$this->getDateByConnection("'$start'",'YYYY-MM-DD HH24:MI:SS');

        $result->winners=WheelTransaction::withoutGlobalScope(CustomerScope::class)
        ->select(DB::raw("SUBSTR(party_name, 1, INSTR(party_name, ' ') - 1) || ' ' || SUBSTR(party_name, INSTR(party_name, ' ', -1) + 1) AS name"),'wheel_item->name as description')
        ->where('status',TransactionStatusEnum::COMPLETED->value)
        ->whereNotNull('wheel_item')
        ->where('wheel_item->type',1)
        ->whereDate('created_at','>=',DB::raw($whareDate))
        //->orderBy(DB::raw($this->getDateByConnection("created_at",'YYYY-MM-DD')),'desc')
        ->orderBy(DB::raw($this->getCastByConnection("json_value(wheel_item, '$.\"value\"')",'DECIMAL')),'desc')
        //->limit(3)
        ->get();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional(collect($result)->toArray()));

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $setting=app(\App\Settings\WheelSettings::class);
        if(!$setting->wheelConfig->allowSpinningWheel || ($request->header('appVersion')??0)<201){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WHEEL",
                    "message"   => [
                        "title"   => __("Failed to spain"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WHEEL_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $start=\Carbon\Carbon::now()->startOfDay()->toDateTimeString();
        $whareDate=$this->getDateByConnection("'$start'",'YYYY-MM-DD HH24:MI:SS');

        $transaction=WheelTransaction::where(function($query) use($whareDate){
            $query
            ->whereDate('created_at','>=',DB::raw($whareDate))
            ->orWhere('status',TransactionStatusEnum::INIT->value);
        })->first();

        if(!is_null($transaction)){
            if($transaction->status==TransactionStatusEnum::INIT->value){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-WHEEL",
                        "message"   => [
                            "title"   => __("Failed to spain"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_WHEEL_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }
            $transactions=WheelTransaction::whereDoesntHave('transaction')
            ->where('status',TransactionStatusEnum::COMPLETED->value)
            ->whereNull('wheel_item')
            ->get();

            if(!count($transactions)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-WHEEL",
                        "message"   => [
                            "title"   => __("Failed to spain"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_WHEEL_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }
        }

        $items=$setting->wheelItems->items
        ->filter(function($item){
            return $item->status==1;
        })
        ->toCollection();

        $selectedIndex=$this->pickIndexBasedOnWeight($items->pluck('weight')->toArray());
        $selectedItem=$items->toArray()[$selectedIndex];
        if((WheelTransaction::amountSumOfTodayTransaction()+$selectedItem['value'])>$setting->wheelConfig->dailyMaxAmount->amount){
            $selectedItem=$items->filter(function($item){
                return $item->type==0;
            })->first()->toArray();
        }
        if($selectedItem['type']!=0 && (($selectedItem['maxAmountPerDay']??0)>-1.0) && (WheelTransaction::amountSumOfTodayTransactionById($selectedItem['id'])+$selectedItem['value'])>$selectedItem['maxAmountPerDay']){
            $selectedItem=$items->filter(function($item){
                return $item->type==0;
            })->first()->toArray();
        }

        $transaction=WheelTransaction::create([
            'status'=>TransactionStatusEnum::INIT->value
        ]);
        LogItem::store($transaction);

        $transaction->wheel_item=collect($selectedItem)->only('id','name','value','type','image','color','weight');
        $transaction->party_name=auth()->user()->name;
        // AccountIdData::from([
        //     "displayValue"=>auth()->user()->name
        // ])->toArray();

       // $transaction->status=TransactionStatusEnum::ERROR->value;
        if(isset($transactions)){
            $transaction->transaction_id=$transactions->first()->id;
        }
        if($selectedItem['type']==1){
            $transaction->save();
            ProcessUsedWheel::dispatch($transaction)->onQueue('critical');
            //ProcessUsedWheel::dispatchSync($transaction);
        }else{
            $transaction->status=TransactionStatusEnum::COMPLETED->value;
            $transaction->save();
        }

         return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "externalReferenceId"=>$transaction->id,
            "item"=>collect($transaction->wheel_item)->only('id','name','type','image','color','weight')
        ]));

    }

    function pickIndexBasedOnWeight($weights) {
        // $weights = [
        //     25.0, 8.333, 12.5, 8.333, 6.25, 8.333,
        //     3.125, 8.333, 1.5625, 8.333, 0.78125, 8.333
        // ];

        $totalWeight = array_sum($weights);
        $rand = mt_rand() / mt_getrandmax() * $totalWeight;

        $cumulative = 0;
        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $index;
            }
        }

        // Fallback
        return count($weights) - 1;
    }
}
