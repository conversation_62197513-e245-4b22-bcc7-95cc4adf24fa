<?php

namespace App\Data\Agent;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class DepositRequestBaseData extends Data
{
   #[Nullable]
   public ?string $agentName;
   #[Required]
   public ?string $agentBranch;
   #[Required]
   public ?string $agentCode;
   #[Required]
   public ?string $serviceCode;
   #[Required]
   public ?string $countryCode;
   #[Required]
   public ?string $inCurrencyCode;
   #[Required]
   public ?CurrencyAmountData $amount;
   #[Required]
   public ?string $partyCode;
   #[Required]
   public ?string $branchCode;
   #[Nullable]
   public ?AccountIdData $customerAccountId;

    #[Nullable]
    public ?int $retry;
   // public static function ()
}