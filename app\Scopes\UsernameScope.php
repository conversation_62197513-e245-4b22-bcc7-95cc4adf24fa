<?php

namespace App\Scopes;

use App\Models\CustomerType;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class UsernameScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return $builder
     */
    public function apply(Builder $builder, Model $model)
    {
        $user=auth()->user();
        if(isset($user->customerRole) && in_array($user->customerRole,CustomerType::getCorporateRoles())){
            $column_name="username";
            return $builder->where($model->getTable().".$column_name",$user->username);
        }
        return $builder;

    }
}
