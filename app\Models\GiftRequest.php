<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GiftRequest extends Model
{
    use HasFactory;
    protected $fillable = ["gift_id",'status',"party_id","party_name","party_phone",'type',"sender",'amount','remarks'];
    protected $casts = [
        'status' => 'int',
        'sender' => 'object',
        'amount' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'gift_id','deleted_at','updated_at'
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            $query->party_id = auth()->user()->id;
        });
    }

    public function gift()
    {
        return $this->belongsTo('App\Models\Gift');
    }
    public function sharing()
    {
        return $this->hasOne('App\Models\Sharing','data_id')->where('type','giftRequest');
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}
