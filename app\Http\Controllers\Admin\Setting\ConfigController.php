<?php

namespace App\Http\Controllers\Admin\Setting;

use App\Data\AccountConfigData;
use App\Data\AppConfigData;
use App\Data\BranchConfigData;
use App\Data\ExchangeAreaConfigData;
use App\Data\GeneralConfigData;
use App\Data\GoldConfigData;
use App\Data\Limit\LimitLinkageData;
use App\Data\Limit\LimitPackageConfigData;
use App\Data\NameData;
use App\Data\PNPLConfigData;
use App\Data\ProductConfigData;
use App\Http\Controllers\Controller;
use App\Models\CustomerType;
use App\Services\LoanService;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\Request;
use \App\Settings\ConfigSettings;
use Auth;
use Illuminate\Support\Facades\View;

class ConfigController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['setting.config.*','setting.config.list'])) return abort(401);

        $filter=$request->all();
        $items=app(\App\Settings\ConfigSettings::class)->toCollection();

        if($request->filled('searchname')){
            $items=$items->filter(function ($element,string $key) use($filter){
                return $key== str_contains($key,$filter['searchname']);
            });
        }
        return view('default.admin.setting.config.index')->with('items', $items)->with('filter', $filter);
    }


        /**
     * Display the specified resource.
     *
     * @param  Request $request
     * @return ?\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function create(Request $request){
       // return \Blade::render($view);

        switch($request->input("id")){

            case 'limitPackageConfig':
                $item=LimitLinkageData::from([
                    "limits" => [
                        [
                            "currency" => "YER",
                            "limitType" => "TXN",
                            "amountRange" => [
                                "minTransaction" => [
                                    "currency" => "YER",
                                    "amount" => 100
                                ],
                                "maxTransaction" => [
                                    "currency" => "YER",
                                    "amount" => 1000000
                                ]
                            ]
                        ],
                        [
                            "currency" => "YER",
                            "limitType" => "PER",
                            "maxAmount" => [
                                "currency" => "YER",
                                "amount" => 3000000
                            ],
                            "maxCount" => 1000,
                            "periodicity" => "DAILY"
                        ],
                        [
                            "currency" => "YER",
                            "limitType" => "PER",
                            "maxAmount" => [
                                "currency" => "YER",
                                "amount" => 10000000
                            ],
                            "maxCount" => 1000,
                            "periodicity" => "MONTHLY"
                        ]
                    ],
                ]);
                return response(view('default.admin.setting.config.components.limit-package-item')
                ->with('key',$request->key)
                ->with('item', $item)/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
            default:
                return response(view('default.admin.setting.config.components.gold-service-item-config')
                ->with('isAjax',$request->ajax())
                ->with('key',$request->key)
                ->with('subChildKey',$request->subChildKey)
                ->with('subChildItem',null)/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
            }
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show(Request $request,$id){
        if (! Auth::user()->canAny(['setting.config.*','setting.config.view','setting.config.edit'])) return abort(401);

        $item=app(ConfigSettings::class)->toCollection()
        ->filter(function ($element,string $key) use($id){
            return $key== $id;
        })->first();

        if(is_null( $item)){
            return abort(\Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND);
        }
        $contents =null;
        if($id=='appConfig'){
            try {
                $contents = \File::get(base_path( "/resources/mocks/utils/privacy.html"));
            } catch (FileNotFoundException $e) {

            }
        }else if($id=='goldConfig'){
            $item->term=NameData::from(json_decode(\File::get(base_path( "/resources/mocks/utils/goldterm.json"))));
        }else if($id=='pnplConfig'){
            $object=new \stdClass();
            $object->partyId=auth()->user()->id;
            $result =LoanService::create($object);
            if(isset($result->getAdditionalData()["activities"])){
                $contents=$result->getAdditionalData();
                $contents["activities"]= collect($contents["activities"])->pluck("activity_name","activity_id")->toArray();
                $contents["sectors"]= collect($contents["sectors"])->pluck("sector_name","sector_code")->toArray();
                $contents["products"]= collect($contents["products"])->pluck("product_name","product_code")->toArray();

                //->where("activity_id",100000001)
                //->first();
                // $sector= collect($result->getAdditionalData()["sectors"])
                // ->where("sector_code","COM")
                // ->first();
                // $product= collect($result->getAdditionalData()["products"])
                // ->where("product_code","PNPL")
                // ->first();
            }
        }

        return view('default.admin.setting.config.view')
        ->with('id', $id)
        ->with('item', $item)
        ->with('contents', $contents);
    }
    // public function flatImageInput(string $key)
    // {
    //     $reault=[];
    //     if(str_contains($key,'*')){
    //         $start=substr($key,0,strpos($key,".*."));

    //         $end=substr($key,strpos($key,".*.")+3,strlen($key));
    //         if(request()->filled($start)){
    //             foreach (request()->input($start) as $k => $value) {
    //                 $baseKey="{$start}.$k.{$end}";
    //                 $reault=array_merge($reault,$this->flatImageInput($baseKey));
    //             }
    //         }

    //     }else{
    //         $reault=[$key];
    //     }
    //     return $reault;
    // }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $service
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request,string $id)
    {
        // $key="goldTypes.*.services.*.icon";
        // $reault=$this->mmmm($key);
        // dd($reault);
        // return;

        if (! Auth::user()->canAny(['setting.config.*','setting.config.edit'])) return abort(401);

        $item=app(ConfigSettings::class)->toCollection()
        ->filter(function ($element,string $key) use($id){
            return $key== $id;
        })->first();

        if(is_null( $item)){
            return abort(\Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND);
        }

        $validations=[];
        switch($id){
            case 'generalConfig':
                $validations+=[
                    "operationsLogging" => 'required|numeric|max_digits:1|max:1',
                    "authorizeServices" => 'required|numeric|max_digits:1|max:1'
                ];
                break;
            case 'appConfig':
                $idRetail=CustomerType::RETAIL;
                $idCorporate=CustomerType::CORPORATE;
                $idAgent=CustomerType::AGENT;
                $validations+=[
                    "reportType" => 'required',
                    "dormantActivateType" => 'required|in:none,local,ana',
                    "notificationEnabled" => 'required',
                    "cacheSignature.$idRetail"=>'nullable',
                    "cacheSignature.$idCorporate"=>'nullable',
                    "cacheSignature.$idAgent"=>'nullable',
                    "tokenValidation"=>'required|numeric|max_digits:1|max:1',
                    "app2FASetting.attempts"=>'required|numeric|max:5',
                    "app2FASetting.resends"=>'required|numeric|max:5',
                    "app2FASetting.status"=>'required|numeric|max_digits:1|max:1',
                    "app2FASetting.firstTimeOnly"=>'required|numeric|max_digits:1|max:1',
                    "app2FASetting.onlyOneDeviceAllowed"=>'required|numeric|max_digits:1|max:1',
                    "app2FASetting.bypassParties"=>'nullable',
                    "app2FASetting.allowedPackageIdsToPassOnlyOneDevice"=>'nullable',
                    "privacy" => 'required',
                    "appVersionSetting.message.ar"=>'required|string',
                    "appVersionSetting.message.en"=>'required|string',
                    'appVersionSetting.types.*.os' => 'required_if:appVersionSetting.types.*.status,1|nullable',
                    "appVersionSetting.types.*.version"=>'required_if:appVersionSetting.types.*.status,1|numeric',
                    "appVersionSetting.types.*.link"=>'required_if:appVersionSetting.types.*.status,1|nullable',
                    "appVersionSetting.types.*.status"=>'required|numeric|in:0,1',
                ];
                break;
            case 'goldConfig':
                $validations+=[
                    "chargeProductId" => 'required',
                    "chargeProductIdSouth" => 'required',
                    "chargeAccountId" => 'required',
                    "chargeTransferProductId" => 'required',
                    "chargeTransferAccountId" => 'required',
                    "term.ar" => 'required',
                    "term.en" => 'required',

                    "goldTypes.*.id" => 'required',
                    "goldTypes.*.name.ar" => 'required',
                    "goldTypes.*.name.en" => 'required',
                    "goldTypes.*.status" => 'required',
                    "goldTypes.*.services.*.name.ar" => 'required',
                    "goldTypes.*.services.*.name.en" => 'required',
                    //"services.*.icon" => 'required',
                    "goldTypes.*.services.*.url" => 'required',
                    "goldTypes.*.services.*.status" => 'required',
                    "goldTypes.*.services.*.enabled" => 'required',


                    // "services.*.name.ar" => 'required',
                    // "services.*.name.en" => 'required',
                    // //"services.*.icon" => 'required',
                    // "services.*.url" => 'required',
                    // "services.*.status" => 'required',
                    // "services.*.enabled" => 'required',

                    "featuresTitle.ar" => 'required',
                    "featuresTitle.en" => 'required',

                    "features.*.name.ar" => 'required',
                    "features.*.name.en" => 'required',
                    //"features.*.icon" => 'required',
                    "features.*.url" => 'nullable',
                    "features.*.status" => 'required',
                    "features.*.enabled" => 'required',
                ];
            case 'limitPackageConfig':
                $validations+=[
                    "targetLimitLinkages.*.target.value" => 'required',
                    "targetLimitLinkages.*.target.area" => 'nullable',
                    "targetLimitLinkages.*.target.currency" => 'nullable',

                    "targetLimitLinkages.*.limits.*.limitType" => 'required',
                    "targetLimitLinkages.*.limits.*.maxAmount.currency" => 'required_if:targetLimitLinkages.*.target.*.limitType,!=,TXN',
                    "targetLimitLinkages.*.limits.*.maxAmount.amount" => 'required_if:targetLimitLinkages.*.target.*.limitType,!=,TXN',
                    "targetLimitLinkages.*.limits.*.maxCount" => 'required_if:targetLimitLinkages.*.target.*.limitType,!=,TXN',
                    "targetLimitLinkages.*.limits.*.periodicity" => 'required_if:targetLimitLinkages.*.target.*.limitType,!=,TXN',

                    "targetLimitLinkages.*.limits.*.amountRange.minTransaction.currency" => 'required_if:targetLimitLinkages.*.target.*.limitType,==,TXN',
                    "targetLimitLinkages.*.limits.*.amountRange.minTransaction.amount" => 'required_if:targetLimitLinkages.*.target.*.limitType,==,TXN',

                    "targetLimitLinkages.*.limits.*.amountRange.maxTransaction.currency" => 'required_if:targetLimitLinkages.*.target.*.limitType,==,TXN',
                    "targetLimitLinkages.*.limits.*.amountRange.maxTransaction.amount" => 'required_if:targetLimitLinkages.*.target.*.limitType,==,TXN',

                ];
                break;
            case 'pnplConfig':
                $validations+=[
                    "activity" => 'required',
                    "sector" => 'required',
                    "product" => 'required',
                    "amount.amount"=>'required|numeric',
                    "amount.currency"=>'required|in:'.join(",",\App\Enums\CurrencyTypeEnum::values()),
                ];
                break;
        }

        $this->validate($request,$validations);


        switch($id){
            case 'generalConfig':
                $configData=GeneralConfigData::from($request->all());
                break;
            case 'appConfig':
                $configData=AppConfigData::from($request->all());
                \File::put(base_path( "/resources/mocks/utils/privacy.html"),$request->privacy);
                break;
            case 'goldConfig':
                $this->setImage('goldTypes.*.services.*.icon',"golds");
               // $this->setImage('services.*.icon',"golds");
                $this->setImage('features.*.icon',"golds");
                // dd($request->all());
                // return;
                $this->validate($request,[
                    "goldTypes.*.services.*.icon" => 'required',
                    //"services.*.icon" => 'required',
                    "features.*.icon" => 'required',
                ]);
                \File::put(base_path( "/resources/mocks/utils/goldterm.json"),json_encode($request->term));
                \Cache::tags(['goldTerm'])->flush();
                $configData=GoldConfigData::from($request->except('term'));
                break;
            case 'productConfig':
                $configData=ProductConfigData::from($request->all());
                break;
            case 'branchConfig':
                $configData=BranchConfigData::from($request->all());
                break;
            case 'accountConfig':
                $configData=AccountConfigData::from($request->all());
                break;
            case 'exchangeAreaConfig':
                $configData=ExchangeAreaConfigData::from($request->all());
                break;
            case 'limitPackageConfig':
                $configData=LimitPackageConfigData::from($request->all());
                break;
            case 'pnplConfig':
                $configData=PNPLConfigData::from($request->all());
                break;
        }

        $settings=app(ConfigSettings::class);
        $settings->{"$id"}=$configData;
        $settings->save();

        return back()->with('success',__("Operation accomplished successfully"));
    }



    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['setting.config.*','setting.config.delete'])) return abort(401);

    }

}
