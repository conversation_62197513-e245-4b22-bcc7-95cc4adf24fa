<?php

namespace App\Jobs;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\PaymentResultData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\Models\SplitPayment;
use App\Services\NotificationService;
use App\Services\OBDX\BankyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
class ProcessCanceledSplit implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

   /**
     * Create a new job instance.
     * @param SplitPayment $splitPayment
     * @return void
     */
    public function __construct(
        public SplitPayment $splitPayment,
    ){
        //
    }
    /**
     * Determine the unique ID for the job.
     */
    public function uniqueId()
    {
        return "wheel.cancel.{$this->splitPayment->id}";
    }
    /**
     * Execute the job.
     */
    public function handle(): void
    {
        foreach($this->splitPayment->transactions as $transaction){
            if(!is_null($this->splitPayment->bank_code) && $transaction->status==TransactionStatusEnum::COMPLETED->value &&
            $transaction->type==InvoiceTransactionsTypeEnum::Payment->value){
                   /** Reverse to user account */
                $paymentResult=$transaction->payment_result;

                $object = new \stdClass();
                $object->journals= [
                    "genericPayee" =>[
                        "nickName" =>'',
                        "accountName" =>'temp',
                        "accountNumber" => $transaction->debit_account_id->value,
                        "transferMode"=> 'ACC'
                    ],
                    "genericPayout" =>[
                        "amount" =>CurrencyAmountData::from([
                            "amount"=>$transaction->amount->amount+$transaction->fee->amount,
                            "currency"=>$transaction->amount->currency
                        ])->toArray(),
                        "purpose" =>'FAML',
                        "purposeText" => null,
                        "debitAccountId" => AccountIdData::from([
                            "displayValue"=>ThirdPartyServiceNameData::split(),
                            "value"=>ThirdPartyServiceNameData::split()
                        ])->toArray(),
                        "remarks"=> sprintf(trans("Split payment reverse for [%s]"),
                        $paymentResult->payment->externalReferenceId
                        )

                    ],
                    "paymentType" => 'INTERNALFT'
                ];

                $result=BankyService::internalTransefer($object);
                if ($result->status->message->code == "0") {
                    $transaction->status=TransactionStatusEnum::CANCELED->value;
                    $transaction->type=InvoiceTransactionsTypeEnum::Reverse->value;

                    $transaction->payment_result=[
                        "payment"=>$paymentResult->payment,
                        "reverse"=>new PaymentResultData(
                            $result->getAdditionalData()["referenceId"],
                            $result->getAdditionalData()["externalReferenceId"],
                            InvoiceTransactionsTypeEnum::Reverse->value
                        ),
                    ];
                }else{
                    $transaction->status=TransactionStatusEnum::ERROR->value;
                    $transaction->type=InvoiceTransactionsTypeEnum::Reverse->value;

                    $this->splitPayment->status=TransactionStatusEnum::ERROR->value;
                    $this->splitPayment->type=InvoiceTransactionsTypeEnum::Reverse->value;
                    $this->splitPayment->save();
                }
                $transaction->save();

            }else{
                $transaction->status=TransactionStatusEnum::CANCELED->value;
                $transaction->save();
            }
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Split payment"),
                    'body'=>sprintf(__("The split payment with referenece [%s] was canceled!"),
                    $this->splitPayment->id
                    ),
                    'type'=>'operation',
                ]
            ],$transaction->party_id);
        }
    }
}
