<?php

namespace App\Services\OBDX;
use App\Data\GeneralResponseData;
use App\Data\OBDX\Transfer\DomesticTransferData;
use App\Data\OBDX\Transfer\InternalTransferData;
use App\Data\TokenData;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Support\Facades\Http;
use App;

/**
 * Service to create and update orders
 */
class BankyService
{
    protected $url;

    protected $cookies;
    protected $headers;

    public $settings;
    public $isTest=false;

     /**
     * Service to handle customer requestes
     *
     */
    public function __construct()
    {
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->obdxBanky;
        if($this->settings->is_test){
            $this->isTest=true;
            $this->settings=$this->settings->test_data;
        }
        $this->url="{$this->settings->url}/digx/v1";
        $this->setCookies();
        $this->headers=["Accept-Encoding"=>"identity"];
    }

    /**
     * Register a stub callable that will intercept requests and be able to return stub responses.
     *
     * @param  string  $method
     * @param  string  $url
     * @param  callable|array $params
     * @param  callable|array $fake
     * @return \Illuminate\Http\Client\Response
     */
    function getHttpRequest(string $method,string $url,array $params=[],array $fake=[]){
        if(!empty($fake)){
            $request=Http::fake($fake);
        }else{
            $request=Http::withOptions([
                'cookies' => $this->cookies
            ]);
        }
        //$request=$request->withHeaders($this->headers);
        if($method=='GET' && !empty($params)){
            $params=[
                'query' => $params,
            ];
        }else if($method!='GET' && !empty($params)){
            $params=[
                'json' => $params,
            ];
        }
        $response=$request//->withHeaders($this->headers)
        ->send( $method,"{$this->url}/$url", $params);
        return $response;

    }
    public static function getCookies()
    {
        return collect(request()->cookies->all())
            ->map(function ($cookie,$key) {
                return $key . '=' . ($cookie ?? '') . '; ';
            })->toArray();
    }

    /**
     * Inject cookies to current request session
     *
     * @param  array  $cookies
     * @return void
     */
    protected function setCookies()
    {
        $cookies =json_decode($this->settings->token?->access_token??null,true);
        if(!is_null($cookies)){
            $this->cookies=CookieJar::fromArray($cookies, parse_url($this->settings->url, PHP_URL_HOST) /*"{$_ENV['OBDX_URL']}"*/);
        }else{
            $this->cookies=$cookies;
        }

    }

     /**
     * request access token.
     *
     * @param  bool $forceUpdate
     * @return ?int|?string
     */
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
            $this->cookies=null;
        }
        if(!isset($token->access_token) || is_null($token->access_token)||empty($token->access_token)){
            $response = rescue(function (){
                return Http::withOptions([
                    'verify' => false,
                ])
                ->withoutRedirecting()
                ->timeout(10)
                ->asForm()
                ->post(
                    "{$this->url}/j_security_check",
                    [
                        'j_username' => $this->settings->client_id,
                        'j_password' => $this->settings->client_secret,
                    ]
                );
            }, function ($e) {
                return $e->getMessage();
            });

            if(is_string($response)){
                return null;
            }

            if ($response->failed()) {
                return null;
            }
            $status=$response->status();
            if(in_array($response->status(),[303,302])){
                $status=200;

                $cookies=collect($response->cookies()->toArray())
                ->flatMap(function ($values) {
                    //Cookie::queue($values["Name"], $values["Value"]);
                    return array_map(function ($value){
                        return $value;
                    }, [$values["Name"]=>$values["Value"]]);
                })->toArray();

                $this->settings->token=TokenData::from([
                    "access_token"=>json_encode($cookies),
                    "token_type"=>"cookies"
                ]);

                //Add cookies to current request
                $this->setCookies();

                $settings=app(\App\Settings\ThirdPartySettings::class);
                $obdxBanky=$settings->obdxBanky;
                if($obdxBanky->is_test){
                    $test_data=$obdxBanky->test_data;
                    $test_data->token=$this->settings->token;
                    $obdxBanky->test_data=$test_data;
                }else{
                    $obdxBanky->token=$this->settings->token;
                }
                $settings->obdxBanky=$obdxBanky;
                $settings->save();
            }
        }
        return $status;
    }

    public static function internalTransefer($request)
    {

        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return -1;
        }

        $response=$service->getHttpRequest('POST',"payments/generic",$request->journals);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::internalTransefer($request);
        }

        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->paymentId)){
            $referenceId=$result->paymentId;
            $response=$service->getHttpRequest('PATCH',"payments/generic/{$result->paymentId}?paymentType=INTERNALFT",[]);

            if(is_string($response)){
                return GeneralResponseData::from(static::defaultErrorResult);
            }

            $result=$response->object();
            return GeneralResponseData::from($result)
            ->additional([
                "referenceId"=>$referenceId,
                "externalReferenceId"=>$result->externalReferenceId??""
            ]);
        }
        return GeneralResponseData::from($result);//abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function postGetGenericTransefer($request): GeneralResponseData
    {

        $result=static::postGenericTransefer($request);
        if(!($result instanceof GeneralResponseData)){
            $paymentId=$result->paymentId;
            $result= static::getGenericTransefer($result);
            if(!($result instanceof GeneralResponseData)){

                if($result->paymentType=='INDIADOMESTICFT'){
                    $payload=DomesticTransferData::from($result->domesticPayoutReadResponse);
                    //$payload->remarks=$request->remarks??null;
                }else{
                    $payload=InternalTransferData::from($result->internalTransferReadResponse);
                }
                return GeneralResponseData::from($result)
                ->additional([
                    "paymentId"=>$paymentId,
                    "payload"=>$payload
                ]);
            }
        }
        return $result;
    }
    public static function postGenericTransefer($request)
    {

        $service=new static();
        // if(env('APP_HOSTING', 'remote')=='local' && env('APP_ENV', 'production')!='production'){
        //     Http::fake([
        //         "{$service->url}/payments/generic" => Http::response(
        //             include(base_path().'/resources/mocks/obdx/internal_post.php'),
        //             200),
        //         "{$service->url}/payments/generic/5HTYSS6RRO" => Http::response(
        //             include(base_path().'/resources/mocks/obdx/domestic_get.php'),
        //             200)
        //     ]);
        // }

        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return -1;
        }

        $response=$service->getHttpRequest('POST',"payments/generic",$request->journals);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::postGenericTransefer($request);
        }

        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->paymentId)){
            return $result;
        }
        return GeneralResponseData::from($result);//abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function getGenericTransefer($request)
    {

        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return -1;
        }

        $response=$service->getHttpRequest('GET',"payments/generic/{$request->paymentId}");
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::getGenericTransefer($request);
        }

        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }
        return GeneralResponseData::from($result);//abort(response()->json(GeneralResponseData::from($result)));
    }

    public static function patchGenericTransefer($request)
    {

        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return -1;
        }

        $response=$service->getHttpRequest('PATCH',"payments/generic/{$request->paymentId}?paymentType={$request->paymentType}",[]);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }

        if(in_array($response->status(),[401,403])){
            $request->retry=($request->retry??-1)+1;
            return static::patchGenericTransefer($request);
        }

        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return GeneralResponseData::from($result)
            ->additional([
                "referenceId"=>$request->paymentId,
                "externalReferenceId"=>$result->externalReferenceId??""
            ]);
        }
        return GeneralResponseData::from($result);//abort(response()->json(GeneralResponseData::from($result)));
    }

    public static function partyAccounts($request,$partyId)
    {
        $service=new static();
        $status=$service->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return false;
        }

        $response=$service->getHttpRequest('GET',"parties/{$partyId}/accounts",[
            "accountType"=>"CSA",
        ]);
        if(is_string($response)){
            return false;
        }

        if(in_array($response->status(),[401,403,400])){
            $request->retry=($request->retry??-1)+1;
            return static::partyAccounts($request,$partyId);
        }

        $result=$response->object();

        // if(in_array($response->status(),[400]) && empty($result)){
        //     $request->retry=($request->retry??-1)+1;
        //     return static::partyAccounts($request,$partyId);
        // }

        if(isset($result->partyToAccountRelationshipDTOs)){
            return $result->partyToAccountRelationshipDTOs;
        }
        return false;
    }
}
