<?php

namespace App\Models;

use App\CacheModel;
use App\Data\BaseNonNullableDataCollection;
use App\Data\Form\FormFieldData;
use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>tie\LaravelData\DataCollection;

class BillPaymentService extends CacheModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'title',
        'image',
        'view_type',
        'url',
        'status',
        'fields',
        'payload'
    ];
    protected $casts = [
        'title'             => 'object',
        'fields'            => BaseNonNullableDataCollection::class.':'.FormFieldData::class,
        'payload'           => 'object',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    public function getNameAttribute()
    {
        $local=app()->getLocale();
        return  $this->title?->{"$local"};
    }
    public function item()
    {
        return $this->hasOne(BillPaymentItem::class, 'bill_payment_service_id', 'id');
    }

    public function items()
    {
        return $this->hasMany(BillPaymentItem::class, 'bill_payment_service_id', 'id');
    }

    public function filters()
    {
        //return $this->morphToMany(BillPaymentFilter::class, "model","bill_payment_model_filters","model_id","bill_payment_filter_id","id","id",false, 'custom_type');

        return $this->morphToMany(BillPaymentFilter::class, "model","bill_payment_model_filters");
    }

    public static function getServiceWithRelations($id,$item=null){
        return static:: with(['filters' => function ($query)use ($id) {
            $query->whereNull('parent_filter_id')->with([
                'filters' => function ($query) use ($id) {
                    $query->with([
                        'filters' => function ($query) use ($id) {
                            $query->with('options')
                            ->where('model_type', BillPaymentService::class)
                            ->where('model_id', $id);
                        }
                    ])
                    ->with('options')
                    ->where('model_type', BillPaymentService::class)
                    ->where('model_id', $id);
                }
            ])->with('options');
        }])
        ->with(['items' => function ($query) use($item) {
            $query
            ->with("options")
            ->with(['bundles' => function ($query) {
                $query->with("options")
                 ->orderBy('amount_value','ASC');
                //->orderBy('amount->amount','ASC');
            }])
            ->whereNotNull('biller')
            ->where('status',1);
            if(!is_null($item)){
                $query->where('id', $item);
            }
        }])
        ->where('id',$id)
        ->where('status',1)
        ->first();
    }
}
