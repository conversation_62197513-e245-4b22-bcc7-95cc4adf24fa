<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillPaymentFilterOption extends Model
{
    use HasFactory;

    protected $fillable = ['id','bill_payment_filter_id','key_value','title','image','status','payload'];
    protected $casts = [
        'title' => 'object',
        'payload'           => 'object',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    public function getNameAttribute()
    {
        $local=app()->getLocale();
        return  $this->title?->{"$local"};
    }
    public function filter()
    {
        return $this->belongsTo(BillPaymentFilter::class,'bill_payment_filter_id' );
    }
    public function items()
    {
        return $this->morphedByMany(BillPaymentItem::class, "model","bill_payment_model_filter_options");
    }
    public function bundles()
    {
        return $this->morphedByMany(BillPaymentBundle::class, "model","bill_payment_model_filter_options");
    }
}
