<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CacheModel extends Model
{
    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot(){
        parent::boot();
        static::saving(function() {
            //\Log::info("Cache clear on saving: $cache");
            static::withCacheUpdate();
        });
        static::deleting(function($item) {
            //\Log::info("Cache clear on deleting: $cache");
            static::withCacheUpdate();
        });
    }

    public static function getWithCache($tag, callable $callback){
        return static::withCache([],$tag,$callback);
    }
    public static function withCache($tags,$tag, callable $callback){
        $tags[]=basename(static::class);
        return \Cache::tags($tags)->rememberForever("$tag", function () use ($tags,$tag,$callback){
            return $callback(new static);
        });
    }
    public static function withCacheUpdate($key=null){
        $cache=basename(static::class);
        return static::cacheUpdate([$cache],$key);
    }

    public static function cacheUpdate($tags,$key=null){
        if(is_null($key))
            \Cache::tags($tags)->flush();
        else
            \Cache::tags($tags)->forget("$key");
        return new static;
    }
}

