<?php

namespace App\Services\OBDX;
use App\Data\GeneralResponseData;
use App\Mail\OtpSent;
use App\Models\Cardless;
use App\Models\CustomerType;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Carbon\Carbon;
use Crypt;
use Illuminate\Http\Request;
use GuzzleHttp\Cookie\CookieJar;

use Illuminate\Support\Facades\Http;

use App;
use App\Data\StatusData;
use App\Enums\TransactionStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\PartyVerify;
use App\Services\NotificationService;
use Closure;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;

/**
 * Service to create and update orders
 */
class LoginService
{
    protected $url;

    protected $cookies;
    protected $headers;

    /**
     * Service to handle customer requestes
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function __construct(Request $request){
        $this->url="{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1";
        $this->cookies=CookieJar::fromArray($request->cookies->all(),"{$_ENV['OBDX_URL']}");
        $this->headers =  collect($request->headers->all())->except([
            'host',
            'cookie',
            'content-length',
            "user-agent",
            "postman-token",
            "content-type",
            "token",
            "x-original-url",
            "x-challenge-response"
        ])->all();
        // if($request->hasHeader("X-CHALLENGE-RESPONSE")){
        //     $this->headers+=["X-CHALLENGE_RESPONSE"=>$request->header("X-CHALLENGE-RESPONSE")];
        // }
        $this->headers+=["Accept-Encoding"=>"identity"];
    }
    /**
     * Register a stub callable that will intercept requests and be able to return stub responses.
     *
     * @param  string  $method
     * @param  string  $url
     * @param  callable|array $params
     * @param  callable|array $fake
     * @return \Illuminate\Http\Client\Response
     */
    function getHttpRequest(string $method,string $url,array $params=[],array $fake=[]){
        if(!empty($fake)){
            $request=Http::fake($fake);
        }else{
            $request=Http::withOptions([
                'cookies' => $this->cookies,
            ])->timeout(20);
        }
        //$request=$request->withHeaders($this->headers);
        if($method=='GET' && !empty($params)){
            $params=[
                'query' => $params,
            ];
        }else if($method!='GET' && !empty($params)){
            $params=[
                'json' => $params,
            ];
        }
        $response=$request
        //->withUserAgent("OIC-Authentication")
        ->withHeaders($this->headers)->send( $method,"{$this->url}/$url", $params);
        return $response;

    }
    public static function getCookies()
    {
        return collect(request()->cookies->all())
            ->map(function ($cookie,$key) {
                return $key . '=' . ($cookie ?? '') . '; ';
            })->toArray();
       // $str = $data['Name'] . '=' . ($data['Value'] ?? '') . '; ';
        // foreach ($data as $k => $v) {
        //     if ($k !== 'Name' && $k !== 'Value' && $v !== null && $v !== false) {
        //         if ($k === 'Expires') {
        //             $str .= 'Expires=' . \gmdate('D, d M Y H:i:s \G\M\T', $v) . '; ';
        //         } else {
        //             $str .= ($v === true ? $k : "{$k}={$v}") . '; ';
        //         }
        //     }
        // }

       // return \rtrim($str, '; ');
    }

    /**
     * Inject cookies to current request session
     *
     * @param  array  $cookies
     * @return void
     */
    protected function setCookies($cookies)
    {
        request()->cookies->add(
            collect($cookies)
            ->flatMap(function ($values) {
                /*Cookie::queue( Cookie::make(
                    $values["Name"],
                    $values["Value"],
                    // 0,
                    // null,
                    // null,
                    // true,
                    // true,
                    // false,
                    // 'NONE'
                ));*/
                //if(env('APP_ENV', 'production')!='production'){
                    //setrawcookie($values["Name"], preg_replace('/[\s,;]+/', '', $values["Value"]),0, "/",'',false,true);
                    Cookie::queue($values["Name"], $values["Value"]);
                    //Cookie::queue($values["Name"], $values["Value"], 0, null, request()->host(), false, true, false, null);
                //}else{
                //    Cookie::queue($values["Name"], $values["Value"]);
                //}
                return array_map(function ($value){
                    return $value;
                }, [$values["Name"]=>$values["Value"]]);
            })->toArray()
        );
        // request()->headers->add(
        //     ["cookie"=>collect($cookies)
        //     ->map(function ($values) {
        //         return $this->cookiesToString($values);
        //     })->toArray()]

        // );
        $this->cookies=CookieJar::fromArray(request()->cookies->all(),"{$_ENV['OBDX_URL']}");

    }
    protected function forgetAllQueuedCookies()
    {
        foreach (Cookie::getQueuedCookies() as $cookie) {
            Cookie::unqueue($cookie->getName());
        }
        //header_remove('set-cookie');
    }
    /**
     * Inject user to request & session to request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|null
     */
    public static function login()
    {
        $request=request();
        $username=AdminService::usernames(request(),$request->j_username);

        $service=new LoginService($request);
        $response = Http::withOptions([
            'verify' => false,
        ])
        //->withUserAgent("OIC-Authentication")
        //->withHeaders($service->headers)
        ->withoutRedirecting()
        ->timeout(10)
        ->asForm()
        ->post(
            "{$service->url}/j_security_check",
            [
                'j_username' =>$username??$request->j_username,
                'j_password' => $request->j_password,
            ]
        );

        if(in_array($response->status(),[303,302])){
            //Add cookies to current request
            $service->setCookies($response->cookies()->toArray());

            //Apply user middleware to attatch current user to this request
            if (auth('digx')->check()) {
                auth()->shouldUse('digx');
            }

            //Start getting user token
            return static::token();
        }

        return response($response->getBody())
        //->withCookie($service->cookies)
        ->withHeaders(
            collect($response->getHeaders())
            ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
            ->toArray()
        )
        ->setStatusCode($response->status());
    }

    /**
     * Get user identety card info
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public static function token()
    {

        $request=request();

        $service=new LoginService($request);
        $fake=[];

        if(auth()->user()->customerType==CustomerType::GUEST){
            $jwtoken=auth()->user()->createToken('Guest Access')->accessToken;
            $headers=[];
            //$this->partyVerify->terminal->credentials;
        }else{
            $response=$service->getHttpRequest('POST','jwt',[
                "channelType"=>"MOBILEJWT",
                "password"=>$request->j_password
            ],$fake);
            $result=$response->object();
            $jwtoken=$result->jwtoken??null;

            $headers=$response->getHeaders();
        }



        if(!is_null($jwtoken) || (isset($result->message->code) && $result->message->code=="DIGX_UM_042")){
            $service->loginWithout2FA($jwtoken??null);

            $terminal=json_decode(base64_decode($request->header("terminal")));
            if(auth()->user()->customerType==CustomerType::GUEST){
                try {
                    $encrypt = Crypt::encrypt("{$request->j_username}:{$request->j_password}");
                    $terminal->credentials=$encrypt;
                } catch (\Illuminate\Contracts\Encryption\EncryptException $e) {
                    return;
                }
                //$this->partyVerify->terminal->credentials;
            }

            $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;

            PartyVerify::where("device_key",$request->header("deviceKey"))
            ->where("status",TransactionStatusEnum::INIT->value)
            ->forceDelete();
            $partyVerify=PartyVerify::create([
                "username"=>auth()->user()->username??null,
                "phone_number"=>auth()->user()->phone,
                "customer_type_id"=>auth()->user()->customerType,
                "otp"=>Controller::generateNumericOTP(4),
                "token"=>$jwtoken??null,
                "attempts_left"=>$app2FASetting->attempts,
                "resends_left"=>$app2FASetting->resends,
                "device_key"=>$request->header("deviceKey"),
                "terminal"=> $terminal,
                "expiry_date"=> Carbon::now()->addMinutes(15)->toDateTimeString(),
                "status"=>TransactionStatusEnum::INIT->value,
                "type"=>"OTP"
            ]);
            $smsMessage=sprintf(trans("verify_otp_message"),
                $partyVerify->otp,
                "15",
                "r0OeQ7JGReE"
            );

            NotificationService::sendSMS([
                "mobile"=> $partyVerify->phone_number,
                "message"=> $smsMessage
            ]);
            if(!empty(auth()->user()->email??"")){
                NotificationService::email(auth()->user()->email,data:new OtpSent($partyVerify));
            }
            return response([
                "status"=> [
                    "result"=> "EXPECTATION_FAILED",
                    "referenceNumber"=> "",
                    "contextID"=> "",
                    "message"=> [
                        "code"=> "DIGX_AUTH_0003",
                        "type"=> "INFO"
                    ]
                ],
                //"firstLoginFlowDone"=> false
            ])
            ->withHeaders(collect($headers)
                ->put("Content-Type","application/json")
                //->put('set-cookie', static::getCookies())
                ->put("X-CHALLENGE",json_encode([
                    "authType"=>"OTP",
                   // "otp"=>$partyVerify->otp,
                    "referenceNo"=>$partyVerify->id,
                    "attemptsLeft"=>$partyVerify->attempts_left,
                    "scope"=>"USERTASK",
                    "resendsLeft"=>$partyVerify->resends_left,
                ]))
                ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location','Set-Cookie',"Content-Length","content-length","Content-Security-Policy","Referrer-Policy"])
                ->toArray()
            )
            ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_EXPECTATION_FAILED);

        }else{
            return response($response->getBody())
            ->withHeaders(collect($response->getHeaders())
                //->put('set-cookie', static::getCookies())
                ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
                ->toArray()
            )
            ->setStatusCode($response->status());
        }
    }
    /**
     * Flow of none 2fa login
     *
     * @param  PartyVerify  $partyVerify
     * @return \Illuminate\Http\Response
     */
    protected function requiredChangePassword($partyVerify)
    {
        return abort(response(StatusData::from(array(
            "result"    => "SUCCESSFUL",
            "contextID" => "OBDX-LOGIN",
            "message"   => [
                "title"   => "Password change required.",
                "detail"  => "Password change required.",
                "code"    => "DIGX_UM_042",
                "type"    => "ERROR"
            ]
        ))->toArray())
        ->withHeaders([
            "referenceNo"=>$partyVerify->id
        ])
        ->setStatusCode(400));
    }
    /**
     * Flow of none 2fa login
     *
     * @param  string  $jwtoken
     * @param  \Closure  $next
     * @return ?\Illuminate\Http\Response
     */
    protected function loginWithout2FA($jwtoken)
    {
        $request=request();

        if($request->hasHeader("registrationId")){
            $partyVerify=PartyVerify::withoutGlobalScope(UsernameScope::class)->where('id',$request->header("registrationId"))
            ->where('device_key',$request->header("deviceKey"))
            ->where("status",TransactionStatusEnum::INIT->value)
            ->first();
            if(!is_null( $partyVerify) && isset($partyVerify->terminal->registrationId)){
                $partyVerify->token=$jwtoken??null;
                $partyVerify->customer_type_id=auth()->user()->customerType??null;
                if(auth()->user()->customerType==CustomerType::GUEST){
                    try {
                        $encrypt = Crypt::encrypt("{$request->j_username}:{$request->j_password}");
                        $terminal=$partyVerify->terminal;
                        $terminal->credentials=$encrypt;
                        $partyVerify->terminal=$terminal;
                    } catch (\Illuminate\Contracts\Encryption\EncryptException $e) {
                        return;
                    }
                }
            }
        }

        $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;
        if($app2FASetting->onlyOneDeviceAllowed && auth()->user()->customerType!=CustomerType::GUEST &&
             !in_array(auth()->user()->id,$app2FASetting->bypassParties)
             //&& !session()->has("appsecret")
             ){
            $parties=PartyVerify::where('status',TransactionStatusEnum::COMPLETED->value)
            ->get();

            $currentParty=$parties->filter(function($item) use($request){
                return $item->device_key==$request->header("deviceKey");
            })
            //->where('device_key',$request->header("deviceKey"))
            ->first();

            $terminal=json_decode(base64_decode($request->header("terminal")));
            $isNewPackage=false;
            if(isset($terminal->package) && in_array($terminal->package,$app2FASetting->allowedPackageIdsToPassOnlyOneDevice)){
                $partiesPackage=$parties
                ->filter(function($item) use($terminal){
                    return isset($item->terminal->package) && $item->terminal->package==$terminal->package;
                })
                //->where('terminal->package',$terminal->package)
                ->all();

                $isNewPackage=count($partiesPackage)==0;
            }

            // $msg="A:".(count($parties)?'1':'0')."B:". (is_null($currentParty)?'1':'0')."C:".(!$isNewPackage?'1':'0');
            // if(auth()->user()->id=="0151268"){
            //     //$this->forgetAllQueuedCookies();
            //     return abort(response(StatusData::from(array(
            //         "result"    => "SUCCESSFUL",
            //         "contextID" => "OBDX-LOGIN",
            //         "message"   => [
            //             "title"   => __("You can't sign in with this new device right now, please call customer service for help!"),
            //             "detail"  => "$msg",
            //             "code"    => "DIGX_NEW_DEVICE",
            //             "type"    => "ERROR"
            //         ]
            //     ))->toArray())
            //     ->setStatusCode(400));
            // }

            if((isset($terminal->osType) && $terminal->osType=="WEB") || (count($parties) && is_null($currentParty) && !$isNewPackage)){
                $this->loginUsingOtherDevice2FA($jwtoken);
                return;
            }
        }

        if(!isset($partyVerify->terminal->registrationId)){
            if($app2FASetting->status && !in_array(auth()->user()->id,$app2FASetting->bypassParties)){
                if($app2FASetting->firstTimeOnly){
                    $previousParty=PartyVerify::where('status',TransactionStatusEnum::COMPLETED->value)
                    ->where('device_key',$request->header("deviceKey"))
                    ->first();
                    if(is_null($previousParty)){
                        return;
                    }
                }else{
                    return;
                }
            }

            PartyVerify::where("device_key",$request->header("deviceKey"))
            ->where("status",TransactionStatusEnum::INIT->value)
            ->forceDelete();

            $terminal=json_decode(base64_decode($request->header("terminal")));
            if(auth()->user()->customerType==CustomerType::GUEST){
                try {
                    $encrypt = Crypt::encrypt("{$request->j_username}:{$request->j_password}");
                    $terminal->credentials=$encrypt;
                } catch (\Illuminate\Contracts\Encryption\EncryptException $e) {
                    return;
                }
            }

            $partyVerify=PartyVerify::create([
                "username"=>auth()->user()->username??null,
                "phone_number"=>auth()->user()->phone,
                "customer_type_id"=>auth()->user()->customerType,
                "token"=>$jwtoken??null,
                "device_key"=>$request->header("deviceKey"),
                "terminal"=>$terminal,
                "expiry_date"=> Carbon::now()->addMinutes(15)->toDateTimeString(),
                "status"=>TransactionStatusEnum::INIT->value
            ]);
        }


        if(!is_null($partyVerify->token)){
            $this->registerDevice($partyVerify->terminal);
        }


        $partyVerifies=PartyVerify::where('status',TransactionStatusEnum::COMPLETED->value)
        ->get();

        $registerdDevices=PartyVerify::where('status',TransactionStatusEnum::COMPLETED->value)
        ->where('device_key',$request->header("deviceKey"))
        ->get();

        if(!count($registerdDevices->toArray()) && count($partyVerifies->toArray())){
            //dispatch()->
            NotificationService::send([
                'tokens'=>$partyVerifies->map(function ($element,int $key){
                    if(isset($element->terminal->registrationToken)){
                        return $element->terminal->registrationToken;
                    }
                    return null;
                })->filter()->unique()->toArray(),
                'title'=>__("New device"),
                'body'=>sprintf(trans("new_device_message"),
                    $partyVerify->terminal->brand
                ),
                'type'=>'operation',
                'extra_id'=>$partyVerify->id
            ]);
        }
        PartyVerify::where("device_key",$partyVerify->device_key)
        ->where("status",TransactionStatusEnum::COMPLETED->value)
        ->delete();

        $partyVerify->status=TransactionStatusEnum::COMPLETED->value;
        $partyVerify->save();

        if(is_null($partyVerify->token)){
            $this->requiredChangePassword($partyVerify);
        }

        return abort(response(CustomerService::details($request))
        ->withHeaders([
            "Content-Type"=>"application/json",
            //'set-cookie'=> static::getCookies(),
            'jwtoken'=> $jwtoken,
        ])
        ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_FOUND));
    }

    protected function loginUsingOtherDevice2FA($jwtoken)
    {
        $errorResult=StatusData::from(array(
            "result"    => "SUCCESSFUL",
            "contextID" => "OBDX-LOGIN",
            "message"   => [
                "title"   => __("You can't sign in with this new device right now, please call customer service for help!"),
                "detail"  => "",
                "code"    => "DIGX_NEW_DEVICE",
                "type"    => "ERROR"
            ]
        ));

        if((request()->header('appVersion')??0)>=203){
            $request=request();


            $registerdDevices=PartyVerify::where('status',TransactionStatusEnum::COMPLETED->value)
            ->where('device_key','!=',$request->header("deviceKey"))
            ->get();

            if(count($registerdDevices->toArray())){
                $terminal=json_decode(base64_decode($request->header("terminal")));

                $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;

                PartyVerify::where("device_key",$request->header("deviceKey"))
                ->whereIn("status",[TransactionStatusEnum::INIT->value,TransactionStatusEnum::PENDING->value])
                ->forceDelete();
                $partyVerify=PartyVerify::create([
                    "username"=>auth()->user()->username??null,
                    "phone_number"=>auth()->user()->phone,
                    "customer_type_id"=>auth()->user()->customerType,
                    "otp"=>Controller::generateNumericOTP(4),
                    "token"=>$jwtoken??null,
                    "attempts_left"=>$app2FASetting->attempts,
                    "resends_left"=>$app2FASetting->resends,
                    "device_key"=>$request->header("deviceKey"),
                    "terminal"=> $terminal,
                    "expiry_date"=> Carbon::now()->addMinutes(15)->toDateTimeString(),
                    "status"=>TransactionStatusEnum::INIT->value,
                    "type"=>'APP'
                ]);

                NotificationService::send([
                    'tokens'=>$registerdDevices->map(function ($element,int $key){
                        if(isset($element->terminal->registrationToken)){
                            return $element->terminal->registrationToken;
                        }
                        return null;
                    })->filter()->unique()->toArray(),
                    'title'=>__("New device"),
                    'body'=>sprintf(trans("new_device_login_message"),
                $partyVerify->terminal->model,
                        $request->header("x-forwarded-for")??""
                    ),
                    'type'=>'verification',
                    'extra_id'=>$partyVerify->id
                ]);

                abort(response([
                    "status"=> [
                        "result"=> "EXPECTATION_FAILED",
                        "referenceNumber"=> "",
                        "contextID"=> "",
                        "message"=> [
                            "code"=> "DIGX_AUTH_0003",
                            "type"=> "INFO"
                        ]
                    ],
                    //"firstLoginFlowDone"=> false
                ])
                ->withHeaders([
                    "Content-Type"=>"application/json",
                    "X-CHALLENGE"=>json_encode([
                        "authType"=>"APP",
                       // "otp"=>$partyVerify->otp,
                        "referenceNo"=>$partyVerify->id,
                        "attemptsLeft"=>$partyVerify->attempts_left,
                        "scope"=>"USERTASK",
                        "resendsLeft"=>$partyVerify->resends_left,
                    ]),
                ])
                ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_EXPECTATION_FAILED));
            }else{
                $errorResult->message->title=__("You need to login in to your account with physical device, before you can login from web!");
            }
        }

        $this->forgetAllQueuedCookies();
        abort(response($errorResult->toArray())
        ->setStatusCode(400));
    }
     /**
     * Get user identety card info
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public static function verify($request, Closure $next)
    {
        // if($request->hasHeader("token")){
        //     $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;
        //     if($app2FASetting->status){
        //         $partyVerify=PartyVerify::
        //             where('device_key',$request->header("deviceKey"))
        //             ->where('token',$request->header("token"))
        //             ->first();
        //         if(is_null($partyVerify)){

        //         }
        //     }
        // }


        if($request->hasHeader("X-CHALLENGE-RESPONSE") && $request->path()=="digx/v1/switch/me"){
            $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;
            if($app2FASetting->status){
                $errorResult=GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "ATTEMPTS-2FA",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "DIGX_TFA_0005",
                            "type"    => "ERROR"
                        ]
                    ]
                        ]);

                //{"authType":"OTP","otp":"4651","referenceNo":"142629"}
                $challenge=json_decode($request->header("X-CHALLENGE-RESPONSE"));

                $partyVerify=PartyVerify:://select("id","otp","attempts_left","resends_left","token","status")
                where('id',$challenge->referenceNo)
                //->where('party_id',auth()->user()->id)
                ->where('device_key',$request->header("deviceKey"))
                ->where('type',$challenge->authType)
                ->first();

                if(is_null($partyVerify) || Carbon::parse($partyVerify->expiry_date)->isBefore(Carbon::now())){
                    $errorResult->status->message->title=__("This verify session ended please try again!");
                    return abort(response($errorResult->toArray())
                    ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST));
                }
                $verified=false;
                if($partyVerify->attempts_left==0){
                    $errorResult->status->message->title=__("You raised the attempt count! please retry again later!");
                    if($partyVerify->status!=TransactionStatusEnum::ERROR->value){
                        $partyVerify->status=TransactionStatusEnum::ERROR->value;
                        $partyVerify->save();

                    }

                }else if($partyVerify->type=='OTP'){
                    if($partyVerify->status==TransactionStatusEnum::INIT->value && $partyVerify->otp==$challenge->otp){
                        $verified=true;
                    }else{
                        $errorResult->status->message->code="DIGX_TFA_0004";
                        $errorResult->status->message->title= __("The verification code is incorrect.");
                    }
                }else if($partyVerify->type=='APP'){
                    if($partyVerify->status==TransactionStatusEnum::PENDING->value){
                        $verified=true;
                    }else if($partyVerify->status==TransactionStatusEnum::CANCELED->value){
                        $errorResult->status->message->title=__("Your request was rejected!");
                    }else if($partyVerify->status==TransactionStatusEnum::INIT->value){
                        $errorResult->status->message->code="DIGX_TFA_0004";
                        $errorResult->status->message->title= __("Waiting for approval!");
                    }
                }
                // return abort(response($partyVerify->toArray())
                // ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST));
                if($verified){
                    $service=new LoginService(request());

                    if(!is_null($partyVerify->token)){
                        $service->registerDevice($partyVerify->terminal);
                    }

                    $partyVerifies=PartyVerify:://select("id","otp","attempts_left","resends_left","token","status")
                        //where('party_id',auth()->user()->id)
                        where('status',TransactionStatusEnum::COMPLETED->value)
                        //->where('device_key',$request->header("deviceKey"))
                        ->get();

                    $registerdDevices=PartyVerify::where('status',TransactionStatusEnum::COMPLETED->value)
                    ->where('device_key',$request->header("deviceKey"))
                    ->get();

                    if(!count($registerdDevices->toArray()) && count($partyVerifies->toArray())){
                        NotificationService::send([
                            'tokens'=>$partyVerifies->map(function ($element,int $key){
                                if(isset($element->terminal->registrationToken)){
                                    return $element->terminal->registrationToken;
                                }
                                return null;
                            })->filter()->unique()->toArray(),
                            'title'=>__("New device"),
                            'body'=>sprintf(trans("new_device_message"),
                                $partyVerify->terminal->brand
                            ),
                            'type'=>'operation',
                            'extra_id'=>$partyVerify->id
                        ]);
                    }
                    PartyVerify::where("device_key",$partyVerify->device_key)
                    ->where("status",TransactionStatusEnum::COMPLETED->value)
                    ->delete();

                    $partyVerify->status=TransactionStatusEnum::COMPLETED->value;
                    $partyVerify->save();

                    if(is_null($partyVerify->token)){
                        $service->requiredChangePassword($partyVerify);
                    }

                    return response(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   => "",
                                "detail"  => "",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ])->toArray())
                    ->withHeaders([
                        "Content-Type"=>"application/json",
                        //'set-cookie'=> static::getCookies(),
                        'jwtoken'=> $partyVerify->token,
                    ])
                    ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_FOUND);
                }else{

                    if($errorResult->status->message->code=="DIGX_TFA_0004"){
                        $partyVerify->attempts_left=$partyVerify->attempts_left-1;
                        $partyVerify->save();

                        $errorResult->status->contextID="VERFIY-USER-OTP";
                        return abort(response($errorResult->toArray())
                        ->withHeaders([
                            'X-CHALLENGE'=> json_encode([
                                "authType"=>"OTP",
                                "referenceNo"=>$partyVerify->id,
                                "attemptsLeft"=>$partyVerify->attempts_left,
                                "scope"=>"USERTASK",
                                "resendsLeft"=>$partyVerify->resends_left,
                            ])
                        ])
                        ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_EXPECTATION_FAILED));

                    }
                    return abort(response($errorResult->toArray())
                    ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST));
                }
            }

        }
        return null;
        //return response()->json($customer);
    }
    /**
     * resend otp again to user
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public static function resend($referenceNo)
    {
        $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;
        if($app2FASetting->status){

            $partyVerify=PartyVerify:://select("id","otp","attempts_left","resends_left","token","status")
            where('id',$referenceNo)
            ->where('party_id',auth()->user()->id)
            ->where('device_key',request()->header("deviceKey"))
            ->first();

            if($partyVerify->status==TransactionStatusEnum::INIT->value && $partyVerify->resends_left>0){


                $smsMessage=sprintf(trans("verify_otp_message"),
                $partyVerify->otp,
                    "15",
                    Controller::generateNumericOTP(2)."r0OeQ7JGReE"
                );

                NotificationService::sendSMS([
                    "mobile"=> $partyVerify->phone_number,
                    "message"=> $smsMessage
                ]);
                if(!empty(auth()->user()->email??"")){
                    NotificationService::email(auth()->user()->email,data:new OtpSent($partyVerify));
                }

                $partyVerify->resends_left--;
                $partyVerify->save();

                return response()->json(GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                )));
            }else{
                if($partyVerify->status!=TransactionStatusEnum::ERROR->value){
                    $partyVerify->status=TransactionStatusEnum::ERROR->value;
                    $partyVerify->save();

                }
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "ATTEMPTS-RESEND-OTP",
                        "message"   => [
                            "title"   => __("You raised the otp resend count! please retry again later!"),
                            "detail"  => "",
                            "code"    => "DIGX_TFA_0004",
                            "type"    => "ERROR"
                        ]
                    ]
                ]),\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST);
            }




        }
    }
     /**
     * Get user identety card info
     *
     * @param  string|object  $terminal
     * @return never|object|array|mixed
     */
    protected function registerDevice($terminal)
    {

        //$service=new LoginService(request());
        $fake=[];

        $terminal=is_string($terminal)?json_decode($terminal):$terminal;
        $terminal=collect($terminal);

        if(auth()->user()->customerType==CustomerType::GUEST || $terminal->get('os')=="Web"){
            if($terminal->has('registrationToken')){
                try{
                    $customer_type=CustomerType::find(auth()->user()->customerType);
                    if(!is_null($customer_type) && !is_null($customer_type->topic)){
                        NotificationService::subscribeToTopics([$customer_type->topic],[$terminal->get('registrationToken')]);
                    }
                }catch(\Exception $e){
                    \Log::error("[registerDevice]: subscribeToTopics");
                }
            }
            return null;
        }

        $response=$this->getHttpRequest('POST','mobileClient',
            collect($terminal->toArray())->except(['registrationId','package','osType','appVersionNumber','appVersionName'])->toArray()
        ,$fake);

        $result=$response->object();
        if($result->status->message->code=="0"){
            $this->getHttpRequest('DELETE','mobileClient/pushToken/os/'.$terminal->get('os'),[],$fake);
            if($terminal->has('registrationToken')){
                $terminal->put('pushAllowed',true);
                $this->getHttpRequest('POST','mobileClient/pushRegistration', collect($terminal->toArray())->except(['package','osType','appVersionNumber','appVersionName'])->toArray(),$fake);
                try{
                    $customer_type=CustomerType::find(auth()->user()->customerType);
                    if(!is_null($customer_type) && !is_null($customer_type->topic)){
                        NotificationService::subscribeToTopics([$customer_type->topic],[$terminal->get('registrationToken')]);
                    }
                }catch(\Exception $e){
                    \Log::error("[registerDevice]: subscribeToTopics");
                }
            }
            return $result;
        }

        return abort(response($response->getBody())
        ->withHeaders($response->headers())
        ->setStatusCode($response->status()));
    }

    public static function changePassword()
    {

        $request=request();
        $service=new LoginService($request);
        $fake=[];

        $partyVerify=PartyVerify:://select("id","otp","attempts_left","resends_left","token","status")
            where('id',$request->referenceNo)
            //->where('party_id',auth()->user()->id)
            ->where('device_key',$request->header("deviceKey"))
            ->first();

        if(!is_null($partyVerify) && $partyVerify->status==TransactionStatusEnum::COMPLETED->value && $partyVerify->token==null){
            $response=$service->getHttpRequest('PUT','me/credentials',[
                "oldPassword"=>$request->oldPassword,
                "changedPassword"=>$request->changedPassword
            ],$fake);

            $result=$response->object();
            if($result!=null && isset($result->passwordValidationRepsonseDTO->verificationStatus) &&
                $result->passwordValidationRepsonseDTO->verificationStatus==true){
                    //auth()->forgetGuards();
                    // if (auth('digx')->check()) {
                    //     auth()->shouldUse('digx');
                    // }
                    $response=$service->getHttpRequest('POST','jwt',[
                        "channelType"=>"MOBILEJWT",
                        "password"=>$request->changedPassword
                    ],$fake);

                    $result=$response->object();
                    if(isset($result->jwtoken)){
                        $partyVerify->token=$result->jwtoken;
                        $partyVerify->save();

                        $service->registerDevice($partyVerify->terminal);

                        return response(CustomerService::details($request))
                        ->withHeaders([
                            "Content-Type"=>"application/json",
                            //'set-cookie'=> static::getCookies(),
                            'jwtoken'=> $partyVerify->token,
                        ])
                        ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_FOUND);
                    }
            }else{
                return abort(response($response->getBody())
                ->withHeaders(collect($response->getHeaders())
                    ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
                    ->toArray()
                )
                ->setStatusCode($response->status()));
            }
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "CHANGE-PASSWORD",
                "message"   => [
                    "title"   => __("This verify session ended please try again!"),
                    "detail"  => "",
                    "code"    => "DIGX_CHANGE-PASSWORD_0001",
                    "type"    => "ERROR"
                ]
            ]
        ]),\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST);

    }

    /**
     * This API when user enter new password after verfiy OTP
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public static function changeCredentials()
    {

        $request=request();
        $service=new LoginService($request);
        $fake=[];

        $response=$service->getHttpRequest('PUT','users/changeCredentials',[
            "userId"=>$request->userId,
            "registrationId"=>$request->registrationId,
            "newPassword"=>$request->newPassword,
        ],$fake);

        $result=$response->object();
        if($result!=null && isset($result->userId) &&
            $result->userId==$request->userId){
                PartyVerify::where("device_key",$request->header("deviceKey"))
                ->where("status",TransactionStatusEnum::INIT->value)
                ->where("party_id",$request->userId)

                ->forceDelete();

                $terminal=json_decode(base64_decode($request->header("terminal")));
                $terminal->registrationId=$request->registrationId;
                $partyVerify=PartyVerify::create([
                    "party_id"=>$request->userId,
                    "phone_number"=>"",
                    "device_key"=>$request->header("deviceKey"),
                    "terminal"=>$terminal,
                    "expiry_date"=> Carbon::now()->addMinutes(15)->toDateTimeString(),
                    "status"=>TransactionStatusEnum::INIT->value
                ]);
                return response()->json([
                    "userId"=>$request->userId,
                    "registrationId"=>$partyVerify->id,
                ]);

                // auth()->logout();
                // if (auth('digx')->check()) {
                //     auth()->shouldUse('digx');
                // }
                // $response=$service->getHttpRequest('POST','jwt',[
                //     "channelType"=>"MOBILEJWT",
                //     "password"=>$request->newPassword
                // ],$fake);

                // $result=$response->object();
                // if(isset($result->jwtoken) || (isset($result->message->code) && $result->message->code=="DIGX_UM_042")){
                //     return $service->loginWithout2FA($result->jwtoken??null);
                // }
        }
        return abort(response($response->getBody())
        ->withHeaders(collect($response->getHeaders())
            ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
            ->toArray()
        )
        ->setStatusCode($response->status()));


    }


    /**
     * Get user identety card info
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public static function register()
    {

        $request=request();
        // foreach (request()->cookies->all() as $key => $value) {
        //     Cookie::make($key, $value);
        // }
        // return response([
        //     "set-cookie"=>$request->header('cookie'),
        //    // "headers"=> $request->headers->all(),
        //     //"firstLoginFlowDone"=> false
        // ])->withHeaders([
        //     "set-cookie"=>$request->header('cookie'),
        //    // "headers"=> $request->headers->all(),
        //     //"firstLoginFlowDone"=> false
        // ]);


        $service=new LoginService($request);
        $fake=[];
        // if(env('APP_ENV', 'production')!='production'){
        //     $fake=[
        //         "{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1/payments/transfers/peerToPeer/user" => Http::response([
        //             "status" => [
        //                 "result" => "SUCCESSFUL",
        //                 "contextID" => "005vGt9MG5O5uXS6yF2jMG0003NK003KPN,0&#x3a;1",
        //                 "message" => [
        //                    "code" => "0",
        //                    "type" => "Test"
        //                 ]
        //              ],
        //             "aliasValue" => "{$request->mobileNumber}",
        //             "aliasType" => "MOBILE",
        //             "uId" => "37262",
        //             "firstName" => "{$request->firstName}",
        //             "payeeStatus" => "PVN",
        //             "tokenValid" => false,
        //          ], 200, [])
        //     ];
        // }
        $userData=[
            "phoneNumber"=> $request->mobileNumber,
            "otprequired"=> true,
            "firstName"=>$request->firstName,
            "lastName"=> $request->lastName,
            "emailId"=> "",
            "password"=> "",
            "uId"=> "",
            "userId"=> $request->mobileNumber,
            "aliasType"=> "MOBILE",
            "aliasValue"=> $request->mobileNumber,
            "mobileNumber"=> $request->mobileNumber
        ];
        $response=$service->getHttpRequest('POST','payments/transfers/peerToPeer/user',$userData,$fake);

        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->uId)){

            // $token=json_decode(\Illuminate\Support\Facades\Route::dispatch($tokenRequest)->getContent(),true);
            $terminal=json_decode(base64_decode($request->header("terminal")));
            try {
                $signature = Crypt::encrypt(json_encode($userData));
                $terminal->signature=$signature;
            } catch (\Illuminate\Contracts\Encryption\EncryptException $e) {
                return;
            }

            $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;
            PartyVerify::where("device_key",$request->header("deviceKey"))->where("status",TransactionStatusEnum::INIT->value)->forceDelete();
            $partyVerify=PartyVerify::create([
                "username"=>$request->mobileNumber,
                "party_id"=>$request->mobileNumber,
                "phone_number"=>$request->mobileNumber,
                "customer_type_id"=>CustomerType::GUEST,
                "otp"=>Controller::generateNumericOTP(4),
                //"token"=>$token['access_token'],
                "attempts_left"=>$app2FASetting->attempts,
                "resends_left"=>$app2FASetting->resends,
                "device_key"=>$request->header("deviceKey"),
                "terminal"=>$terminal,
                "expiry_date"=> Carbon::now()->addMinutes(15)->toDateTimeString(),
                "status"=>TransactionStatusEnum::INIT->value,
                "type"=>"OTP"
            ]);
            $smsMessage=sprintf(trans("verify_otp_message"),
                $partyVerify->otp,
                "15",
                "r0OeQ7JGReE"
            );

            NotificationService::sendSMS([
                "mobile"=> $partyVerify->phone_number,
                "message"=> $smsMessage
            ]);
            // if($request->filled('emailId')){
            //     NotificationService::email(auth()->user()->email,data:new OtpSent($partyVerify));
            // }
            return response([
                "status"=> [
                    "result"=> "EXPECTATION_FAILED",
                    "referenceNumber"=> "",
                    "contextID"=> "",
                    "message"=> [
                        "code"=> "DIGX_AUTH_0003",
                        "type"=> "INFO"
                    ]
                ],
                //"firstLoginFlowDone"=> false
            ])
            ->withHeaders(collect($response->getHeaders())
                ->put("Content-Type","application/json")
                //->put('set-cookie', static::getCookies())
                ->put("X-CHALLENGE",json_encode([
                    "authType"=>"OTP",
                    "otp"=>env('APP_ENV', 'production')!='production'?$partyVerify->otp:'',
                    "signature"=>$terminal->signature,
                    "referenceNo"=>$partyVerify->id,
                    "attemptsLeft"=>$partyVerify->attempts_left,
                    "scope"=>"USERTASK",
                    "resendsLeft"=>$partyVerify->resends_left,
                ]))
                ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
                ->toArray()
            )
            ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_EXPECTATION_FAILED);

        }else{
            return response($response->getBody())
            ->withHeaders(collect($response->getHeaders())
                //->put('set-cookie', static::getCookies())
                ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
                ->toArray()
            )
            ->setStatusCode($response->status());
        }
    }
    // private function encryptData($key,$plaintext){
    //     $method ='aes-256-cbc';
    //     $iv =chr(0x0). chr(0x0). chr(0x0). chr(0x0). chr(0x0). chr(0x0). chr(0x0).chr(0x0)
    //             .chr(0x0). chr(0x0). chr(0x0). chr(0x0). chr(0x0). chr(0x0). chr(0x0).chr(0x0);

    //     return base64_encode(openssl_encrypt($plaintext,$method,$key, OPENSSL_RAW_DATA,$iv));
    // }

     /**
     * Get user identety card info
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public static function verifyRegister()
    {
        if(request()->hasHeader("X-CHALLENGE-RESPONSE")){
            $app2FASetting=app(\App\Settings\ConfigSettings::class)->appConfig->app2FASetting;
                //{"authType":"OTP","otp":"4651","referenceNo":"142629"}
                $challenge=json_decode(request()->header("X-CHALLENGE-RESPONSE"));

                $partyVerify=PartyVerify::withoutGlobalScope(CustomerScope::class)
                ->withoutGlobalScope(UsernameScope::class)//select("id","otp","attempts_left","resends_left","token","status")
                ->where('id',$challenge->referenceNo)
                //->where('token',$challenge->signature)
                ->where('customer_type_id',CustomerType::GUEST)
                ->where('device_key',request()->header("deviceKey"))
                ->first();



                if($partyVerify->status==TransactionStatusEnum::INIT->value && $partyVerify->attempts_left>0 && $partyVerify->otp==$challenge->otp && $challenge->signature==$partyVerify->terminal->signature){
                    $service=new LoginService(request());


                    // if(!is_null($partyVerify->token)){
                    //     $service->registerDevice($partyVerify->terminal);
                    // }

                    $fake=[];
                    // if(env('APP_ENV', 'production')!='production'){
                    //     $fake=[
                    //         "{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1/payments/transfers/peerToPeer/user?type=MOBILE&value={$partyVerify->party_id}" => Http::response([
                    //             "status" => [
                    //                 "result" => "SUCCESSFUL",
                    //                 "contextID" => "005vGt9MG5O5uXS6yF2jMG0003NK003KPN,0&#x3a;1",
                    //                 "message" => [
                    //                    "code" => "0",
                    //                    "type" => "Test"
                    //                 ]
                    //             ],
                    //              "globalPayee" => [
                    //                 "aliasValue" => "{$partyVerify->party_id}",
                    //                 "aliasType" => "MOBILE",
                    //                 "payeeStatus" => "PVN",
                    //                 "uId" => "37262",
                    //             ],
                    //          ], 200, [])
                    //     ];
                    // }
                    $response=$service->getHttpRequest('GET','payments/transfers/peerToPeer/user',[
                        "type"=> "MOBILE",
                        "value"=> $partyVerify->party_id,
                    ],$fake);

                    $result=$response->object();
                    if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->globalPayee->uId)){
                        PartyVerify::where("device_key",$partyVerify->device_key)
                        ->where("status",TransactionStatusEnum::PENDING->value)
                        ->delete();

                        $partyVerify->status=TransactionStatusEnum::PENDING->value;
                        $partyVerify->save();

                        // abort(response(StatusData::from(array(
                        //     "result"    => "SUCCESSFUL",
                        //     "contextID" => "OBDX-LOGIN",
                        //     "message"   => [
                        //         "title"   => "Password change required.",
                        //         "detail"  => "Password change required.",
                        //         "code"    => "DIGX_UM_042",
                        //         "type"    => "ERROR"
                        //     ]
                        // ))->toArray())
                        // ->withHeaders([
                        //     "referenceNo"=>$partyVerify->id,
                        //     "userId"=>$result->globalPayee->uId,
                        //     "referenceNo"=>$partyVerify->id
                        // ])
                        // ->setStatusCode(400));

                        return response(StatusData::from(array(
                            "result"    => "SUCCESSFUL",
                            "contextID" => "OBDX-LOGIN",
                            "message"   => [
                                "title"   => "Password change required.",
                                "detail"  => "Password change required.",
                                "code"    => "0",
                                "type"    => "ERROR"
                            ]
                        ))->additional([
                            "data"=>[
                                "userId"=>$result->globalPayee->uId,
                                "registrationId"=>$partyVerify->id
                            ]
                        ])->toArray());

                    }

                    return response($response->getBody())
                    ->withHeaders(collect($response->getHeaders())
                        //->put('set-cookie', static::getCookies())
                        ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
                        ->toArray()
                    )
                    ->setStatusCode($response->status());
                }else{

                    if($partyVerify->attempts_left==0){
                        if($partyVerify->status!=TransactionStatusEnum::ERROR->value){
                            $partyVerify->status=TransactionStatusEnum::ERROR->value;
                            $partyVerify->save();

                        }
                        return abort(response(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "ERROR",
                                "contextID" => "ATTEMPTS-2FA",
                                "message"   => [
                                    "title"   => __("You raised the attempt count! please retry again later!"),
                                    "detail"  => "",
                                    "code"    => "DIGX_TFA_0005",
                                    "type"    => "ERROR"
                                ]
                            ]
                        ])->toArray())
                        ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST));
                    }

                    $partyVerify->attempts_left=$partyVerify->attempts_left-1;
                    $partyVerify->save();

                    return abort(response(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "VERFIY-USER-OTP",
                            "message"   => [
                                "title"   => __("The verification code is incorrect."),
                                "detail"  => "",
                                "code"    => "DIGX_TFA_0004",
                                "type"    => "ERROR"
                            ]
                        ]
                    ])->toArray())
                    ->withHeaders([
                        'X-CHALLENGE'=> json_encode([
                            "authType"=>"OTP",
                            "referenceNo"=>$partyVerify->id,
                            "attemptsLeft"=>$partyVerify->attempts_left,
                            "scope"=>"USERTASK",
                            "resendsLeft"=>$partyVerify->resends_left,
                        ])
                    ])
                    ->setStatusCode(\Symfony\Component\HttpFoundation\Response::HTTP_EXPECTATION_FAILED));

                }


        }
        return null;
        //return response()->json($customer);
    }
    /**
     * Get user identety card info
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public static function passwordRegister()
    {

        $request=request();
        $service=new LoginService($request);
        $fake=[];

        $partyVerify=PartyVerify::withoutGlobalScope(CustomerScope::class)
        ->withoutGlobalScope(UsernameScope::class)//select("id","otp","attempts_left","resends_left","token","status")
        ->where('id',$request->referenceNo)
        //->where('token',$challenge->signature)
        ->where('customer_type_id',CustomerType::GUEST)
        ->where('device_key',$request->header("deviceKey"))
        ->first();

        $service=new LoginService(request());

        $date=Carbon::parse($partyVerify->expiry_date)->addMinutes(10);
        if($partyVerify->status==TransactionStatusEnum::PENDING->value &&  $date->isAfter(Carbon::now()) && $partyVerify->token==null && $request->signature==$partyVerify->terminal->signature){
            $request=request();
            $service=new LoginService($request);


            $fake=[];
            // if(env('APP_ENV', 'production')!='production'){
            //     $fake=[
            //         "{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1/payments/transfers/peerToPeer/user?type=MOBILE&value={$partyVerify->party_id}" => Http::response([
            //             "status" => [
            //                 "result" => "SUCCESSFUL",
            //                 "contextID" => "005vGt9MG5O5uXS6yF2jMG0003NK003KPN,0&#x3a;1",
            //                 "message" => [
            //                    "code" => "0",
            //                    "type" => "Test"
            //                 ]
            //             ],
            //              "globalPayee" => [
            //                 "aliasValue" => "{$partyVerify->party_id}",
            //                 "aliasType" => "MOBILE",
            //                 "payeeStatus" => "PVN",
            //                 "uId" => "37262",
            //             ],
            //          ], 200, [])
            //     ];
            // }
            $response=$service->getHttpRequest('GET','payments/transfers/peerToPeer/user',[
                "type"=> "MOBILE",
                "value"=> $partyVerify->party_id,
            ],$fake);

            $result=$response->object();
            if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->globalPayee->uId) && $result->globalPayee->uId==$request->userId){
                try {
                    $userData = json_decode(Crypt::decrypt($partyVerify->terminal->signature));
                } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
                    return;
                }

                $userData->password=$request->newPassword;
                $userData->uId=$result->globalPayee->uId;

                $fake=[];
                // if(env('APP_ENV', 'production')!='production'){
                //     $fake=[
                //         "{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1/payments/transfers/peerToPeer/user/authentication" => Http::response([
                //             "status" => [
                //                 "result" => "SUCCESSFUL",
                //                 "contextID" => "005vGt9MG5O5uXS6yF2jMG0003NK003KPN,0&#x3a;1",
                //                 "message" => [
                //                    "code" => "0",
                //                    "type" => "Test"
                //                 ]
                //             ],
                //             "payeeStatus" => "U",
                //          ], 200, [])
                //     ];
                // }
                $response=$service->getHttpRequest('PATCH','payments/transfers/peerToPeer/user/authentication',collect($userData)->toArray(),$fake);

                $result=$response->object();
                if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->payeeStatus) && $result->payeeStatus=='U'){
                        PartyVerify::where("device_key",$request->header("deviceKey"))
                        ->where("status",TransactionStatusEnum::INIT->value)
                        ->where('customer_type_id',CustomerType::GUEST)
                        ->where("party_id",$partyVerify->party_id)
                        ->forceDelete();

                        $terminal=$partyVerify->terminal;
                        $terminal->registrationId=$userData->uId;
                        $terminal->userData=null;
                        $partyVerify->terminal=$terminal;
                        $partyVerify->status=TransactionStatusEnum::INIT->value;
                        $partyVerify->save();

                        return response()->json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "ERROR",
                                "contextID" => "CHANGE-PASSWORD",
                                "message"   => [
                                    "title"   => __("Success change password"),
                                    "detail"  => "",
                                    "code"    => "0",
                                    "type"    => "INFO"
                                ]
                            ]
                        ])->additional([
                            "data"=>[
                                "userId"=>$request->userId,
                                "registrationId"=>$partyVerify->id,
                            ]
                        ]));


                }
            }
            return abort(response($response->getBody())
                ->withHeaders(collect($response->getHeaders())
                    ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location'])
                    ->toArray()
                )
                ->setStatusCode($response->status()));
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "CHANGE-PASSWORD",
                "message"   => [
                    "title"   => __("Un"),
                    "detail"  => "",
                    "code"    => "DIGX_CHANGE-PASSWORD_0001",
                    "type"    => "ERROR"
                ]
            ]
        ]),\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST);

    }

}
