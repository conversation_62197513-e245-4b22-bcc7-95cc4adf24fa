<?php

namespace App\Enums;

enum HarvestStatusEnum: int
{
    case PENDING = 0;
    case COMPLETED = 1;
    case FAILED = -1;
    case MANUAL_PENDING = 2;
    case MANUAL_VERIFICATION = 3;      // Stage 1: Customer verification
    case MANUAL_DATA_INPUT = 4;        // Stage 2: Remittance data input
    case MANUAL_REVIEW = 5;            // Stage 3: Final review
    case MANUAL_APPROVED = 6;          // Stage 4: Approved for execution
    case MANUAL_COMPLETED = 8;         // Stage 5: Manual processing completed
    case MANUAL_REJECTED = -2;         // Manual processing rejected
    case CANCELLED = -3;

    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pending',
            self::COMPLETED => 'Completed',
            self::FAILED => 'Failed',
            self::MANUAL_PENDING => 'Manual Processing Pending',
            self::MANUAL_VERIFICATION => 'Manual Stage 1 - Customer Verification',
            self::MANUAL_DATA_INPUT => 'Manual Stage 2 - Data Input',
            self::MANUAL_REVIEW => 'Manual Stage 3 - Review',
            self::MANUAL_APPROVED => 'Manual Stage 4 - Approved',
            self::MANUAL_COMPLETED => 'Manual Processing Completed',
            self::MANUAL_REJECTED => 'Manual Processing Rejected',
            self::CANCELLED => 'Cancelled',
        };
    }

    public function description(): string
    {
        return match($this) {
            self::PENDING => 'Transaction is pending automated processing',
            self::COMPLETED => 'Transaction completed successfully',
            self::FAILED => 'Transaction failed during automated processing',
            self::MANUAL_PENDING => 'Transaction submitted for manual processing',
            self::MANUAL_VERIFICATION => 'Customer verification and data validation',
            self::MANUAL_DATA_INPUT => 'Remittance details input and verification',
            self::MANUAL_REVIEW => 'Final review and compliance checks',
            self::MANUAL_APPROVED => 'Approved and ready for execution',
            self::MANUAL_COMPLETED => 'Manual processing completed successfully',
            self::MANUAL_REJECTED => 'Manual processing failed or rejected',
            self::CANCELLED => 'Transaction was cancelled',
        };
    }

    public function getDescription(): string
    {
        return $this->description();
    }

    public function isManual(): bool
    {
        return in_array($this, [
            self::MANUAL_PENDING,
            self::MANUAL_VERIFICATION,
            self::MANUAL_DATA_INPUT,
            self::MANUAL_REVIEW,
            self::MANUAL_APPROVED,
            self::MANUAL_COMPLETED,
            self::MANUAL_REJECTED,
        ]);
    }

    public function isCompleted(): bool
    {
        return in_array($this, [
            self::COMPLETED,
            self::MANUAL_COMPLETED,
        ]);
    }

    public function isFailed(): bool
    {
        return in_array($this, [
            self::FAILED,
            self::MANUAL_REJECTED,
            self::CANCELLED,
        ]);
    }

    public function isPending(): bool
    {
        return in_array($this, [
            self::PENDING,
            self::MANUAL_PENDING,
            self::MANUAL_VERIFICATION,
            self::MANUAL_DATA_INPUT,
            self::MANUAL_REVIEW,
            self::MANUAL_APPROVED,
        ]);
    }

    public static function getManualStages(): array
    {
        return [
            self::MANUAL_VERIFICATION,
            self::MANUAL_DATA_INPUT,
            self::MANUAL_REVIEW,
            self::MANUAL_APPROVED,
            self::MANUAL_COMPLETED,
        ];
    }

    /**
     * Find enum case by integer value
     */
    public static function findByValue(int $value): ?HarvestStatusEnum
    {
        return collect(HarvestStatusEnum::cases())->filter(function($item) use($value){
            return $item->value === $value;
        })->first();
    }

    /**
     * Get all enum values as array
     */
    public static function values(): array
    {
        return collect(static::cases())->pluck("value")->toArray();
    }

    /**
     * Check if status is manual processing status
     */
    public static function isManualStatus(int $status): bool
    {
        return $status >= 2 && $status <= 8 && $status !== 7; // Manual statuses: 2,3,4,5,6,8
    }

    /**
     * Check if status is automated processing status
     */
    public static function isAutomatedStatus(int $status): bool
    {
        return in_array($status, [-1, 0, 1]);
    }

    /**
     * Get stage number for manual processing statuses
     */
    public function getStage(): ?int
    {
        return match($this) {
            self::MANUAL_VERIFICATION => 1,
            self::MANUAL_DATA_INPUT => 2,
            self::MANUAL_REVIEW => 3,
            self::MANUAL_APPROVED => 4,
            self::MANUAL_COMPLETED => 5,
            default => null,
        };
    }
}
