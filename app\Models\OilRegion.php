<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
class OilRegion extends Model
{
    use HasFactory;

    protected $fillable = ['name','account_id','types','branches','status'];
    protected $casts = [
        'name'          => 'object',
        'types'       => 'array',
        'branches'       => 'array',
        'created_at'    => 'datetime:Y-m-d H:i:s',
        'updated_at'    => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    public function getName()
    {
        $local=app()->getLocale();
        return $this->name->{"$local"};
    }
}
