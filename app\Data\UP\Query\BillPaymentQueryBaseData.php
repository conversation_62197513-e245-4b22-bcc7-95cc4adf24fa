<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\BaseNonNullableDataCollection;
use App\Data\CurrencyAmountData;
use App\Data\NameData;
use App\Data\StatusData;
use App\Enums\BillPaymentServiceCodeEnum;
use Lang;
use Spatie\LaravelData\Attributes\MapInputName;


abstract class BillPaymentQueryBaseData extends BaseNonNullableData
{
    public function __construct(
        public ?StatusData $status=null,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['status']= [
            "message"=>[
                "title"=>data_get($properties,"MSG"),
                "code"=>data_get($properties,"RC","Error"),
            ]
        ];
        return $properties;
    }
    public static function fromArray(BillPaymentServiceCodeEnum $serviceCode,$properties) : ?BillPaymentQueryBaseData
    {
        switch ($serviceCode) {
            case BillPaymentServiceCodeEnum::yemenMobile:
                return BillPaymentQueryYemenMobileData::from($properties);
            case BillPaymentServiceCodeEnum::adsl:
                return BillPaymentQueryAdslData::from($properties);
            case BillPaymentServiceCodeEnum::landPhone:
                return BillPaymentQueryLandlineData::from($properties);
            case BillPaymentServiceCodeEnum::yemen4G:
                return BillPaymentQueryYemen4GData::from($properties);
            case BillPaymentServiceCodeEnum::waterUtility:
                return BillPaymentQueryWaterData::from($properties);
            case BillPaymentServiceCodeEnum::electricity:
                return BillPaymentQueryElectricityData::from($properties);
            case BillPaymentServiceCodeEnum::electricityCommercial:
                return BillPaymentQueryComElectricityData::from($properties);
            case BillPaymentServiceCodeEnum::government:
                return BillPaymentQueryGovernmentData::from($properties);
            case BillPaymentServiceCodeEnum::education:
                return BillPaymentQueryEducationData::from($properties);

        }

        return null;
    }
    public function toAppResponse():?BillPaymentQueryResponseData{
        if( $this->status->message->code!="0"){
            return new BillPaymentQueryResponseData(
                status: $this->status,
                items:is_null($this->status->message->title)?null:BillPaymentQueryResponseItemData::collect([
                    new BillPaymentQueryResponseItemData(
                        foregroundColor: "#ffffff",
                        backgroundColor: "#b00020",
                        item:new BillPaymentQueryResponseSubitemItemData(
                            foregroundColor: "#ffffff",
                            title:new NameData(
                                ar:Lang::get('Failed', locale: 'ar'),
                                en:"Failed"
                            ),
                        ),
                        subitem:new BillPaymentQueryResponseSubitemItemData(
                            foregroundColor: "#ffffff",
                            title:new NameData(
                                ar:$this->status->message->title,
                                en:$this->status->message->title
                            ),
                        )

                    )
                ], BaseNonNullableDataCollection::class)
            );
        }
        return null;
    }
}
