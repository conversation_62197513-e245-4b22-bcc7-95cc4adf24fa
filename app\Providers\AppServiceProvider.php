<?php

namespace App\Providers;

use App\Helpers\BaseHelper;
use App\Helpers\JsonCamel\JsonCamelHelper;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Laravel\Passport\Passport;
use Illuminate\Pagination\Paginator;
use App\Order;
use App\Helpers\ProxyHelper; //or your path
use Illuminate\Support\Facades\Http;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //Passport::ignoreMigrations();
        Passport::enablePasswordGrant();
        //\Laravel\Telescope\Telescope::ignoreMigrations();
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
        $this->app->bind('ProxyHelper', function($app) {
            return new ProxyHelper();
        });
        $this->app->bind('JsonCamelHelper', function($app) {
            return new JsonCamelHelper();
        });

       // Passport::useClientModel('App\Client');
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {

        //if (!\Request::isSecure() && str_contains(\Request::getHttpHost(),"banky")) {
        if (!\Request::isSecure() && (in_array(\Request::getPort(),["4437","4432","4433","4438"])|| str_contains(\Request::getHttpHost(),"blite-portal"))) {
           // \Illuminate\Support\Facades\URL::forceSchema('https');
           $this->app['request']->server->set('HTTPS', true);
        }
        Schema::defaultStringLength(191);
        // $this->app->singleton('BaseHelper', function(){
        //     return new BaseHelper();
        // });
        Http::globalRequestMiddleware(function (\GuzzleHttp\Psr7\Request $request) {
            return \App\Providers\AppServiceProvider::httpInterceptor($request);
        });
    }

    /**
     * Middleware that applies a map function to the request before passing to
     * the next handler.
     *
     * @param \GuzzleHttp\Psr7\Request|\Psr\Http\Message\RequestInterface $request Function that accepts a RequestInterface and returns
     *                     a RequestInterface.
     */
    public static function httpInterceptor($request): \GuzzleHttp\Psr7\Request|\Psr\Http\Message\RequestInterface
    {
        try {
            if(env('MIDDELWARE_URL')!=null){
                $parsedUrl = [
                    'scheme' => $request->getUri()->getScheme(),
                    'host' => $request->getUri()->getHost(),
                    'port' => $request->getUri()->getPort(),
                    'path' => $request->getUri()->getPath()
                ];

                // Modify the URL
                //$parsedUrl = parse_url( $request->url());

                // Set the modified URL back
                $middlewareUrl = parse_url( env('MIDDELWARE_URL'));
                $modifiedUrl=
                $request->getUri()->withScheme($middlewareUrl['scheme'])
                ->withHost($middlewareUrl['host'])
                ->withPort($middlewareUrl['port']);
                //  env('MIDDELWARE_URL').$parsedUrl['path'];
                $request=$request->withUri($modifiedUrl);

                // Modify headers (e.g., add an Authorization header)
                $request=$request->withHeader(
                    'X-Server-Name' ,[
                        $parsedUrl['scheme'] . '://' . $parsedUrl['host'] .(empty($parsedUrl['port'])?'': (':' . $parsedUrl['port']))
                    ]
                );

                // $request=$request->withAddedHeader(
                //     'Host' ,[
                //         $middlewareUrl['host']
                //     ]
                // );
                // You can also log the modified URL and headers if needed
                // \Log::error('Modified HTTP request:', [
                //     'modified_url' =>$request->getUri()->__toString(),
                //     'headers' => $request->getHeaders(),
                // ]);
                return $request;
            }
        } catch (\Exception $e) {
            \Log::error('Error in beforeSending: ' . $e->getMessage());
        }
        return $request;
    }
}
