<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RemittanceNetwork extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'service_code_id',
        'tracking_code_length',
        'tracking_code_prefix',
        'tracking_code_pattern',
        'icon',
        'status',
        'is_auto',
        'notes'
    ];

    protected $casts = [
        'status' => 'boolean',
        'is_auto' => 'boolean',
        'tracking_code_length' => 'integer'
    ];

    /**
     * Get the service code for this network
     */
    public function serviceCode(): BelongsTo
    {
        return $this->belongsTo(HarvestServiceCode::class, 'service_code_id');
    }

    /**
     * Get the harvests for this network through the service code
     */
    public function harvests(): HasMany
    {
        return $this->hasMany(Harvest::class, 'service_code_id', 'service_code_id');
    }

    /**
     * Check if a tracking code matches this network's pattern
     */
    public function matchesTrackingCode(string $trackingCode): bool
    {
        // Basic validation - tracking code should be at least 6 characters
        if (strlen($trackingCode) < 6) {
            return false;
        }

        // Check length - allow some flexibility (±1 character) for edge cases
        $trackingLength = strlen($trackingCode);
        $expectedLength = $this->tracking_code_length;

        if ($expectedLength && abs($trackingLength - $expectedLength) > 1) {
            return false;
        }

        // Check prefix if specified - simple prefix matching
        if ($this->tracking_code_prefix) {
            $prefixLength = strlen($this->tracking_code_prefix);

            // Make sure tracking code is long enough for prefix
            if ($trackingLength < $prefixLength) {
                return false;
            }

            $trackingPrefix = substr($trackingCode, 0, $prefixLength);

            if ($trackingPrefix !== $this->tracking_code_prefix) {
                return false;
            }
        }

        // Check pattern if specified with error handling
        if ($this->tracking_code_pattern) {
            try {
                if (!preg_match($this->tracking_code_pattern, $trackingCode)) {
                    return false;
                }
            } catch (\Exception $e) {
                // If regex fails, skip pattern matching and log warning
                \Log::warning("Invalid regex pattern for network {$this->name}: " . $e->getMessage());
                return false;
            }
        }

        return true;
    }

    /**
     * Scope for active networks
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for automated networks
     */
    public function scopeAutomated($query)
    {
        return $query->where('is_auto', true);
    }

    /**
     * Scope for manual networks
     */
    public function scopeManual($query)
    {
        return $query->where('is_auto', false);
    }

    /**
     * Get networks that support automated processing
     */
    public static function getAutomatedNetworks()
    {
        return static::active()->automated()->get();
    }

    /**
     * Get networks that require manual processing
     */
    public static function getManualNetworks()
    {
        return static::active()->manual()->get();
    }

    /**
     * Find network by tracking code pattern
     */
    public static function findByTrackingCode(string $trackingCode): ?RemittanceNetwork
    {
        $networks = static::active()->get();
        
        foreach ($networks as $network) {
            if ($network->matchesTrackingCode($trackingCode)) {
                return $network;
            }
        }

        return null;
    }

    /**
     * Get all available networks for admin
     */
    public static function getAllNetworks(): array
    {
        return RemittanceNetwork::with('serviceCode')
            ->orderBy('name')
            ->get()
            ->map(function ($network) {
                return [
                    'id' => $network->id,
                    'name' => $network->name,
                    'code' => $network->code,
                    'is_auto' => $network->is_auto,
                    'status' => $network->status,
                    'tracking_code_length' => $network->tracking_code_length,
                    'tracking_code_prefix' => $network->tracking_code_prefix,
                    'service_code_name' => $network->serviceCode?->name,
                    'service_code_id' => $network->service_code_id,
                    'processing_method' => $network->is_auto ? 'Automated' : 'Manual'
                ];
            })
            ->toArray();
    }

    /**
     * Get statistics for networks
     */
    public static function getNetworkStatistics(): array
    {
        $networks = RemittanceNetwork::withCount('harvests')->with('serviceCode')->get();

        return [
            'total_networks' => $networks->count(),
            'automated_networks' => $networks->where('is_auto', true)->count(),
            'manual_networks' => $networks->where('is_auto', false)->count(),
            'active_networks' => $networks->where('status', true)->count(),
            'networks' => $networks->map(function ($network) {
                return [
                    'name' => $network->name,
                    'harvests_count' => $network->harvests_count,
                    'service_code_name' => $network->serviceCode?->name,
                    'processing_method' => $network->is_auto ? 'Automated' : 'Manual',
                    'status' => $network->status ? 'Active' : 'Inactive'
                ];
            })
        ];
    }
}
