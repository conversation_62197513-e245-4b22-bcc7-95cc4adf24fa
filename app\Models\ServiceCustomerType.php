<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;

class ServiceCustomerType extends Pivot
{
    use HasFactory;
    protected $table = 'service_customer_types';

    protected $fillable = [
        "service_id",
        "customer_type_id",
        "page_service_id",
        "status",
        "status_type"
    ];
    // Example additional relationship on the pivot
    public function customerRoles()
    {
       // dd('Pivot:', $this->service_id, $this->customer_type_id);

         return $this->hasMany(ServiceCustomerRole::class, 'service_id', 'service_id')
         ->where('service_customer_roles.customer_type_id',$this->customer_type_id);

        //  ->where('service_customer_roles.customer_type_id', $this->customer_type_id)->select("service_id",
        // "customer_type_id","role_identifer");//->dd();
       // return $this->belongsToMany(ServiceCustomerRole::class, 'service_customer_roless','customer_type_id','customer_type_id')
    }

    public function customerRole()
    {
         return $this->hasMany(ServiceCustomerRole::class, 'service_id', 'service_id')
         ->whereColumn('service_customer_roles.customer_type_id', 'service_customer_types.customer_type_id');
    }
}
