<?php

namespace App\Http\Middleware;

use App;
use App\Data\StatusData;
use Closure;

class AppVersionMiddleware
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string[]  ...$guards
     * @return mixed
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards)
    {
        $appVersionSetting=app(\App\Settings\ConfigSettings::class)->appConfig->appVersionSetting;
        if (!is_null($appVersionSetting) && $request->hasHeader("x-os-type") && $request->hasHeader("appVersion")) {
            $os=$request->header("x-os-type");
            $type=$appVersionSetting->types->filter(function($type)use($os){
                return $type->os==$os && $type->status==1;
            })->first();
            if (!is_null($type) && $type->version>($request->header('appVersion')??10000)) {
                $local=App::getLocale();
                return response()->json(StatusData::from([
                    "result"    => "SUCCESSFUL",
                    "contextID" => "MIDDLEWARE",
                    "message"   => [
                        "title"   => $appVersionSetting->message->{"$local"},
                        "detail"  => $type->link,
                        "code"    => "DIGX_SWITCH_FU_0001",
                        "type"    => "ERROR"
                    ]
                ]),\Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST);
            }
        }
        return $next($request);
    }

}
