<?php

namespace App\Data;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class CustomCardData extends Data
{
    #[MapInputName('relationshipNumber')]
    public ?string $cardNumber;
    public ?string $classification;
    public ?string $provider;

    public ?string $name;

    public function with():array {
        $functions=[];
        if($this->classification=="VC"){
            $functions=[/*"reload","status",*/"balance","history","delete"];
        }else{
            $functions=["delete"];
        }
        return [
            'walletCurrency'=>"USD",
            'product'=>"Prepaid",
            "functions"=>$functions
        ];
    }
    public static function prepareForPipeline(array $properties) : array
    {
        $properties['name']= html_entity_decode($properties["nickName"]??"");
        $properties['classification']= $properties["billerId"]=="MASTERCARD"?"VC":"CSC";
        $properties['provider']= $properties["billerId"]=="MASTERCARD"?"mastercard":"visa";
        return $properties;
    }

}
