<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
            return redirect()->route('login');
        }

        // Check if user has admin role or permissions
        $user = auth()->user();
        
        // You can customize this logic based on your user model structure
        // Example checks:
        
        // Option 1: Check for admin role
        if (method_exists($user, 'hasRole') && $user->hasRole('admin')) {
            return $next($request);
        }
        
        // Option 2: Check for specific permission
        if (method_exists($user, 'can') && $user->can('manage_manual_harvests')) {
            return $next($request);
        }
        
        // Option 3: Check user type/role field
        if (property_exists($user, 'role') && $user->role === 'admin') {
            return $next($request);
        }
        
        // Option 4: Check if user is in admin group (customize based on your needs)
        if (property_exists($user, 'customerRole') && in_array($user->customerRole, ['admin', 'super_admin', 'manager'])) {
            return $next($request);
        }
        
        // Option 5: Check specific user IDs (for development/testing)
        if (in_array($user->id, config('app.admin_user_ids', []))) {
            return $next($request);
        }

        // If none of the above conditions are met, deny access
        if ($request->expectsJson()) {
            return response()->json(['error' => 'Forbidden - Admin access required'], 403);
        }
        
        abort(403, 'Admin access required');
    }
}
