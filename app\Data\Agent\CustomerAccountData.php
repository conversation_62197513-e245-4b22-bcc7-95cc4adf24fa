<?php

namespace App\Data\Agent;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class CustomerAccountData extends Data
{
    #[MapInputName('Customer_Account_No')]
    public ?string $id;

    #[MapInputName('Name')]
    public ?string $name;

    #[MapInputName('Branch_Code')]
    public ?string $branch;

}
