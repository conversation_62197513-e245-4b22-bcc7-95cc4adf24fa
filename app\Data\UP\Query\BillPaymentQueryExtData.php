<?php

namespace App\Data\UP\Query;

use App\Casts\FlexibleDateCast;
use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\StatusData;
use Carbon\Carbon;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithCastAndTransformer;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Casts\DateTimeInterfaceCast;
use Spatie\LaravelData\Transformers\DateTimeInterfaceTransformer;


class BillPaymentQueryExtData extends BaseNonNullableData
{
    public function __construct(
        public ?string $studentNumber = null,
        public ?string $studentName = null,
        public ?string $feeName = null,

        public ?string $billType = null,
        public ?string $subscriberName = null,
        public ?string $packageBalance = null,
        public ?string $billingNo = null,
        public ?float $highestToPay = null,
        #[WithCast(DateTimeInterfaceCast::class, "YmdHis"),WithTransformer(DateTimeInterfaceTransformer::class, format: "Y-m-d H:i:s")]
        public ?Carbon $expiryDate = null,
        public ?string $billStatus = null,
        #[WithCast(FlexibleDateCast::class),WithTransformer(DateTimeInterfaceTransformer::class, format: "Y-m-d H:i:s")]
        public ?Carbon $issueDate = null,
        public ?string $serviceType = null,
        public ?string $message = null,
        public ?string $billNo = null,
        public ?float $feeAmount = null,
        public ?float $dueAmount = null,
        public ?string $allowOver = null,
        #[WithCast(DateTimeInterfaceCast::class, "YmdHis"),WithTransformer(DateTimeInterfaceTransformer::class, format: "Y-m-d H:i:s")]
        public ?Carbon $openDate = null,
        #[WithCast(DateTimeInterfaceCast::class, "YmdHis"),WithTransformer(DateTimeInterfaceTransformer::class, format: "Y-m-d H:i:s")]
        public ?Carbon $dueDate = null,
        public ?float $lowestToPay = null,
        public ?string $allowPart = null,
        public ?string $packageId = null,
        public ?string $dataBalance = null,

        public ?string $subscriberMobile = null,


        // public ?string $billNo = null,
        // public ?float $lowestToPay = null,
        // public ?string $lowestToPay = null,


    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {

        $properties['billingNo']= data_get($properties,"billingNo",data_get($properties,"subscriberNo"));
        $properties['subscriberName']= data_get($properties,"subscribename",data_get($properties,"subscriberName",data_get($properties,"StudArName",data_get($properties,"FeeName",data_get($properties,"payer_name")))));
        $properties['dueAmount']= data_get($properties,"dueAmount",data_get($properties,"subscriberBal",data_get($properties,"Total",data_get($properties,"Debit",data_get($properties,"amount")))));
        $properties['subscriberMobile']= data_get($properties,"subscriberMobile",data_get($properties,"Mobile"));

        $properties['studentNumber']=  data_get($properties,"RollNo");
        $properties['studentName']=  data_get($properties,"StudArName");
        $properties['feeName']= data_get($properties,"FeeName");

        $properties['message']= data_get($properties,"message",data_get($properties,"unit_name"));
        $properties['issueDate']= data_get($properties,"issueDate",data_get($properties,"revenue_command_date"));


        return $properties;
    }

}
