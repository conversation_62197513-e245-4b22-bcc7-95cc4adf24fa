<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
//use <PERSON>vel\Sanctum\HasApiTokens;
use Lara<PERSON>\Passport\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use Notifiable,HasApiTokens, HasRoles,HasFactory;
    protected $table = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',

    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'id' => 'string',
    ];

    public function merchant()
    {
        return $this->hasOne('App\Models\Merchant', "user_id");
    }

    public function role()
    {
        return $this->belongsToMany(\Spatie\Permission\Models\Role::class, 'role_user');
    }

    public function canAny($permissions, $arguments = [])
    {
        foreach ($permissions as $e) {
            if ($this->can($e))
                return true;
        }

        return false;
    }

    public function canAll(array $permissions)
    {
        foreach ($permissions as $e) {
            if (!$this->can($e))
                return false;
        }

        return true;
    }


    public static function flushCache($key = null)
    {
        $cache = basename(static::class);
        return static::clearCache($cache, $key);
    }
    public static function clearCache($tag, $key = null)
    {
        if (is_null($key))
            \Cache::tags(["$tag"])->flush();
        else
            \Cache::tags(["$tag"])->forget("$key");
        return new static;
    }
}
