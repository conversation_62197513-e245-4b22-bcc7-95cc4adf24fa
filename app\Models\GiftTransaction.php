<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GiftTransaction extends Model
{
    use HasFactory;
    protected $fillable = [
        'gift_id',
        "party_id",
        'reference_id',
        'external_reference_id',
        'claim_party_id',
        'claim_reference_id',
        'claim_external_reference_id',
        "payee",
        "credit_account_id",
        'amount',
        "status",
        "sender_gender",
        "sender_phone",
        'exchange_rate'

    ];
    protected $casts = [
        'credit_account_id' => 'object',
        'amount' => 'object',
        'payee' => 'object',
        'exchange_rate'     => 'object',
    ];

    public function gift()
    {
        return $this->belongsTo('App\Models\Gift');
    }
    public function sharing()
    {
        return $this->hasOne('App\Models\Sharing','data_id')->where('type','gift');
    }
}
