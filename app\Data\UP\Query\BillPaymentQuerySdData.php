<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\StatusData;
use Carbon\Carbon;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithCastAndTransformer;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Casts\DateTimeInterfaceCast;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Transformers\DateTimeInterfaceTransformer;


class BillPaymentQuerySdData extends BaseNonNullableData
{
    public function __construct(
        #[MapInputName('MT')]
        public ?int $type = null,
        #[MapInputName('Balance')]
        public ?float $balance = null,
        public ?BillPaymentQueryLoanData $loan = null,
        #[MapInputName('Offers'),DataCollectionOf(BillPaymentQueryOfferData::class)]
        public ?DataCollection $offers=null,
    ) {
    }

}
