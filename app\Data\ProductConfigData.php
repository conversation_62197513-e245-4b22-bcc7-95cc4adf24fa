<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Spatie\LaravelData\DataCollection;
class ProductConfigData extends Data
{
    public ?array $values;

    public ?array $current;
    public ?array $saving;
    public ?array $individual;

    public ?array $ad;
    public ?array $st;
    public ?array $corporation;

    public ?array $cash;
    public ?array $cashNoneYER;
    public ?array $cashOut;
    public ?array $cashOutNoneYER;

    public ?array $eloan;
    public ?array $creditCard;
    public ?array $gold;


}
