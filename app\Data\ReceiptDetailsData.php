<?php

namespace App\Data;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Illuminate\Support\Collection;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Attributes\DataCollectionOf;

class ReceiptDetailsData extends BaseNonNullableData
{
    public function __construct(
        public ?string $referenceId,
        public ?AccountIdData $debitAccountId,
        public ?CurrencyAmountData $amount,
        public ?CurrencyAmountData $fee,
        public ?string $remarks,
        //public ?string $receiverNumber,
        //public ?string $receiverName,
        //public ?string $bankCode,
        public ?string $remittanceId,
        #[DataCollectionOf(ReceiptData::class)]
        public ?DataCollection $items,
    ) {
    }


}
