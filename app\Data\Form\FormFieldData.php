<?php

namespace App\Data\Form;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\NameData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\DataCollection;


class FormFieldData extends FormBaseData
{
    public function __construct(
        public ?string $id=null,
        #[MapOutputName('lb'),MapInputName('lb')]
        public ?NameData $label=null,
        #[MapOutputName('ne'),MapInputName('ne')]
        public ?string $name=null,
        #[MapOutputName('ht'),MapInputName('ht')]
        public ?NameData $hint=null,
        #[MapOutputName('et'),MapInputName('et')]
        public ?string $element=null,
        #[MapOutputName('ty'),MapInputName('ty')]
        public ?string $type=null,
        #[MapOutputName('val'),MapInputName('val')]
        public $value=null,
        #[MapOutputName('ed'),MapInputName('ed')]
        public ?bool $enabled=null,
        #[MapOutputName('hp'),MapInputName('hp')]
        public ?FormGeneralData $helper=null,
        #[MapOutputName('vs'),MapInputName('vs'),DataCollectionOf(FormValidationData::class)]
        public ?DataCollection $validations=null,
        #[MapOutputName('os'),MapInputName('os'),DataCollectionOf(FormOptionData::class)]
        public ?DataCollection $options=null,
        #[MapOutputName('fs'),MapInputName('fs')]
        public ?array $formatters=null
    ) {
        parent::__construct($element); // Call parent constructor
    }

    // public static function prepareForPipeline(array $properties) : array
    // {
    //     $properties['debitAccountId']= data_get($properties,"payload.debitAccountId");
    //     $properties['amount']= data_get($properties,"payload.amount");
    //     $properties['fee']=data_get($properties,"payload.fee");
    //     $properties['receiverNumber']=data_get($properties,"payload.receiverNumber",);
    //     $properties['receiverName']=data_get($properties,"payload.receiverName");
    //     $properties['bankCode']=data_get($properties,key: "payload.bankCode");
    //     $properties['remarks']=data_get($properties,key: "payload.remarks");

    //     return $properties;
    // }
    public function copy(
        ?string $id=null,
         ?NameData $label=null,
         ?string $name=null,
         ?string $hint=null,
         ?string $element=null,
         ?string $type=null,
         $value=null,
         ?bool $enabled=null,
         ?FormGeneralData $helper=null,
         ?DataCollection $validations=null,
         ?DataCollection $options=null,
         ?array $formatters=null
    ) : static
    {
        return new static(
            id: $id ?? $this->id,
            label: $label ?? $this->label,
            name: $name ?? $this->name,
            hint: $hint ?? $this->hint,
            element: $element ?? $this->element,
            type: $type ?? $this->type,
            value: $value ?? $this->value,
            enabled: $enabled ?? $this->enabled,
            helper: $helper ?? $this->helper,
            validations: $validations ?? $this->validations,
            options: $options ?? $this->options,
            formatters: $formatters ?? $this->formatters
        );
    }
}
