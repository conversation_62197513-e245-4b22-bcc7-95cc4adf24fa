<?php

namespace App\Jobs;

use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\Models\SplitPayment;
use App\Scopes\CustomerScope;
use App\Services\NotificationService;
use App\Services\OBDX\BankyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
class ProcessSplitPayment implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

   /**
     * Create a new job instance.
     * @param SplitPayment $splitPayment
     * @return void
     */
    public function __construct(
        public SplitPayment $splitPayment,
    ){
        //
    }

    /**
     * Determine the unique ID for the job.
     */
    public function uniqueId()
    {
        return "wheel.process.{$this->splitPayment->id}";
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        \Log::warning("handle {id}",[['id' => $this->splitPayment->id]]);

        $this->splitPayment->load(['transactions'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class);
        }]);
        if($this->splitPayment->status==TransactionStatusEnum::INIT->value &&
            $this->splitPayment->type==InvoiceTransactionsTypeEnum::Payment->value){
            $valid=true;
            foreach($this->splitPayment->transactions as $transaction){
                if($transaction->status!=TransactionStatusEnum::COMPLETED->value ||
                    $transaction->type!=InvoiceTransactionsTypeEnum::Payment->value){
                    $valid=false;
                    break;
                }
            }
            \Log::warning("handle {valid}",[['valid' => $valid]]);
            if(is_null($this->splitPayment->bank_code)){
                if($valid){
                    $this->splitPayment->status=TransactionStatusEnum::COMPLETED->value;
                    $this->splitPayment->save();
                    NotificationService::sendMessagesToParty([
                        [
                            'title'=>__("Split payment"),
                            'body'=>sprintf(__("The split payment with referenece [%s] has completed!"),
                            $this->splitPayment->id
                            ),
                            'type'=>'operation',
                        ]
                    ],$this->splitPayment->party_id);
                }
            }else{
                $paymentResult=$this->splitPayment->payment_result;
                if(is_null($paymentResult->payment->referenceId) || !is_null($paymentResult->payment->externalReferenceId)){
                    $valid=false;
                }
                \Log::warning("handle2 {valid}",[['valid' => $valid]]);
                if($valid){
                    $this->splitPayment->status=TransactionStatusEnum::PENDING->value;
                    $this->splitPayment->save();

                    $object = new \stdClass();
                    $object->paymentId=$paymentResult->payment->referenceId;
                    $result=BankyService::getGenericTransefer($object);
                    if ($result->status->message->code == "0") {

                        $object = new \stdClass();
                        $object->paymentId=$paymentResult->payment->referenceId;
                        $object->paymentType="INDIADOMESTICFT";
                        $result=BankyService::patchGenericTransefer($object);

                        if ($result->status->message->code == "0") {
                            $this->splitPayment->status=TransactionStatusEnum::COMPLETED->value;
                            $paymentResult->payment->externalReferenceId=$result->getAdditionalData()["externalReferenceId"];
                            $paymentResult->payment->status=InvoiceTransactionsTypeEnum::Payment->value;

                            $this->splitPayment->payment_result=$paymentResult;
                            $this->splitPayment->save();

                            NotificationService::sendMessagesToParty([
                                [
                                    'title'=>__("Split payment"),
                                    'body'=>sprintf(__("The split payment with referenece [%s] has completed!"),
                                    $this->splitPayment->id
                                    ),
                                    'type'=>'operation',
                                ]
                            ],$this->splitPayment->party_id);

                            return;
                        }
                    }
                    $this->splitPayment->status=TransactionStatusEnum::ERROR->value;
                    $this->splitPayment->save();
                    ProcessCanceledSplit::dispatch($this->splitPayment)->onQueue('critical');
                    //ProcessCanceledSplit::dispatchSync($this->splitPayment);

                }
            }

        }


    }
}
