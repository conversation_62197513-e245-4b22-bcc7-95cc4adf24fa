<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class CustomerScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return $builder
     */
    public function apply(Builder $builder, Model $model)
    {
        $user=auth()->user();

        if(!isset($user->customerType))
           return $builder;

        $column_name="party_id";

        if(substr($user->id,0,1)!="0"){
            $partyId="0{$user->id}";
        }else{
            $partyId=substr($user->id,1);
        }
        return $builder->where(function($query) use($model,$column_name,$user,$partyId){
            $query->where($model->getTable().".$column_name",$user->id)
            ->orWhere($model->getTable().".$column_name",$partyId);
        });//->where($model->getTable().".$column_name",$user->id)->where($model->getTable().".$column_name",$user->id);
    }
}
