<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;
use App\Services\RemittanceNetworkService;

class ManualHarvestData extends Data
{
    public function __construct(
        public int $id,
        public string $trackingCode,
        public string $customerName,
        public string $customerPhone,
        public string $customerIdNumber,
        public ?string $networkName,
        public ?string $serviceCodeName,
        public int $status,
        public int $manualStage,
        public ?string $manualNotes,
        public ?string $rejectionReason,
        public ?int $processedBy,
        public ?string $processedByName,
        public ?string $manualStartedAt,
        public ?string $manualCompletedAt,
        public ?RemittanceDetailsData $remittanceDetails,
        public ?CustomerVerificationData $customerVerification,
        public string $createdAt,
        public string $updatedAt
    ) {}

        /**
     * Get network name from harvest using multiple methods
     */
    private static function getNetworkName($harvest): string
    {
        // First priority: Pattern matching from tracking code
        if (!empty($harvest->data->trackingCode)) {
            $trackingCode = $harvest->data->trackingCode;
            
            // Pattern matching for different networks (specific patterns first)
            if (preg_match('/^YM\d+/', $trackingCode)) {
                return 'Yeah Money Transfer';
            } elseif (preg_match('/^WU\d+/i', $trackingCode)) {
                return 'Western Union';
            } elseif (preg_match('/^888\d+/', $trackingCode)) {
                return 'MoneyGram';
            } elseif (preg_match('/^749\d+/', $trackingCode)) {
                return 'Ria Money Transfer';
            } elseif (preg_match('/^449\d+/', $trackingCode)) {
                return 'Small World';
            } elseif (preg_match('/^AAE\d+/', $trackingCode)) {
                return 'Al Ansari Exchange';
            } elseif (preg_match('/^555\d+/', $trackingCode)) {
                return 'UAE Exchange';
            } elseif (preg_match('/^XM\d+/', $trackingCode)) {
                return 'Xpress Money';
            } elseif (preg_match('/^777\d+/', $trackingCode)) {
                return 'Al Rajhi Bank';
            } elseif (preg_match('/^999\d+/', $trackingCode)) {
                return 'Tahweel Al Rajhi';
            } elseif (preg_match('/^\d{10}$/', $trackingCode)) {
                return 'Western Union';
            }
        }
        
        // Second priority: Try network detection service
        if (!empty($harvest->data->trackingCode)) {
            try {
                $detectedNetwork = RemittanceNetworkService::detectNetwork($harvest->data->trackingCode);
                if ($detectedNetwork && isset($detectedNetwork['network']) && $detectedNetwork['network']) {
                    return $detectedNetwork['network']->name;
                }
            } catch (\Exception $e) {
                // Continue to next fallback
            }
        }
        
        // Third priority: Service networks (but avoid default Ria)
        if ($harvest->service && $harvest->service->networks && $harvest->service->networks->count() > 0) {
            $networks = $harvest->service->networks;

            // Try to find Western Union network specifically for WU codes
            if (!empty($trackingCode) && preg_match('/^WU/i', $trackingCode)) {
                $westernUnionNetwork = $networks->where('name', 'Western Union')->first();
                if ($westernUnionNetwork) {
                    return 'Western Union';
                }
            }

            // Try to find a non-Ria network first
            foreach ($networks as $network) {
                $cleanName = trim(preg_replace('/\d+$/', '', $network->name)); // Remove trailing numbers
                if (!str_contains($cleanName, 'Ria Money Transfer')) {
                    return $cleanName;
                }
            }

            // If all are Ria, return cleaned name
            $firstName = $networks->first()->name;
            return trim(preg_replace('/\d+$/', '', $firstName)); // Remove trailing numbers
        }
        
        // Final fallback to service name
        $serviceCodeName = $harvest->service?->name;
        if ($serviceCodeName) {
            return $serviceCodeName;
        }
        
        return 'غير محدد';
    }

public static function fromHarvest($harvest): self
    {
        // Get customer data from multiple sources with fallbacks
        $customerName = $harvest->receiver_info->Receiver_Full_Name ??
                       $harvest->data->fullName ??
                       $harvest->customer?->name ??
                       'غير محدد';

        // Try to get phone from multiple sources
        $customerPhone = $harvest->receiver_info->Receiver_Mobile ??
                        $harvest->data->phoneNumber ??
                        $harvest->customer?->phoneNumber?->value ??
                        '';

        // Try to get ID number from multiple sources
        $customerIdNumber = $harvest->receiver_info->Receiver_Id ??
                           $harvest->data->idNumber ??
                           $harvest->customer?->idNumber ??
                           '';

        return new self(
            id: $harvest->id,
            trackingCode: $harvest->data->trackingCode ?? '',
            customerName: $customerName,
            customerPhone: $customerPhone,
            customerIdNumber: $customerIdNumber,
            networkName: self::getNetworkName($harvest),
            serviceCodeName: $harvest->service?->name ?? 'غير محدد',
            status: $harvest->status,
            manualStage: $harvest->manual_stage ?? 1,
            manualNotes: $harvest->manual_notes,
            rejectionReason: $harvest->rejection_reason,
            processedBy: $harvest->processed_by,
            processedByName: $harvest->processedBy?->name,
            manualStartedAt: $harvest->manual_started_at?->format('Y-m-d H:i:s'),
            manualCompletedAt: $harvest->manual_completed_at?->format('Y-m-d H:i:s'),
            remittanceDetails: $harvest->sender_info ? RemittanceDetailsData::fromSenderInfo($harvest->sender_info, $harvest->amount) : null,
            customerVerification: $harvest->customer ? CustomerVerificationData::fromUser($harvest->customer) : null,
            createdAt: $harvest->created_at->format('Y-m-d H:i:s'),
            updatedAt: $harvest->updated_at->format('Y-m-d H:i:s')
        );
    }

    public function getStatusDescription(): string
    {
        return match($this->status) {
            2 => 'Manual processing initiated',
            3 => 'Awaiting customer verification',
            4 => 'Awaiting remittance details input',
            5 => 'Awaiting final review',
            6 => 'Approved, awaiting execution',
            8 => 'Manual processing completed',
            -2 => 'Manual processing rejected',
            default => 'Unknown status'
        };
    }

    public function getProgressPercentage(): int
    {
        return match($this->status) {
            2 => 10,
            3 => 25,
            4 => 50,
            5 => 75,
            6 => 90,
            8 => 100,
            -2 => 0,
            default => 0
        };
    }

    public function canAdvanceToNextStage(): bool
    {
        return in_array($this->status, [2, 3, 4, 5, 6]);
    }

    public function isCompleted(): bool
    {
        return $this->status === 8;
    }

    public function isRejected(): bool
    {
        return $this->status === 16;
    }

    public function isPending(): bool
    {
        return in_array($this->status, [10, 11, 12, 13, 14]);
    }
}

class RemittanceDetailsData extends Data
{
    public function __construct(
        public ?string $senderName,
        public ?string $senderPhone,
        public ?string $senderCountry,
        public ?string $agentName,
        public ?float $amount,
        public ?string $currency,
        public ?string $purpose,
        public ?string $notes
    ) {}

    public static function fromSenderInfo($senderInfo, $amountData): self
    {
        return new self(
            senderName: $senderInfo->Sender_Full_Name ?? null,
            senderPhone: $senderInfo->Sender_Mobile ?? null,
            senderCountry: $senderInfo->Sender_Country ?? null,
            agentName: $senderInfo->Agent_Name ?? null,
            amount: $amountData->amount ?? null,
            currency: $amountData->currency ?? null,
            purpose: null,
            notes: null
        );
    }
}

class CustomerVerificationData extends Data
{
    public function __construct(
        public string $fullName,
        public string $firstName,
        public ?string $secondName,
        public ?string $thirdName,
        public string $lastName,
        public string $phoneNumber,
        public ?string $idNumber,
        public ?string $nationality,
        public ?string $dateOfBirth,
        public ?string $address
    ) {}

    public static function fromUser($user): self
    {
        return new self(
            fullName: $user->name ?? '',
            firstName: $user->firstName ?? '',
            secondName: $user->secondName ?? '',
            thirdName: $user->thirdName ?? '',
            lastName: $user->lastName ?? '',
            phoneNumber: $user->phoneNumber->value ?? '',
            idNumber: $user->idNumber ?? '',
            nationality: $user->nationality ?? '',
            dateOfBirth: $user->dateOfBirth?->format('Y-m-d'),
            address: $user->address ?? ''
        );
    }

    public function matchesRemittanceReceiver(?object $receiverInfo): array
    {
        $matches = [];
        $mismatches = [];

        if ($receiverInfo) {
            // Check name matching
            $receiverFullName = $receiverInfo->Receiver_Full_Name ?? '';
            if (strtolower($this->fullName) === strtolower($receiverFullName)) {
                $matches[] = 'Full name matches';
            } else {
                $mismatches[] = "Name mismatch: Customer '{$this->fullName}' vs Remittance '{$receiverFullName}'";
            }

            // Check phone matching
            $receiverPhone = $receiverInfo->Receiver_Mobile ?? '';
            if ($this->phoneNumber === $receiverPhone) {
                $matches[] = 'Phone number matches';
            } else {
                $mismatches[] = "Phone mismatch: Customer '{$this->phoneNumber}' vs Remittance '{$receiverPhone}'";
            }

            // Check ID number if available
            $receiverId = $receiverInfo->Receiver_Id ?? '';
            if ($receiverId && $this->idNumber === $receiverId) {
                $matches[] = 'ID number matches';
            } elseif ($receiverId && $this->idNumber !== $receiverId) {
                $mismatches[] = "ID mismatch: Customer '{$this->idNumber}' vs Remittance '{$receiverId}'";
            }
        }

        return [
            'matches' => $matches,
            'mismatches' => $mismatches,
            'verification_score' => count($matches) / max(1, count($matches) + count($mismatches)) * 100
        ];
    }
}


