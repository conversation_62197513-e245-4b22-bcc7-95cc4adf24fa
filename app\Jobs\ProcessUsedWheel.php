<?php

namespace App\Jobs;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\PaymentResultData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\WheelTransaction;
use App\Services\FlexService;
use App\Services\OBDX\AdminService;
use App\Services\OBDX\BankyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessUsedWheel implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

       /**
     * Create a new job instance.
     * @param WheelTransaction $transaction
     * @return void
     */
    public function __construct(
        public WheelTransaction $transaction,
    ){
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $setting=app(\App\Settings\WheelSettings::class);

        LogItem::store($this->transaction);
        $wheelAccountId=AccountIdData::from([
            "displayValue"=>ThirdPartyServiceNameData::wheel(),
            "value"=>ThirdPartyServiceNameData::wheel()
        ]);
        //if(is_null($this->transaction->account_id)){

            //$accounts=AdminService::accountAccess(request(),$this->transaction->party_id);
            $accounts=BankyService::partyAccounts(request(),$this->transaction->party_id);
            if($accounts==false){
                $this->transaction->status=TransactionStatusEnum::ERROR->value;
                $this->transaction->save();
                return;
            }
            // $accounts=collect($accounts)
            //     ->filter(function ($account,$key)use($setting,$wheelAccountId)  {
            //         $accountId=AccountIdData::from($account->accountNumber);
            //         return $account->accountStatus=="ACTIVE" &&
            //          $wheelAccountId->isNorth()==$accountId->isNorth() &&
            //           $account->currencyCode==$setting->wheelConfig->dailyMaxAmount->currency &&
            //            $accountId->isCashOut();
            //     })->values();
            $accounts=collect($accounts)
            ->filter(function ($account,$key)use($setting,$wheelAccountId)  {
                $accountId=AccountIdData::from($account->account);
                return
                    $wheelAccountId->isNorth()==$accountId->isNorth() &&
                    $accountId->currencyId()==$setting->wheelConfig->dailyMaxAmount->currency &&
                    $accountId->isCashOut();
            })->values();
            if(!count($accounts->all())){
                $this->transaction->status=TransactionStatusEnum::ERROR->value;
                $this->transaction->save();
                return;
            }
            $account=$accounts->first();

           // $accountId=$this->transaction->account_id;
           // $accountId->value=$account->accountNumber->value;

           $this->transaction->account_id=$account->account;
            //$this->transaction->account_id=$account->accountNumber;
            $this->transaction->save();

        //}

        $object = new \stdClass();
        $object->journals= [
            "genericPayee" =>[
                "nickName" =>'',
                "accountName" =>'temp',
                "accountNumber" => $this->transaction->account_id->value,
                "transferMode"=> 'ACC'
            ],
            "genericPayout" =>[
                "amount" =>CurrencyAmountData::from([
                    "amount"=>$this->transaction->wheel_item->value,
                    "currency"=>$setting->wheelConfig->dailyMaxAmount->currency
                ])->toArray(),
                "purpose" =>'FAML',
                "purposeText" => null,
                "debitAccountId" => $wheelAccountId->toArray(),
                "remarks"=> sprintf(trans("lucky_wheel_remark_message"),
                $this->transaction->id
                )

            ],
            "paymentType" => 'INTERNALFT'
        ];

        $result=BankyService::internalTransefer($object);
        if ($result->status->message->code == "0") {
            $this->transaction->status=TransactionStatusEnum::COMPLETED->value;
            $this->transaction->payment_result=new PaymentResultData(
                $result->getAdditionalData()["referenceId"],
                $result->getAdditionalData()["externalReferenceId"],
                InvoiceTransactionsTypeEnum::Payment->value
            );
            $this->transaction->save();
        }else{
            $this->transaction->status=TransactionStatusEnum::PENDING->value;
            $this->transaction->save();
        }

        // $object=new \stdClass();
        // $object->reference_id   = uniqid();
        // $object->service_name   = ThirdPartyServiceNameData::wheel();
        // $object->account_id     = $account->accountNumber->value;
        // $object->amount         = $this->transaction->wheel_item->value;
        // $object->currency       = $setting->wheelConfig->dailyMaxAmount->currency;
        // $object->remarks        = sprintf(trans("lucky_wheel_remark_message"),
        //         $this->transaction->id
        // );


        // $result=FlexService::accountToDebit($object);
        // if($result->status->message->code=="0"){
        //     $this->transaction->status=TransactionStatusEnum::COMPLETED->value;
        //     $this->transaction->payment_result= new PaymentResultData(
        //         $object->reference_id,
        //         $result->getAdditionalData()["externalReferenceId"],
        //         InvoiceTransactionsTypeEnum::Payment->value
        //     );

        //     $this->transaction->save();

        // }

    }
}
