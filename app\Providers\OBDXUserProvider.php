<?php

namespace App\Providers;

use App\Services\OBDX\CustomerService;
use App\Services\OBDX\LoginService;
use Closure;
use Illuminate\Support\Facades\Http;
use Illuminate\Contracts\Auth\Authenticatable as UserContract;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Contracts\Hashing\Hasher as HasherContract;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Facades\Cookie;
use Laravel\Passport\PassportUserProvider;

class OBDXUserProvider implements UserProvider
{
    /**
    * The partyVerify instance.
    *
    * @var \App\Models\PartyVerify
    */
    public $partyVerify;
    /**
    * The partyVerify instance.
    *
    * @var CustomerService
    */
    protected $customerService;
    /**
     * The hasher implementation.
     *
     * @var \Illuminate\Contracts\Hashing\Hasher
     */
    protected $hasher;

    /**
     * The Eloquent user model.
     *
     * @var string
     */
    protected $model;

    /**
     * The Eloquent user model.
     *
     * @var \Illuminate\Http\Request
     */
    protected $request;

    /**
     * Create a new database user provider.
     *
     * @param  \Illuminate\Contracts\Hashing\Hasher  $hasher
     * @param  string  $request
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    public function __construct(HasherContract $hasher,$model, $request)
    {
        $this->model = $model;
        $this->request = $request;
        $this->hasher = $hasher;
    }

    /**
     * Retrieve a user by their unique identifier.
     *
     * @param  mixed  $identifier
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveById($identifier)
    {
        $this->customerService??=new CustomerService(request());
        $customer=$this->customerService->getUser($this->partyVerify);
        if(isset($customer->userProfile)){
            $userProfile=$customer->userProfile;
            $user= $this->createModel();
            $user->status=$customer->status;
            $user->id=$userProfile->partyId->value??$userProfile->phoneNumber->value??null;
            $user->name=html_entity_decode($userProfile->firstName)." ".html_entity_decode($userProfile->middleName??"")." ".html_entity_decode($userProfile->lastName);
            $user->email=$userProfile->emailId?->value??null;
            $user->phone=$userProfile->phoneNumber->value;
            $user->customerType=$userProfile->customerType;
            $user->username=$userProfile->userName;

            if(isset($userProfile->customerRole)){
                $user->customerRole=$userProfile->customerRole;
            }
            $user->userProfile=$userProfile;
            $user->firstLoginFlowDone=$customer->firstLoginFlowDone??true;
           // request()->headers->remove('token');
            return $user;
        }else if(is_null($customer)){
            return null;
        }else{
            return abort($customer);
        }
    }
    /**
     * {@inheritdoc}
     */
    public function updateRememberToken($user, $token)
    {
       // $this->provider->updateRememberToken($user, $token);
    }

    /**
     * Retrieve a user by their unique identifier and "remember me" token.
     *
     * @param  mixed  $identifier
     * @param  string  $token
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveByToken($identifier, $token)
    {

        $this->customerService=new CustomerService(request());
        $this->customerService->headers+=['jwt'=>$token];
        //$service->getUser(request(),$this->partyVerify);
        return $this->retrieveById(null);
    }

    /**
     * Retrieve a user by the given credentials.
     *
     * @param  array  $credentials
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveByCredentials(array $credentials)
    {
        $request=request();
        $url="{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1";
        $response = Http::withOptions([
            'verify' => false,
        ])
        ->withoutRedirecting()
        ->timeout(10)
        ->asForm()
        ->post(
            "{$url}/j_security_check",
            [
                'j_username' => $credentials['username'],
                'j_password' => $credentials['password'],
            ]
        );

        if(in_array($response->status(),[303,302])){
            //Add cookies to current request
            $cookies=$response->cookies()->toArray();
            $request->cookies->add(
                collect($cookies)
                ->flatMap(function ($values) {
                    //setrawcookie($values["Name"], preg_replace('/[\s,;]+/', '', $values["Value"]),0, "/",'',false,true);
                    Cookie::queue($values["Name"], $values["Value"]);
                    return array_map(function ($value){
                        return $value;
                    }, [$values["Name"]=>$values["Value"]]);
                })->toArray()
            );
            return $this->retrieveById(null);
        }
        return null;
    }
    /**
     * Rehash the user's password if required and supported.
     *
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  array  $credentials
     * @param  bool  $force
     * @return void
     */
    public function rehashPasswordIfRequired($user,array $credentials, bool $force = false){

    }
    /**
     * Validate a user against the given credentials.
     *
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  array  $credentials
     * @return bool
     */
    public function validateCredentials(UserContract $user, array $credentials)
    {
        // if (is_null($plain = $credentials['password'])) {
        //     return false;
        // }

        // return $this->hasher->check($plain, $user->getAuthPassword());
        return false;
    }


     /**
     * Create a new instance of the model.
     *
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function createModel()
    {
        $class = '\\'.ltrim($this->model, '\\');

        return new $class;
    }

    /**
     * Gets the hasher implementation.
     *
     * @return \Illuminate\Contracts\Hashing\Hasher
     */
    public function getHasher()
    {
        return $this->hasher;
    }

    /**
     * Sets the hasher implementation.
     *
     * @param  \Illuminate\Contracts\Hashing\Hasher  $hasher
     * @return $this
     */
    public function setHasher(HasherContract $hasher)
    {
        $this->hasher = $hasher;

        return $this;
    }

    /**
     * Gets the name of the Eloquent user model.
     *
     * @return string
     */
    public function getModel()
    {
        return $this->model;
    }

    /**
     * Sets the name of the Eloquent user model.
     *
     * @param  string  $model
     * @return $this
     */
    public function setModel($model)
    {
        $this->model = $model;

        return $this;
    }

}
