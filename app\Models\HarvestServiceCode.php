<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class HarvestServiceCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'process',
        'status'
    ];

    protected $casts = [
        'process' => 'boolean',
        'status' => 'boolean'
    ];

    /**
     * Get the remittance networks that use this service code
     */
    public function networks(): HasMany
    {
        return $this->hasMany(RemittanceNetwork::class, 'service_code_id');
    }

    /**
     * Get the harvests for this service code
     */
    public function harvests(): Has<PERSON>any
    {
        return $this->hasMany(Harvest::class, 'service_code_id');
    }

    /**
     * Scope for active service codes
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Check if this service supports automated processing
     * (based on associated networks)
     */
    public function supportsAutomatedProcessing(): bool
    {
        return $this->networks()->where('is_auto', true)->exists();
    }

    /**
     * Check if this service requires manual processing
     * (based on associated networks)
     */
    public function requiresManualProcessing(): bool
    {
        return $this->networks()->where('is_auto', false)->exists();
    }

    /**
     * Get processing methods supported by this service
     */
    public function getProcessingMethods(): array
    {
        $methods = [];

        if ($this->supportsAutomatedProcessing()) {
            $methods[] = 'automated';
        }

        if ($this->requiresManualProcessing()) {
            $methods[] = 'manual';
        }

        return $methods;
    }

    /**
     * Get primary processing method
     */
    public function getPrimaryProcessingMethod(): string
    {
        // If service supports automated processing, prefer it
        if ($this->supportsAutomatedProcessing()) {
            return 'automated';
        }

        return 'manual';
    }
}
