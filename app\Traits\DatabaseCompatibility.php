<?php

namespace App\Traits;

trait DatabaseCompatibility
{
    protected function getFormatedDateByConnection(string $column,string $format)
    {
        if(env('DB_CONNECTION')!='oracle'){
            switch($format){
                case 'YYYY-MM-DD':
                    return "DATE_FORMAT($column,'%Y-%m-%d')";
                case 'DD':
                    return "DATE_FORMAT($column,'%d')";
                case 'IW':
                    return "WEEK($column,1)";
                case 'YYYY-MM':
                    return "DATE_FORMAT($column,'%Y-%m')";
                case 'MM':
                    return "DATE_FORMAT($column,'%m')";
                case 'YYYY':
                    return "DATE_FORMAT($column,'%Y')";
                case 'YYYY-MM-DD HH24':
                    return "DATE_FORMAT($column,'%Y-%m-%d %H')";
                case 'HH24':
                    return "DATE_FORMAT($column,'%H')";
                case 'YYYY-MM-DD HH24:MI:SS':
                    return "DATE_FORMAT($column,'%Y-%m-%d %H:%i:%s')";
            }
        }
        switch($format){
            case 'YYYY-MM-DD':
                return "TO_CHAR($column,'YYYY-MM-DD')";
            case 'DD':
                return "TO_CHAR($column,'DD')";
            case 'IW':
                return "TO_CHAR($column - 7/24,'IW')";
            case 'YYYY-MM':
                return "TO_CHAR($column,'YYYY-MM')";
            case 'MM':
                return "TO_CHAR($column,'MM')";
            case 'YYYY':
                return "TO_CHAR($column,'YYYY')";
            case 'YYYY-MM-DD HH24':
                return "TO_CHAR($column,'YYYY-MM-DD HH24')";
            case 'HH24':
                return "TO_CHAR($column,'HH24')";
            case 'YYYY-MM-DD HH24:MI:SS':
                return "TO_CHAR($column,'YYYY-MM-DD HH24:MI:SS')";

        }
    }
    protected function getDateByConnection(string $column,string $format)
    {
        if(env('DB_CONNECTION')!='oracle'){
            switch($format){
                case 'YYYY-MM-DD':
                    return "STR_TO_DATE($column,'%Y-%m-%d')";
                case 'DD':
                    return "STR_TO_DATE($column,'%d')";
                case 'IW':
                    return "WEEK($column,1)";
                case 'YYYY-MM':
                    return "STR_TO_DATE($column,'%Y-%m')";
                case 'MM':
                    return "STR_TO_DATE($column,'%m')";
                case 'YYYY':
                    return "STR_TO_DATE($column,'%Y')";
                case 'YYYY-MM-DD HH24':
                    return "STR_TO_DATE($column,'%Y-%m-%d %H')";
                case 'HH24':
                    return "STR_TO_DATE($column,'%H')";
                case 'YYYY-MM-DD HH24:MI:SS':
                    return "STR_TO_DATE($column,'%Y-%m-%d %H:%i:%s')";
            }
        }
        switch($format){
            case 'YYYY-MM-DD':
                return "TO_DATE($column,'YYYY-MM-DD')";
            case 'DD':
                return "TO_DATE($column,'DD')";
            case 'IW':
                return "TO_DATE($column - 7/24,'IW')";
            case 'YYYY-MM':
                return "TO_DATE($column,'YYYY-MM')";
            case 'MM':
                return "TO_DATE($column,'MM')";
            case 'YYYY':
                return "TO_DATE($column,'YYYY')";
            case 'YYYY-MM-DD HH24':
                return "TO_DATE($column,'YYYY-MM-DD HH24')";
            case 'HH24':
                return "TO_DATE($column,'HH24')";
            case 'YYYY-MM-DD HH24:MI:SS':
                return "TO_DATE($column,'YYYY-MM-DD HH24:MI:SS')";
        }
    }

    protected function getSubDateByConnection(string $column,string $value,string $period)
    {
        if(env('DB_CONNECTION')!='oracle'){
            return "DATE_SUB($column, INTERVAL $value $period)";
        }
        if($period=="MONTH"){
            return "ADD_MONTHS($column, -$value)";
        }
        if($period=="HOUR"){
            return "$column-$value/24";
        }
        return "$column-$value";
    }


    protected function getCastByConnection(string $column,string $format)
    {
        if(env('DB_CONNECTION')!='oracle'){
            switch($format){
                case 'DECIMAL':
                    return "CAST($column AS DECIMAL(10,2))";
                case 'SIGNED':
                    return "CAST($column AS SIGNED)";
            }
        }
        switch($format){
            case 'DECIMAL':
                return "CAST($column AS number)";
            case 'SIGNED':
                return "CAST($column AS number)";
        }
    }
}
