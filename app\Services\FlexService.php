<?php

namespace App\Services;
use App\Data\AccountIdData;
use App\Data\AppConfigData;
use App\Data\CreditCardData;
use App\Data\GeneralResponseData;
use App\Data\GoldWithdrawData;
use App\Data\PaymentResultData;
use App\Data\TokenData;

use App\Enums\InvoiceTransactionsTypeEnum;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;

use App;
use App\Data\Flex\ChargeFeeData;
use App\Data\Flex\RechargeFeeData;
use App\Enums\CurrencyTypeEnum;
use App\Models\User;
use Spatie\LaravelData\DataCollection;

/**
 * Service to create and update orders
 */
class FlexService
{
    public $settings;
    public $isTest=false;

    /**
     * Service to create and update orders
     */
    public function __construct()
    {
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->cardless;
        if($this->settings->is_test){
            $this->isTest=true;
            $this->settings=$this->settings->test_data;
        }
        //$this->updateInstid();
    }
    // public function updateInstid()
    // {
    //     if(env('APP_FLAG',"banky.commercial")!="banky.islamic"){
    //         $settings=app(\App\Settings\ThirdPartySettings::class);
    //         //\Log::info($settings);
    //         $cardless=$settings->cardless;
    //         $test_data=$cardless->test_data;
    //         $test_data->instid="BANKY";
    //         $cardless->test_data=$test_data;
    //         $cardless->instid="BANKY";
    //         $settings->cardless=$cardless;
    //         $settings->save();
    //     }

    // }
    /**
     * request access token.
     *
     * @param  bool $forceUpdate
     * @return ?int|?string
     */
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }
        if(is_null($token->access_token)||empty($token->access_token)){
            $response = rescue(function (){
                return  Http::timeout(2)
                ->withBasicAuth($this->settings->client_id, $this->settings->client_secret)
                ->asForm()
                ->post("{$this->settings->url}/oauth/token",[
                    "grant_type"=>"client_credentials"
                ]);
            }, function ($e) {
                return $e->getMessage();
            });

            if(is_string($response)){
                return $response;
            }

            if ($response->failed()) {
                return $response;
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->access_token)){
                $this->settings->token=TokenData::from($response);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $cardless=$settings->cardless;
                if($cardless->is_test){
                    $test_data=$cardless->test_data;
                    $test_data->token=$this->settings->token;
                    $cardless->test_data=$test_data;
                }else{
                    $cardless->token=$this->settings->token;
                }
                $settings->cardless=$cardless;
                $settings->save();
            }
            // dd($response);

        }
        return $status;
    }
    public static function cardlessInit($request) {

        $flexService=new FlexService();

        //$user=$request->user()->userProfile;
        $params=[
            "SERV_TRX_REF_ID"   => $request->id,
            "SERVICE_CD"        => $flexService->settings->cardless->service_cd,
            "USERNAME"          => $flexService->settings->cardless->username,
            "PASSWORD"          => $flexService->settings->cardless->password,
            "ZONE_ID"           => $request->zone_id,
            "ACCOUNTID"         => $request->account_id,
            "AMOUNT"            => $request->amount,
            "CURRENCY"          => $request->currency,
            "TERMINAL_TYPE"     => "ATM",
            "SENDER_MOBILE"     => $request->phone,
            "SENDER_OTP"        => $request->otp,
            "REMARKS"           => $request->remark,//"1/23/2022 8:43:05 PM"
            "TO_DATETIME"       => $request->expiry_date,//"1/23/2022 8:43:05 PM"
            // "S_FIRST_NM"        => html_entity_decode( $user->firstName),
            // "S_SECOND_NM"       => html_entity_decode( $user->middleName),
            // "S_THIRD_NM"        => "",
            // "S_LAST_NM"         => html_entity_decode( $user->lastName),
        ];
        if(!is_null($request->receiverPhone)){
            if(is_null($request->receiverName->first) || is_null($request->receiverName->second) ||
                is_null($request->receiverName->third) || is_null($request->receiverName->last)
            ){
                return GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "[CARDLESS_SERVER]",
                        "message"   => [
                            "title"   => "Please check paramters & try again",
                            "detail"  => "failed",
                            "code"    => "DIGX_SWITCH_CARDLESS_100",
                            "type"    => "ERROR"
                        ]
                    ]
                ]);
            }else{
                if( !is_null($request->remark) && !empty($request->remark)){
                    $remark=sprintf(trans("cardless_to_remark_with_note"),
                        $request->receiverName->first." ".$request->receiverName->second." ".$request->receiverName->third." ".$request->receiverName->last,
                        $request->remark);
                }else{
                    $remark=sprintf(trans("cardless_to_remark"),
                        $request->receiverName->first." ".$request->receiverName->second." ".$request->receiverName->third." ".$request->receiverName->last);
                }
                $params+=[
                    "REMARKS"           => $remark,
                    "RECEIVER_MOBILE"   => $request->receiverPhone,
                    "R_FIRST_NM"        => $request->receiverName->first,
                    "R_SECOND_NM"       => $request->receiverName->second,
                    "R_THIRD_NM"        => $request->receiverName->third,
                    "R_LAST_NM"         => $request->receiverName->last,
                ];
            }
        }else{
            if( !is_null($request->remark) && !empty($request->remark)){
                $remark=sprintf(trans("cardless_remark_with_note"),
                    $request->remark);
            }else{
                $remark=trans("cardless_remark");
            }
            $params+=[
                "REMARKS"           => $remark,
                "RECEIVER_MOBILE"   => $request->phone
            ];
        }

        $response = Http::post($flexService->settings->cardless->url,$params);
        if ($response->failed()) {
            //dd($response);
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[CARDLESS_SERVER]",
                    "message"   => [
                        "title"   => "Can't process your order, please connect help desk to fix problem!",
                        "detail"  => "failed",
                        "code"    => "DIGX_SWITCH_CARDLESS_200",
                        "type"    => "ERROR"
                    ]
                ]
            ]);
        }
        $result=$response->object();
        if(($result->ResultCode??"-1")=="0" && isset($result->TransRefNum)){
            $partyId=AccountIdData::from([
                "value"=> $request->account_id
            ])->partyId();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Cardless request"),
                    'body'=>$remark,
                    'type'=>'operation',
                    //'extra_id'=>$referenceId??""
                ]
            ],$partyId);
            return GeneralResponseData::from([
                'status' => [
                    "result" => "SUCCESSFUL",
                    "contextID" => "",
                    "message" => [
                        "code" => "0",
                        "type" => "INFO"
                    ]
                ]
            ])->additional([
                'external_reference_id'=>$result->TransRefNum,
            ]);
        }
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[CARDLESS_SERVER] - {$result->ResultCode} - {$result->ResultStatus}",
                "message"   => [
                    "title"   => "Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->ResultDesc??"",
                    "code"    => "DIGX_SWITCH_CARDLESS_101",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }
    public static function checkTransactionStatus($request) {

        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ]);
        }

        $response = Http::withToken($flexService->settings->token->access_token)
            ->post($flexService->settings->url."/banky/check_req_ref",
                [
                    "req_ref_id"=> $request->trxId,
                    "brn_cd"=> substr($request->trxId,0,3),
                    "instid"=> "BANKY",
                ]);
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::checkTransactionStatus($request);
        }
        $result=$response->object();
        if(isset($result->result_code)){
            $externalTxn=null;
            if(isset($result->transaction_details)){
                if(array_is_list($result->transaction_details)){
                    $transaction_details=$result->transaction_details[0];
                }else{
                    $transaction_details=$result->transaction_details;
                }
                $externalTxn=[
                    "reference_id" => $transaction_details->trnref,
                    "detail"    => $transaction_details->ext_trn_code_desc,
                    "amount"    => \App\Data\CurrencyAmountData::from([
                        "amount"=>$transaction_details->lcyamt,
                        "currency"=>$transaction_details->ccy,
                    ]),
                    "date"    => $transaction_details->txndt
                ];
            }
            if($result->result_code == "0"
                && isset($transaction_details->authstat) && $transaction_details->authstat=="A"
                //&& isset($result->transaction_details->eventcode) && $result->transaction_details->eventcode!="REVS"
            ){
                return GeneralResponseData::from([
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "Payment successfully",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ])->additional([
                    'externalTxn' =>$externalTxn,
                ]);
            }else{
                return GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "[STATUS_SERVER] - {$result->result_code}",
                        "message"   => [
                            "title"   => "Payment failed",
                            "detail"  => $result->result_desc??"",
                            "code"    => "DIGX_SWITCH_STATUS_001",
                            "type"    => "ERROR"
                        ]
                    ]
                ])->additional([
                    'externalTxn' =>$externalTxn,
                ]);
            }
        }

        return GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" =>"[STATUS_SERVER] - {$result->result_code} - {$result->result_status}",
                "message"   => [
                    "title"   => "Server enabled to reverse amount to customer account!",
                    "detail"  => $result->result_desc??"",
                    "code"    => "DIGX_SWITCH_STATUS_102",
                    "type"    => "ERROR"
                ]
            ]
        ]);

    }

    public static function debitToAccount($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        $referenceId=uniqid();
        if(isset($request->reference_id)){
            $referenceId=$request->reference_id;
        }
        $params= [
            "req_ref_id"=>  "{$referenceId}",
            "instid"    => $flexService->settings->instid,
            "serv_name" => $request->service_name,
            "fr_account"=> $request->account_id,
            "trx_type"  => "cr",
            "amount"    => $request->amount,
            "currency"  => $request->currency,
            "remark"    => $request->remarks
        ];
        if(isset($request->fee)){
            $params['fee']=$request->fee;
        }
        $response = Http::withToken($flexService->settings->token->access_token)
            ->post(
                $flexService->settings->url."/banky/post_crtrt_trx",
                $params
            );
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::debitToAccount($request);
        }

        $result=$response->object();
        if($result->result_code=="0"){
            $partyId=AccountIdData::from([
                "value"=> $request->account_id
            ])->partyId();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Debit from account"),
                    'body'=>sprintf(__("Successfully debit from account [%s] for [%s]"),
                        $request->account_id,
                        $request->remarks
                    ),
                    'type'=>'operation',
                    //'extra_id'=>$referenceId??""
                ]
            ],$partyId);
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "Successfully recharge customer account",
                        "detail"  => "Successfully recharge customer account",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ))->additional([
                'referenceId'           =>$referenceId??"",
                'externalReferenceId'   =>$result->fccref??"",
            ]);
        }
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[CARDLESS_SERVER] - {$result->result_code} - {$result->result_status}",
                "message"   => [
                    "title"   => "Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->result_desc??"Can't process your order, please connect help desk to fix problem!",
                    "code"    => "DIGX_SWITCH_CARDLESS_001",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }
    // public static function debitToAccountWithFees($request)
    // {
    //     $object = new \stdClass();
    //     $object->service_name = $request->service_name ;
    //     $object->account_id =  $request->account_id;
    //     $object->amount = $request->amount->amount+$request->fee->amount;
    //     $object->currency = $request->amount->currency;
    //     $object->fee = $request->fee->amount;
    //     $object->remarks = $request->remarks;


    //     $result=static::debitToAccount($object);
    //     if ($result->status->message->code == "0") {
    //         $amount =new PaymentResultData(
    //             $result->getAdditionalData()["referenceId"],
    //             $result->getAdditionalData()["externalReferenceId"],
    //             InvoiceTransactionsTypeEnum::Payment->value
    //         );
    //         return GeneralResponseData::from(array(
    //             'status'=>[
    //                 "result"    => "SUCCESSFUL",
    //                 "contextID" => "",
    //                 "message"   => [
    //                     "title"   => "Successfully recharge customer account",
    //                     "detail"  => "Successfully recharge customer account",
    //                     "code"    => "0",
    //                     "type"    => "INFO"
    //                 ]
    //             ]
    //         ))->additional([
    //             'payment'=>[
    //                 'amount'=>$amount,
    //             ]
    //         ]);
    //     }
    //     return $result;
    // }
    public static function debitToAccountWithFees($request)
    {
        $object = new \stdClass();
        $object->service_name = $request->service_name ;
        $object->account_id =  $request->account_id;
        $object->amount = $request->amount->amount;
        $object->currency = $request->amount->currency;
        $object->remarks = $request->remarks;


        $result=static::debitToAccount($object);
        if ($result->status->message->code == "0") {
            $amount =new PaymentResultData(
                $result->getAdditionalData()["referenceId"],
                $result->getAdditionalData()["externalReferenceId"],
                InvoiceTransactionsTypeEnum::Payment->value
            );
            if($request->fee->amount>0){
                //Fee Payment
                $object = new \stdClass();
                $object->service_name = $request->fee_service_name;
                $object->account_id =  $request->account_id;
                $object->amount = $request->fee->amount;
                $object->currency = $request->fee->currency;
                $object->remarks = $request->remarksFee;

                $result=static::debitToAccount($object);
                if ($result->status->message->code == "0") {
                    $fee =new PaymentResultData(
                        $result->getAdditionalData()["referenceId"],
                        $result->getAdditionalData()["externalReferenceId"],
                        InvoiceTransactionsTypeEnum::Payment->value
                    );

                    return GeneralResponseData::from(array(
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   => "Successfully recharge customer account",
                                "detail"  => "Successfully recharge customer account",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ))->additional([
                        'payment'=>[
                            'amount'=>$amount,
                            'fee'   =>$fee,
                        ]
                    ]);
                }else{
                    $object=new \stdClass();
                    $object->reference_id   = $amount->referenceId;
                    $object->service_name   = $request->service_name;
                    $object->account_id     = $request->account_id;

                    $reverseResult=static::reverseToAccount($object);
                    if($reverseResult->status->message->code=="0"){
                        $amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                    }
                    $result->additional([
                        'payment'=>[
                            'amount'=>$amount,
                        ]
                    ]);
                }
            }else{
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "Successfully recharge customer account",
                            "detail"  => "Successfully recharge customer account",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'payment'=>[
                        'amount'=>$amount
                    ]
                ]);
            }

        }
        return $result;
    }
    public static function accountToDebit($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }
        $response = Http::withToken($flexService->settings->token->access_token)
            ->post(
                $flexService->settings->url."/banky/post_crtrt_trx",
                [
                    "req_ref_id"=>  "{$request->reference_id}",
                    "instid"    => $flexService->settings->instid,
                    "serv_name" => $request->service_name,
                    "trx_type"  => "dr",
                    "to_account"=> $request->account_id,
                    "amount"    => $request->amount,
                    "currency"  => $request->currency,
                    "remark"    => $request->remarks
                ]
            );


        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::accountToDebit($request);
        }

        $result=$response->object();
        if($result->result_code=="0"){
            $partyId=AccountIdData::from([
                "value"=> $request->account_id
            ])->partyId();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Credit from account"),
                    'body'=>sprintf(__("Successfully credit to account [%s] for [%s]"),
                        $request->account_id,
                        $request->remarks
                    ),
                    'type'=>'operation',
                ]
            ],$partyId);
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully claim the amount to customer account!"),
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ))->additional([
                'referenceId'=>"{$request->reference_id}",
                'externalReferenceId'=>$result->fccref??"",
            ]);
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[GIFT-INVOICE]",
                "message"   => [
                    "title"   => "Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->result_desc??"",
                    "code"    => "DIGX_SWITCH_Gift_001",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }
    public static function reverseToAccount($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        $response = Http::withToken($flexService->settings->token->access_token)
        ->post($flexService->settings->url."/banky/revers_rt",[
                "req_ref_id"=>  "{$request->reference_id}",
                "instid"=> $flexService->settings->instid,
                "serv_name" => $request->service_name,
                "accountid"=> $request->account_id
        ]);
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::reverseToAccount($request);
        }

        $result=$response->object();
        if(isset($result->result_code) && $result->result_code == "0"){
            $partyId=AccountIdData::from([
                "value"=> $request->account_id
            ])->partyId();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Reverse to account"),
                    'body'=>sprintf(__("Successfully reverse to account [%s] for [%s]"),
                        $request->account_id,
                        $request->reference_id
                    ),
                    'type'=>'operation',
                ]
            ],$partyId);

            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "Successfully reverse amount to customer account",
                        "detail"  => "Successfully reverse amount to customer account",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ));
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" =>"[REVERSE_SERVER] - {$result->result_code} - {$result->result_status}",
                "message"   => [
                    "title"   => "Server enabled to reverse amount to customer account!",
                    "detail"  => $result->result_desc??"",
                    "code"    => "DIGX_SWITCH_CARDLESS_104",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }


    public static function customerEnInfo($request)
    {

        $flexService=new FlexService();

        $response = rescue(function () use($flexService,$request){
            try {
                return Http::timeout(1)
                //->withToken($flexService->settings->token->access_token)
                ->post(
                    $flexService->settings->url.($flexService->isTest?"/ykb_api/custinfo":"/yapi/get_custinfo"),
                    [
                        "req_ref_id"    => $flexService->settings->instid,
                        "instid"        => $flexService->settings->instid,
                        "password"      => "BaNkY134#2341",
                        "custno"        => $request->account_id,
                        // "mobilenumber"  => $request->amount,
                        // "idnum"         => $request->currency
                    ]
                );
            } catch (\Illuminate\Http\Client\ConnectionException $e) {
                return $e->getMessage();
            }
        }, function ($e) {
            return $e->getMessage();
        });

        if(!is_string($response)){
            $result=$response->object();

            if(($result->email_address??"")!=""){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'enInfo'=>$result,
                ]);
            }
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YKP_SERVER] - ".($result->result_code??""),
                    "message"   => [
                        "title"   => "Can't process your order, please connect help desk to fix problem!",
                        "detail"  => $result->result_desc??"",
                        "code"    => "DIGX_SWITCH_YKP_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[YKP_SERVER]",
                "message"   => [
                    "title"   => "Can't process your order, please connect help desk to fix problem!",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_YKP_001",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }


    public static function activateNewCurrencyAccount($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }
        $response = Http::withToken($flexService->settings->token->access_token)
            ->post(
                $flexService->settings->url."/banky/new_account",
                // [
                //     "req_ref_id"    =>"ssdfd9FF6",
                //     "instid"        =>"banky",
                //     "custno"        =>"0151336",
                //     "brn_cd"        =>402,
                //     "currency"      =>"YER",
                //     "accountclass"  =>"SDCA"
                // ]
                [
                    "req_ref_id"    => $request->referenceId,
                    "instid"        => $flexService->settings->instid,
                    "custno"        => $request->partyId,
                    "brn_cd"        => $request->branchId,
                    "currency"      => $request->currencyId,//"YER"
                    "accountclass"  => $request->productId//"SDCA"
                ]
            );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::activateNewCurrencyAccount($request);
        }

        $result=$response->object();
        if(($result->result_code??"-1")=="0"){
            $partyId=AccountIdData::from([
                "value"=> $request->accountId
            ])->partyId();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Open account"),
                    'body'=>sprintf(__("Successfully open account with currency [%s] from account [%s]"),
                        $request->currencyId,
                        $request->accountId
                    ),
                    'type'=>'operation',
                ]
            ],$partyId);
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => sprintf(trans("successfully_open_new_currency_account"),
                            $request->currencyId,
                            $request->accountId
                        ),
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ))
            ->additional([
                'openedAccountId'=>$result->accountid??"",
            ]);
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[CARDLESS_SERVER] - ".($result->result_code??'')." - ".($result->result_status??''),
                "message"   => [
                    "title"   => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_OPEN_CURRENCY_ACCOUNT_003",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }

    public static function activateDormantAccount($request)
    {
        $flexService=new FlexService();

        $status=$flexService->getAccessToken(($request->retry??-1)>=0);
        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        $response = Http::withToken($flexService->settings->token->access_token)
        ->post(
            $flexService->settings->url."/banky/active_dorm_acct",
            [
                "req_ref_id"    => $request->referenceId,
                "instid"        => $flexService->settings->instid,
                "accountid"     => $request->accountId,
                "authId"        => $request->authId,
                "remarks"       => "Activate dormant account {$request->accountId}"
            ]
        );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::activateDormantAccount($request);
        }

        $result=$response->object();
        if(($result->result_code??"-1")=="0"){
            $partyId=AccountIdData::from([
                "value"=> $request->accountId
            ])->partyId();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Activate account"),
                    'body'=>sprintf(__("Successfully activate dormant account [%s]"),
                        $request->accountId
                    ),
                    'type'=>'operation',
                ]
            ],$partyId);
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully activate dormant account"),
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ));
        }

        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[CARDLESS_SERVER] - ".($result->result_code??'')." - ".($result->result_status??''),
                "message"   => [
                    "title"   => __($result->result_desc??"Can't process your order, please connect help desk to fix problem!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_ACTIVATE_DORMANT_ACCOUNT_002",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }
    public static function getCustomerCreditCardInfo($request)
    {

        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        $accoutId=AccountIdData::from(["value"=>$request->account_id]);

        $response = rescue(function () use($flexService,$accoutId){
            return Http::timeout(1)
            ->withToken($flexService->settings->token->access_token)
            ->post(
                $flexService->settings->url."/banky/query_credit_card",
                // [
                //    "req_ref_id": "402STAN223100001",
                //     "instid"        =>"banky",
                //     "accountid"        =>"4020178800YERCUCS018",
                //     "custno"        =>"0178800",
                //     "brn_cd"      =>"402"
                // ]
                [
                    "instid"        => $flexService->settings->instid,
                    "custno"        => $accoutId->partyId(),
                    "accountid"     => $accoutId->value,
                    "brn_cd"        => $accoutId->branchId()
                ]
            );
        }, function ($e) {
            return $e->getMessage();
        });

        $cards=[];
        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=($request->retry??-1)+1;
                return static::getCustomerCreditCardInfo($request);
            }
            $result=$response->object();

            if(($result->result_code??"-1")=="0"){
                $_cards=$result->cards_details??[];
                //$_cards=CreditCardData::collect(is_array($_cards)?$_cards:[$_cards],DataCollection::class);
                $references=[];
                foreach ($_cards as $card) {
                    $references[]=$card->request_reference_no;
                    //\Log::info("reference:".$card->request_reference_no);

                }
                $request->references=$references;
                $cards=$flexService->_getCustomerCreditCardInfoDetails($flexService,$request);

            }
        }


        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "Successfully open new currency account",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'creditCards'=>$cards,
        ]);
    }

    protected function _getCustomerCreditCardInfoDetails($flexService,$request)
    {

        $accoutId=AccountIdData::from(["value"=>$request->account_id]);
        $response =Http::pool(fn (Pool $pool) => collect($request->references)->map(function ($reference,$key) use($pool,$flexService,$request,$accoutId) {
            return $pool->as("$key")
                ->withToken($flexService->settings->token->access_token)
                ->post($flexService->settings->url."/banky/query_credit_card",[
                    "req_ref_id"    => $reference,
                    "request_reference_no"    => $reference,
                    "instid"        => $flexService->settings->instid,
                    "custno"        => $accoutId->partyId(),
                    "accountid"     => $accoutId->value,
                    "brn_cd"        => $accoutId->branchId()
                ]);
        })->toArray());


        $cards=collect($request->references)->mapWithKeys(function ($reference,$key) use($response) {
            //\Log::info("response:".json_encode($response["$key"]->object()));
            $result=$response["$key"]->object();
            $cards=[];
            if(($result->result_code??"-1")=="0"){
                $cards=$result->credit_card_details??[];
            }
            return ["$key" => $cards];
        })->flatten(1)->values()->all();
        $cards=CreditCardData::collect(is_array($cards)?$cards:[$cards],DataCollection::class);

        return $cards;
    }


    public static function goldRequest($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }
        $response = Http::withToken($flexService->settings->token->access_token)
            ->post(
                $flexService->settings->url."/banky/withdraw_order",
                [
                    "req_ref_id"=> $request->reference_id,
                    "instid"    => $flexService->settings->instid,
                    "brn_cd"    => $request->branchId,
                    "accountid" => $request->account_id,
                    "amount"    => $request->amount,
                    "currency"  => $request->currency,
                    "remarks"    => $request->remarks
                ]
            );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::goldRequest($request);
        }

        $result=$response->object();
        if($result->result_code=="0"){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully sent gold withdraw request"),
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ));
        }
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => sprintf("[GOLD_SERVER] - {%s}",$result->result_code),
                "message"   => [
                    "title"   => "Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->result_desc??"",
                    "code"    => "DIGX_SWITCH_GOLD_001",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }
    public static function goldRequestList($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }
        $response = Http::withToken($flexService->settings->token->access_token)
            ->post(
                $flexService->settings->url."/banky/get_withdraw_orders",
                [
                    "req_ref_id"=> $request->reference_id,
                    "instid"    => $flexService->settings->instid,
                    "custno"    => $request->partyId
                ]
            );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::goldRequestList($request);
        }

        $result=$response->object();
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'orders'=>GoldWithdrawData::collect($result->orders??[],DataCollection::class)
        ]);
    }

    public static function chargeFee($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        $response = Http::withToken($flexService->settings->token->access_token)
            ->acceptJson()
            ->post(
                $flexService->settings->url."/banky/query_charge_prodv",
                [
                    "instid"    => $flexService->settings->instid,
                    "prod_cd"    => $request->chargeProductId,
                    "brn_cd"    => $request->branchId
                ]
            );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::chargeFee($request);
        }

        $result=$response->object();
        //\Log::error($response->json());
        if(isset($result->{'Product-Details-Full'}->{'Charge-Details'}->{'Charge-Acclass-Details'})){
            $chargeDetails=$result->{'Product-Details-Full'}->{'Charge-Details'};
            $_fees=collect($chargeDetails->{'Charge-Acclass-Details'}??[])
            ->filter(function ($element,int $key) use($request){
                //only in range & product id.
                return ($element->ACLASS??$element->aclass)==$request->productId;
            })->values();
            $fees=[];
            foreach ($_fees as $fee) {
                if(isset($fee->{'Charge-Slab-Details'})){
                    $products=$fee->{'Charge-Slab-Details'}??[];
                    if (!empty($products) && (is_object($products) || !array_is_list($products))) {
                        $products = [$products];
                    }
                    $products=collect($products)
                    ->map(function ($element,int $key) use ($fee){
                        return [
                            "ACLASS"=> $fee->ACLASS??$fee->aclass,
                            "CCY"=> $fee->CCY??$fee->ccy,
                            "SLABAMT"=> $element->SLABAMT??$element->slabamt,
                            "CHGAMT"=> $element->CHGAMT??$element->chgamt??0,
                            "CHGRT"=> $element->CHGRT??$element->chgrt??0,
                        ];
                    });
                    $fees=array_merge($fees,$products->toArray());
                }else{
                    $fees[]=$fee;
                }
            }

            $fees=ChargeFeeData::collect($fees,DataCollection::class);

            $equlivent= $request->currency==CurrencyTypeEnum::G24->value?
                    31.1:1;
            $amount=$request->amount*$equlivent;
            //\Log::error($fees->toArray());
            $filteredFees=$fees->filter(function (ChargeFeeData $element,int $key) use($amount){
                 //only in range & product id.
                 return $element->amount->amount<=$amount;
            });

            if(empty($filteredFees->toArray())){
                //\Log::error($filteredFees->toArray());
                $filteredFees=$fees->toCollection()->sortBy(function (ChargeFeeData $element, int $key) {
                   return $element->amount->amount;
               });
            }else{
                $filteredFees=$filteredFees->toCollection()->sortByDesc(function (ChargeFeeData $element, int $key) {
                    return $element->amount->amount;
                });
            }

            $fee=$filteredFees->first();
            if(is_null($fee)){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "[FLEX_SERVER]",
                        "message"   => [
                            "title"   => __("Can't calculate service fee for your account, please connect help desk to fix problem!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_FLIX_002",
                            "type"    => "ERROR"
                        ]
                    ]
                ));
            }

            $fee->fee->currency=$chargeDetails->chgccy??$chargeDetails->CHGCCY;
            if($fee->fee->currency=="LCY" /*|| $fee->fee->currency=="ACY"*/){
                $fee->fee->currency=CurrencyTypeEnum::YER->value;
            }
            if($fee->precent->amount>0){
                $fee->fee->amount=ceil($amount*$fee->precent->amount);
            }
            if(in_array($fee->fee->currency,CurrencyTypeEnum::values())){
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'fee'=>$fee
                ]);
            }

            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => sprintf("[GOLD_SERVER] - {%s}",$result->result_code??''),
                    "message"   => [
                        "title"   => "Charge currency not correct! [{$fee->fee->currency}]",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_002",
                        "type"    => "ERROR"
                    ]
                ]
            ));

        }
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => sprintf("[GOLD_SERVER] - {%s}",$result->result_code??''),
                "message"   => [
                    "title"   => "Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->result_desc??"",
                    "code"    => "DIGX_SWITCH_GOLD_002",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }


    public static function recordEntries($request)
    {
        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }

        $response = Http::withToken($flexService->settings->token->access_token)
            ->post(
                $flexService->settings->url."/banky/pc_fx_trans",
                //$flexService->settings->url."/banky/exchange_detrans",
                [
                    "req_ref_id"  =>  $request->reference_id ,//substr($request->reference_id,0, 3)."b" ,
                    "instid"    => $flexService->settings->instid,
                    "agentid"   => $flexService->settings->instid,
                    "trans_journals"  => $request->journals
                ]
            );

        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::recordEntries($request);
        }

        $result=$response->object();
        if($result->result_code=="0" && isset($result->reference_no)){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully record entries"),
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ))->additional([
                'externalReferenceId'=>$result->reference_no??"",
            ]);
        }
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => sprintf("[GOLD_SERVER] - {%s}",$result->result_code??''),
                "message"   => [
                    "title"   => "Can't process your order, please connect help desk to fix problem!",
                    "detail"  => $result->result_desc??"",
                    "code"    => "DIGX_SWITCH_GOLD_002",
                    "type"    => "ERROR"
                ]
            ]
        ));
    }

    public static function oilPayment($request)
    {

        $flexService=new FlexService();
        $status=$flexService->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "$status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                ]
            ));
        }
        $response = rescue(function () use($flexService,$request){
            try {
                return Http::withToken($flexService->settings->token->access_token)
                ->post(
                    $flexService->settings->url."/banky/cby_clearing",
                    [
                        "instid"    => $flexService->settings->instid,
                        "req_ref_id"=>  "{$request->reference_id}",
                        "amount"    => $request->amount->amount,
                        "currency"  => $request->amount->currency,
                        "description"  => $request->remarks,

                        "db_account_no"    => $request->debitAccountId->value,
                        "db_account_name"  => $request->debitAccountId->displayValue,
                        "db_branch_no"     => "0",
                        "db_branch_name"   => "0",
                        "cr_account_no"    => $request->creditAccountId->value,
                        "cr_account_name"  => $request->creditAccountId->displayValue,
                        "customer_account_id"=>$request->customerAccountId,
                        "opt_type"=>$request->type
                        //"bank_refrence"    => "YKBA_20250205_0002"
                    ]
                );
            } catch (\Illuminate\Http\Client\ConnectionException $e) {
                return $e->getMessage();
            }
        }, function ($e) {
            return $e->getMessage();
        });



        if(!is_string($response)){
            if($response->status()==401){
                $request->retry=($request->retry??-1)+1;
                return static::oilPayment($request);
            }

            $result=$response->object();
            if(isset($result->result_code)){
                if($result->result_code=="0"){
                    $cbyResp=json_decode($result->cby_resp??"{}");
                    return GeneralResponseData::from(array(
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   => __("Successfully make payment!"),
                                "detail"  => "",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ))->additional([
                        'transactionId'=>"{$result->transaction_no}",
                        'externalReferenceId'=>isset($cbyResp->LinkCode)?$cbyResp?->LinkCode:"",
                    ]);
                }
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "[OIL-PAYMENT]",
                        "message"   => [
                            "title"   => $result->result_desc??__("Can't process your order, please connect help desk to fix problem!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_OIL_001",
                            "type"    => "ERROR"
                        ]
                    ]
                ));
            }

        }
        return GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[OIL-PAYMENT]",
                "message"   => [
                    "title"   => __("Service provider take long time to response, customer service will review this transaction & call you back soon!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_TIMEOUT",
                    "type"    => "ERROR"
                ]
            ]
        ));

    }

}
