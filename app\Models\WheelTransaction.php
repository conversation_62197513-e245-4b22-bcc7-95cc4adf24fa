<?php

namespace App\Models;

use App\Enums\TransactionStatusEnum;
use App\Scopes\CustomerScope;
use App\Traits\DatabaseCompatibility;
use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;

class WheelTransaction extends Model
{
    use DatabaseCompatibility;

    use HasFactory;
    protected $dates = ['created_at','updated_at'];

    protected $fillable = ['party_id','wheel_item','payment_result','account_id','transaction_id', 'status','party_name','note','user_id','created_at'];
    protected $casts = [
        'account_id' => 'object',
        'wheel_item' => 'object',
        'payment_result'=>'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
         static::creating(function ($query) {
             if(is_null($query->party_id))
            $query->party_id = auth()->user()->id;
        });
    }
    public function transaction()
    {
        return $this->hasOne('App\Models\WheelTransaction', 'transaction_id');
    }
    public function user()
    {
        return $this->belongsTo('App\Models\User',"user_id");
    }

    public static function getCustomerWheelCounts():int
    {
        return static::whereDoesntHave('transaction')
        ->where('status',TransactionStatusEnum::COMPLETED->value)
        ->whereNull('wheel_item')
        ->count();
    }
    public static function isCustomerTryWheelAfterDate():bool
    {
        $start=\Carbon\Carbon::now()->startOfDay()->toDateTimeString();
        $whareDate=(new static())->getDateByConnection("'$start'",'YYYY-MM-DD HH24:MI:SS');

        $counts=static::whereDoesntHave('transaction')
        ->whereIn('status',[TransactionStatusEnum::COMPLETED->value,TransactionStatusEnum::PENDING->value,TransactionStatusEnum::ERROR->value])
        ->whereNotNull('wheel_item')
        ->whereDate('created_at','>=',DB::raw($whareDate))
        ->count();

        return $counts>0;
    }

    public static function amountSumOfTodayTransaction()
    {
        $start=\Carbon\Carbon::now()->startOfDay()->toDateTimeString();
        $whareDate=(new static())->getDateByConnection("'$start'",'YYYY-MM-DD HH24:MI:SS');

        $transaction=static::withoutGlobalScope(CustomerScope::class)
        ->select( DB::raw("sum(json_value(wheel_item,'$.value')) as total"))
        ->whereNotNull('wheel_item')
        ->whereDate('created_at','>=',DB::raw($whareDate))
        ->first();

        return $transaction->total;
    }

    public static function amountSumOfTodayTransactionById(string $id)
    {
        $start=\Carbon\Carbon::now()->startOfDay()->toDateTimeString();
        $whareDate=(new static())->getDateByConnection("'$start'",'YYYY-MM-DD HH24:MI:SS');

        $transaction=static::withoutGlobalScope(CustomerScope::class)
        ->select( DB::raw("sum(json_value(wheel_item,'$.value')) as total"))

        // ->whereIn('status',[
        //     TransactionStatusEnum::ERROR->value,
        //     TransactionStatusEnum::INIT->value,
        //     TransactionStatusEnum::COMPLETED->value
        //     ])
        ->whereNotNull('wheel_item')
        ->where('wheel_item->id',$id)
        ->whereDate('created_at','>=',DB::raw($whareDate))
        ->first();

        return $transaction->total;
    }
    protected function serializeDate(DateTimeInterface $date){
        return $date->timezone('Asia/Aden')->format('Y-m-d H:i:s');
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}

