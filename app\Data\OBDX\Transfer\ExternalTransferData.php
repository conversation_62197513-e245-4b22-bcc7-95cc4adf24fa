<?php

namespace App\Data\OBDX\Transfer;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use Arr;


class ExternalTransferData extends BaseNonNullableData
{
    public function __construct(
        public ?AccountIdData $debitAccountId,
        public ?CurrencyAmountData $amount,
        public ?string $receiverNumber,
        public ?string $receiverName,
        public ?string $remarks,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['debitAccountId']= data_get($properties,"paymentDetails.debitAccountId");
        $properties['amount']= data_get($properties,"paymentDetails.amount");
        $properties['receiverNumber']=data_get($properties,"paymentDetails.creditAccountId.value");
        $properties['receiverName']= html_entity_decode(data_get($properties,"paymentDetails.creditAccountId.displayValue"));

        //if(Arr::has($properties,"paymentDetails.remarks")){
           $properties['remarks']= html_entity_decode(data_get($properties,"paymentDetails.remarks"));
        //}
        return $properties;
    }

}
