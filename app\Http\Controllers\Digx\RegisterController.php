<?php

namespace App\Http\Controllers\Digx;
use App\Data\GeneralResponseData;
use App\Enums\TransactionStatusEnum;
use App\Services\OBDX\LoginService;

use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\Registration;
use Illuminate\Http\Request;
use App\Services\AnaService;

class RegisterController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $appConfig=app(\App\Settings\ConfigSettings::class)->appConfig;
        return response()->json([
            "config"=>[
                "allowBankyActivation"=>$appConfig->allowBankyActivation??0,
                "allowAnaWeb"=>(request()->header('appVersion')??0)<177?0:($appConfig->allowAnaWeb??0),
                "allowGuest"=>$appConfig->allowGuest??0,
            ]
        ]);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function create()
    {
        $type=request()->type;
        $contents = \File::get(base_path( "/resources/mocks/utils/ekyc{$type}.json"));
        return response()->json(json_decode($contents));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {

        $validator=validator()->make($request->all(),[
            //"dependents_children"           => "nullable|numeric|max_digits:2",

            "emp_current_desig_job_nm"      => "max:105",
            "emp_previous_desig_company_nm" => "max:105",
            "emp_telephone"                 => "max:105",
            "emp_salary"                    => "nullable|numeric|max_digits:22",
            "emp_currency_salary"           => "max:3",
            "emp_previous_desig_job_nm"     => "max:105",
            "emp_years_num"                 => "nullable|numeric|max_digits:2",

            "expense_rent"                  => "nullable|numeric|max_digits:22",
            "expense_insurance"             => "nullable|numeric|max_digits:22",
            "expense_loan_payments"         => "nullable|numeric|max_digits:22",
            "expense_othexp"                => "nullable|numeric|max_digits:22",
            "expense_house_value"           => "nullable|numeric|max_digits:22",
            "expense_no_of_credit_cards"    => "nullable|numeric|max_digits:2",

            'account_ccy'       =>"required",
            "brn_cd"            => "required",
            "customer_category" => "required",
            //"emp_status"        => "required",
            "residnt"           => "required",
            "us_res_status"     => "required",
            //"maritalstat"       => "required",
            "incoming_fund"     => "required",
            //"ovrsincsrc"      => "required",
            "account_prps"      => "required",
            //"media_type"        => "required",
            //"lang"              => "required",
            "authId"            => "required"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-REGISTER",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_REGISTER_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $registration=Registration::where("device_key",$request->header("deviceKey"))->first();
        $registration??=Registration::create([
            'status'        => TransactionStatusEnum::INIT->value,
            "reference_id"  => round(microtime(true)*1000),
            "type"          => "registration",
            "device_key"    => $request->header("deviceKey"),
            "request"       => $request->all(),
        ]);
        LogItem::store($registration);

        $request->request->add([
            'reference_id' => $registration->reference_id,
        ]);

        $result=AnaService::registerAccount($request);

        if($result->status->message->code=="0" || $result->status->message->code=="DIGX_SWITCH_REGISTER_002"){
            if($result->status->message->code=="DIGX_SWITCH_REGISTER_002"){
                $registration->status=TransactionStatusEnum::ERROR->value;
            }else{
                $registration->status=TransactionStatusEnum::COMPLETED->value;
            }
            if(isset($result->getAdditionalData()["response"])){
                $registration->response=$result->getAdditionalData()["response"];
            }
            $registration->save();

            return response()->json(GeneralResponseData::from($result->toArray()));
        }

        $registration->status=TransactionStatusEnum::ERROR->value;
        if(isset($result->getAdditionalData()["response"])){
            $registration->response=$result->getAdditionalData()["response"];
        }
        $registration->save();

        return response()->json(GeneralResponseData::from($result->toArray()),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        $validator=validator()->make($request->all(),[
            //'account_ccy'       =>"required|max:3|min:3|in:YER,USD",
            "brn_cd"            => "required|numeric|min_digits:3|max_digits:3",
            "custno"            => "required",
            "authId"            => "required"
        ]);
        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-REGISTER",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_REGISTER_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $registration=Registration::where("device_key",$request->header("deviceKey"))->first();
        $registration??=Registration::create([
            'status'        => TransactionStatusEnum::INIT->value,
            "reference_id"  => round(microtime(true)*1000),
            "type"          => "activate",
            "device_key"    => $request->header("deviceKey"),
            "request"       => $request->all(),
        ]);
        LogItem::store($registration);

        $request->request->add([
            'reference_id' => $registration->reference_id,
        ]);

        $result=AnaService::activateOBDXAccount($request);

        if($result->status->message->code=="0" || $result->status->message->code=="DIGX_SWITCH_REGISTER_002"){
            if($result->status->message->code=="DIGX_SWITCH_REGISTER_002"){
                $registration->status=TransactionStatusEnum::ERROR->value;
            }else{
                $registration->status=TransactionStatusEnum::COMPLETED->value;
            }
            if(isset($result->getAdditionalData()["response"])){
                $registration->response=$result->getAdditionalData()["response"];
            }
            $registration->save();

            return response()->json(GeneralResponseData::from($result->toArray()));
        }

        $registration->status=TransactionStatusEnum::ERROR->value;
        if(isset($result->getAdditionalData()["response"])){
            $registration->response=$result->getAdditionalData()["response"];
        }
        $registration->save();

        return response()->json(GeneralResponseData::from($result->toArray()),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }

    public function guestRegister(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'firstName'         =>"required",
            "lastName"          => "required",
            "mobileNumber"       => "required",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-REGISTER",
                    "message"   => [
                        "title"   => "Parameters not correct",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_REGISTER_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        return LoginService::register();
    }

    public function guestVerify(Request $request)
    {
        return LoginService::verifyRegister();
    }
    public function guestPassword(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'registrationId'         =>"required",
            "signature"          => "required",
            "userId"       => "required",
            "newPassword" => "required",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-REGISTER",
                    "message"   => [
                        "title"   => "Parameters not correct",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_REGISTER_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        return LoginService::passwordRegister();
    }
}
