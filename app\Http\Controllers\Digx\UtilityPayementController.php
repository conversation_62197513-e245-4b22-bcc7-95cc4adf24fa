<?php

namespace App\Http\Controllers\Digx;

use App\Data\GeneralResponseData;
use App\Http\Controllers\Controller;
use App\Services\UtilityPayementService;
use Illuminate\Http\Request;

class UtilityPayementController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $result=UtilityPayementService::catalog();

        return response()->json($result,200, [], JSON_OBJECT_AS_ARRAY);
    }
    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($number,Request $request)
    {
        $inputsToValidate=[
            'ac'=>"required|max:4|min:4|in:4001,4006,4007",
            'sc'=>"required|max:5|min:1|in:42103,42105,42106,42107,42108,42113,1"
        ];
        if(in_array($request->sc??"",["42108"])){
            $inputsToValidate['sac']="required";
        }
        if(in_array($request->sc??"",["1"])){
            $inputsToValidate['item']="required";
        }
        $validator=validator()->make($request->all(),$inputsToValidate);
        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "UTILITY-PAYMENT-BALANCE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_UTILITY-PAYMENT-BALANCE_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $object=new \stdClass();
        $object->ac=$request->ac;
        $object->sc=$request->sc;
        $object->sno=$number;
        if(in_array($request->sc??"",["42108"])){
            $object->sac=$request->sac;
        }
        if(in_array($request->sc??"",["1"])){
            $object->item=$request->item;
        }

        $result=UtilityPayementService::query($object);

        return response()->json($result,200, [], JSON_OBJECT_AS_ARRAY);
    }

}
