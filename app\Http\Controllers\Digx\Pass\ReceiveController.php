<?php

namespace App\Http\Controllers\Digx\Pass;
use App\Data\AccountConfigData;
use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\Pass\PassCreateRequestData;
use App\Data\PaymentResultData;
use App\Data\ReceiptData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Jobs\ProcessClaimedTimeoutWasil;
use App\LogItem;
use App\Models\Pass;
use App\Models\PassTransaction;
use App\Services\NotificationService;
use App\Services\OBDX\CustomerService;
use App\Services\PassService;
use App\Data\AccountData;

use App\Http\Controllers\Controller;

use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;
use App\Services\FlexService;
use App\Enums\TransactionStatusEnum;

class ReceiveController extends Controller
{

    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::WASIL
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $transactions=Pass::with(['transactions'=>function($query){
            return $query->select('id','pass_id','type','status','account_id','amount','fee')
            ->where('status', TransactionStatusEnum::COMPLETED->value);
        }])
        ->select('id','remarks','created_at as date')
        ->where('status', TransactionStatusEnum::COMPLETED->value)
        ->where('type', InvoiceTransactionsTypeEnum::Claim->value)
        ->skip($request->from)
        ->take($request->limit)
        ->orderBy("id","DESC")
        ->get();

        return response()->json($transactions);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $pass
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $mpt)
    {
        $validator=validator()->make($request->all(),[
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required|max:3|min:3",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-HARVEST",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_HARVEST_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $requestData=PassCreateRequestData::from($request->all()+[
            'reference_id'          =>uniqid(),
            'receiver_mobile'       =>html_entity_decode(auth()->user()->phone),
            "external_reference_id" =>$mpt
        ]);

        $result=PassService::find($requestData);
        if($result->status->message->code=="0"){
            $details=$result->getAdditionalData()['details'];
            return response()->json(GeneralResponseData::from($result->toArray())
                ->additional([
                'data'=>[
                    "tracking_code"=>$details->mpt,
                    "amount"=>CurrencyAmountData::from([
                        "amount"=>$details->amount,
                        "currency"=>$details->currency,
                    ])->toArray(),
                    "sender_account"=>$details->sender_account_no,
                    "sender_name"=>$details->sender_name??$details->sender_agent_name,
                    "sender_agent"=>$details->sender_agent_name,
                    "date"=>$details->creation_date,
                    ]
                ])
            );
        }else if($result->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
            $result->status->message->title=__("The remittance not found!");
            return response()->json($result);
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);

    }

    protected function getReceiptData(Pass $item,?PassTransaction $transaction=null): ReceiptData
    {
        $transaction??=$item->transactions->last();
        $accountId=AccountIdData::from($transaction->account_id);
        $title=__("Funds transfer") ." (".__('Wasil').")"." ".__("wasil_type_". $transaction->type);
        return ReceiptData::from([
            "id"=> $item->id,
            "date"=> date_format(date_create($item->created_at), "Y-m-d H:i:s"),
            "title"=>  $title,
            "beneficiary"=>$accountId->fullName()."\n". $accountId->value,
            "statement"=>$title,
            "details"=> [
                "debitAccountId"=> AccountIdData::from([
                    "value"=> $transaction->extra->sender_account_no,
                    "displayValue"=> $transaction->extra?->sender_name??""
                ])->toArray() ,
                "remittanceId"=> $transaction->extra->mpt,
                "amount"=>$transaction->amount,
                "fee"=>$transaction->fee,
                "remarks"=>$item->remarks

            ]
        ]);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            "trackingCode"=> "required",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required|max:3|min:3",
            'creditAccountId.value'=>"required|max:20|min:20",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-HARVEST",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_HARVEST_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $requestData=PassCreateRequestData::from([
            'account_id'            =>$request->creditAccountId,
            'reference_id'          =>uniqid(),
            'receiver_mobile'       =>html_entity_decode(auth()->user()->phone),
            "amount"                => $request->amount,
            "external_reference_id" => $request->trackingCode,
        ]);

        $result=PassService::find($requestData);
        if($result->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
            $result->status->message->title=__("The remittance not found!");
            return response()->json($result);
        }else if($result->status->message->code!="0"){
            return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }

        $details=$result->getAdditionalData()['details'];

        $valid=false;
        $message="Can't load your accounts!";
        $result=CustomerService::account($request,$requestData->accountId->value);
        if($result instanceof AccountData){
            $account=$result;
            $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$requestData->amount->currency &&
            $account->allowedService(AccountConfigData::waseelTransfer);
        }
        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-PASS",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }



        $pass=Pass::create([
            "type"              =>InvoiceTransactionsTypeEnum::Claim->value,
            "status"            =>TransactionStatusEnum::INIT->value,
            "remarks"           =>$request->remarks??null
        ]);
        LogItem::store($pass);

        $referenceId=uniqid();
        $passTransaction=PassTransaction::create([
            "pass_id"           =>$pass->id,
            "type"              =>InvoiceTransactionsTypeEnum::Claim->value,
            "status"            =>TransactionStatusEnum::INIT->value,
            "account_id"        =>$requestData->accountId->toArray(),
            "receiver_mobile"   =>$requestData->receiverMobile,
            "amount"            =>CurrencyAmountData::from([
                "amount"=>$details->amount,
                "currency"=>$details->currency,
            ])->toArray(),
            "reference_id"      =>$referenceId,
            "remarks"           =>sprintf(trans("wasil_receive_remark_message"),
                $requestData->externalReferenceId,
                $referenceId
            ),
            "extra"=>$details,
        ]);

        $requestData=PassCreateRequestData::from([
            'reference_id'          => $passTransaction->reference_id,
            "external_reference_id" => $details->transaction_reference,
            "row_version" => $details->row_version
        ]);
        $result=PassService::receive($requestData);

        if($result->status->message->code=="0"){
            //$gold->external_reference_id= $result->getAdditionalData()["externalReferenceId"];
            $pass->status=TransactionStatusEnum::COMPLETED->value;
            $pass->save();

            $passTransaction->extra= collect($passTransaction->extra)
            ->merge($result->getAdditionalData()["extra"]);//$result->getAdditionalData()["extra"];

            $passTransaction->external_reference_id= $passTransaction->extra->financial_reference??$passTransaction->extra->transaction_reference;
            $passTransaction->status=TransactionStatusEnum::COMPLETED->value;
            $passTransaction->save();

            /** Credit to customer account **/
            $object=new \stdClass();
            $object->reference_id   = $passTransaction->reference_id;
            $object->service_name   = ThirdPartyServiceNameData::wasil();
            $object->account_id     = $passTransaction->account_id->value;
            $object->amount         = $passTransaction->amount->amount;
            $object->currency       = $passTransaction->amount->currency;
            $object->remarks        = $passTransaction->remarks;

            $result=FlexService::accountToDebit($object);
            if($result->status->message->code=="0"){
                $passTransaction->payment_result= [
                    'amount'=>new PaymentResultData(
                        $result->getAdditionalData()["referenceId"],
                        $result->getAdditionalData()["externalReferenceId"],
                        InvoiceTransactionsTypeEnum::Claim->value
                    ),
                ];
                $passTransaction->save();
            }
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Collect money"),
                    'body'=>sprintf(__("Successfully collect remittance #[%s] sended by [%s] service"),
                        ($request->trackingCode??""),
                        __("Wasil")
                    ),
                    'type'=>'operation',
                ]
            ]);
            return response()->json(GeneralResponseData::from($result->toArray())
                ->additional([
                    'externalReferenceId'=>$passTransaction->external_reference_id,
                    'receipt'=> $this->getReceiptData($pass,$passTransaction)
                ])
            );
        // }else if($result->status->message->code=="DIGX_SWITCH_WASIL_TIMEOUT"){
        //     $pass->status=TransactionStatusEnum::PENDING->value;
        //     $pass->save();

        //     $passTransaction->status=TransactionStatusEnum::PENDING->value;
        //     $passTransaction->save();

        }else{
            $pass->status=TransactionStatusEnum::ERROR->value;
            $pass->save();
            $passTransaction->status=TransactionStatusEnum::ERROR->value;
            $passTransaction->save();

            ProcessClaimedTimeoutWasil::dispatch(null,$requestData,$pass,$passTransaction)
            ->onQueue('critical')
            ->delay(now()->addMinutes(1));
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);

    }
}
