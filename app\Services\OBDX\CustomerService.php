<?php

namespace App\Services\OBDX;
use App\Data\AccountData;
use App\Data\GeneralResponseData;
use App\Data\PaymentResultData;
use App\Data\StatusData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Models\AgentUser;
use App\Models\CustomerType;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Illuminate\Http\Request;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Support\Facades\Http;

use App;
use App\Enums\LimitTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\Models\PartyVerify;
use App\Services\FlexService;
use Illuminate\Support\Facades\Cookie;
use Spatie\LaravelData\DataCollection;

/**
 * Service to create and update orders
 */
class CustomerService
{
    public const defaultErrorResult=[
        'status'=>[
            "result"    => "SUCCESSFUL",
            "contextID" => "CUSTOMER-SERVICE",
            "message"   => [
                "title"   => "System cannot process the request currently. Please try later.",
                "detail"  => "Service",
                "code"    => "DIGX_PROD_DEF_0000",
                "type"    => "ERROR",
            ]
        ]
    ];
    protected $url;

    protected $cookies;
    public $headers;

    /**
     * Service to handle customer requestes
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function __construct(Request $request){
        $this->url="{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1";
        $this->cookies=CookieJar::fromArray($request->cookies->all(),"{$_ENV['OBDX_URL']}");
        $this->headers =  collect($request->headers->all())->except([
            "lang",
            "appversion",
            'host',
            'cookie',
            'content-length',
            "user-agent",
            "postman-token",
            "content-type",
            "token",
            "x-original-url",
            "x-challenge-response"
        ])->all();

        // if($request->hasHeader("X-CHALLENGE-RESPONSE")){
        //     $this->headers+=["X-CHALLENGE_RESPONSE"=>$request->header("X-CHALLENGE-RESPONSE")];
        // }
        $this->headers+=["accept-encoding"=>"identity","X-Requested-With"=>"com.ofss.ykb"];
    }
    /**
     * Register a stub callable that will intercept requests and be able to return stub responses.
     *
     * @param  string  $method
     * @param  string  $url
     * @param  callable|array $params
     * @param  callable|array $fake
     * @return mixed $response
     */
    function getHttpRequest(string $method,string $url,array $params=[],array $fake=[],?int $timeout=null){
        $request=Http::withOptions([
            'cookies' => $this->cookies,
        ]);
        //$request=$request->withHeaders($this->headers);
        if($method=='GET' && !empty($params)){
            $params=[
                'query' => $params,
            ];
        }else if($method!='GET' && !empty($params)){
            $params=[
                'json' => $params,
            ];
        }
        $headers=$this->headers;
        if($url=="me"){
            $headers=collect($this->headers)->all();
            $headers["accept-encoding"]="identity";
           // $headers["host"]= request()->header("x-forwarded-host")??null;
        }

        if($url=="CardInquiry"){
            $request=$request->timeout(1);
        }else if(!is_null($timeout)){
            $request=$request->timeout($timeout);
        }
        $response = rescue(function () use($headers,$method,$url,$params,$request){
            return $request
            ->withUserAgent("OIC-Authentication")
            ->withHeaders($headers)->send( $method,"{$this->url}/$url", $params);
        }, function ($e) {
            return $e->getMessage();
        });
        return $response;

    }

    /**
     * Inject user to request & session to request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse|null
     */
    public function getUser($partyVerify=null)
    {
        $request=request();
        $response=$this->getHttpRequest('GET','me',[],[]);
        if(is_string($response)){
            return response()->json(GeneralResponseData::from(static::defaultErrorResult),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }
        //Log::info( $request->url);
        $result=$response->object();
        if(isset($result->message->code) && $result->message->code=="DIGX_UM_042" && ($request->filled("j_username")|| $request->filled("oldPassword") || $request->hasHeader("X-CHALLENGE-RESPONSE"))){
            $userProfile=json_decode(json_encode(["firstLoginFlowDone"=>true]));

            $service=new CustomerService($request);
            $result=$service->_cardInfo($request,false);
            if($result->status->message->code=="0"){
                $cardInfo=$result->getAdditionalData()["cardInfo"];

                $userProfile->userName=$request->j_username;
                $userProfile->firstName=$cardInfo->firstName??"";
                $userProfile->middleName="";
                $userProfile->lastName=$cardInfo->lastName??"";

                if($request->filled("referenceNo")){
                    $partyVerify=PartyVerify::
                        where('id',$request->referenceNo)
                        ->where('device_key',$request->header("deviceKey"))
                        ->where('phone_number',$cardInfo->mobileNumber)
                        ->first();
                    $userProfile->partyId=json_decode(json_encode([
                        "value"=> $partyVerify->party_id
                    ]));
                }else if($request->hasHeader("X-CHALLENGE-RESPONSE")){
                    $challenge=json_decode($request->header("X-CHALLENGE-RESPONSE"));
                    $partyVerify=PartyVerify::
                        where('id',$challenge->referenceNo)
                        ->where('device_key',$request->header("deviceKey"))
                        ->where('phone_number',$cardInfo->mobileNumber)
                        ->first();
                    $userProfile->partyId=json_decode(json_encode([
                        "value"=> $partyVerify->party_id
                    ]));
                }else{
                    $userProfile->partyId=json_decode(json_encode([
                        "value"=> $request->j_username
                    ]));
                }



                $userProfile->phoneNumber=json_decode(json_encode([
                    "value"=> $cardInfo->mobileNumber??null
                ]));

                $cardInfo->firstName=null;
                $cardInfo->lastName=null;
                $userProfile->cardInfo=$cardInfo;

                $object=new \stdClass();
                $object->account_id=  $userProfile->partyId->value;
                $result=FlexService::customerEnInfo($object);
                if($result->status->message->code=="0"){
                    $enInfo=$result->getAdditionalData()["enInfo"];
                    $cardInfo=$userProfile->cardInfo;
                    $cardInfo->firstName=$enInfo->firstname??null;
                    $cardInfo->lastName=$enInfo->lastname??null;
                    $cardInfo->email=$enInfo->email_address;
                    $userProfile->emailId=json_decode(json_encode([
                        "value"=> $cardInfo->email
                    ]));
                    $userProfile->cardInfo=$cardInfo;
                }
                $userProfile->customerType=CustomerType::RETAIL;
                return json_decode(json_encode([
                    "status"=>$result->status,
                    "userProfile"=>$userProfile,
                ]));
            }


        }

        if((isset($result->status->message->code) && ($result->status->message->code??"-1")!="0") ||
             (isset($result->message->code) && ($result->message->code??"-1")!="0") || $response->status()!=200){
                $headers =  collect($response->headers())->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location','Location','Set-Cookie','set-cookie']);
                $headers->put("fromx","1");
                $responseNew=response($response->getBody());
                foreach ($request->cookies as $key => $value) {
                    $responseNew->withoutCookie($key);
                }

            return $responseNew
                    ->withHeaders($headers->all())
                    ->setStatusCode($response->status());
        }

        if(!isset($result->userProfile)){
            return null;
        }

        $userProfile=$result->userProfile;
        if(in_array('CorporateUser',$result->userProfile->roles)){
            //$allPrivilege=AdminService::approvalRules($request,$userProfile->partyId->value,$userProfile->userName);
            $allPrivilege=AdminService::preferences($request,$userProfile->partyId->value);
            $isFullAccess=(($allPrivilege && in_array('Maker',$result->userProfile->roles)) ||
            (in_array('Maker',$result->userProfile->roles) && in_array('Checker',$result->userProfile->roles)));

            //if($userProfile->userName!="600000SH"){
                if(!$isFullAccess){
                    return response()->json(
                    GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "[CardInquiry]",
                            "message"   => [
                                "title"   => __("You can't use Banky Lite app, Please use IBank!"),
                                "detail"  => "",
                                "code"    => "DIGX_PROD_DEF_0000",
                                "type"    => "ERROR"
                            ]
                            ]
                        ]),
                        \Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED
                    );
                }
            //}

            $userProfile->customerRole=$isFullAccess?'full':(in_array('Maker',$result->userProfile->roles)?'maker':(in_array('Checker',$result->userProfile->roles)?'checker':''));
            $userProfile->customerType=CustomerType::CORPORATE;
        }else if(!in_array('Customer',$result->userProfile->roles)){
            $userProfile->customerType=CustomerType::GUEST;
        }else{
            $userProfile->customerRole='basic';
            $userProfile->customerType=CustomerType::RETAIL;
        }
        // if(env('APP_HOSTING', 'remote')=='local' && env('APP_ENV', 'production')!='production' && $userProfile->partyId->value=="0183415"){
        //     $userProfile->customerRole='full';
        //     $userProfile->customerType=CustomerType::CORPORATE;
        // }
        // if($userProfile->userName=="600000MQ" && $userProfile->customerType==CustomerType::CORPORATE && isset($userProfile->customerRole) && in_array($userProfile->customerRole,['full'])){
        //     $userProfile->customerRole='checker';
        // }

        if(($userProfile->userName=="600000MJ"||$userProfile->userName=="600000MQ") && $userProfile->customerType==CustomerType::CORPORATE && isset($userProfile->customerRole) && in_array($userProfile->customerRole,['full'])){
            $userProfile->customerType=CustomerType::BUSINESS;
        }

        $agentUser=AgentUser::select("id","account_id","agent_code","branch_code")
        ->where('party_id',$userProfile->partyId->value)
        ->first();

        if(!is_null($agentUser)){
            $userProfile->agent=$agentUser;
            //$userProfile->customerType=CustomerType::AGENT;
            if(isset($userProfile->customerRole) && in_array($userProfile->customerRole,[/*'full',*/'basic'])){
                $userProfile->customerType=CustomerType::AGENT;
            }
        }

        if(is_null($partyVerify)){
            $partyVerify=PartyVerify::withoutGlobalScope(CustomerScope::class)
            ->withoutGlobalScope(UsernameScope::class)
            ->where('party_id',$userProfile->partyId->value??$userProfile->phoneNumber->value??null)
            ->where('device_key',$request->header("deviceKey"))
            ->where("status",TransactionStatusEnum::COMPLETED->value);
            //->first();

            $corporateRoles = CustomerType::getCorporateRoles();
            if (isset($userProfile->customerRole) && in_array($userProfile->customerRole, is_array($corporateRoles) ? $corporateRoles : [])) {
                $partyVerify = $partyVerify->where('username', $userProfile->userName);
            }
            // if(isset($userProfile->customerRole) && in_array($userProfile->customerRole,CustomerType::getCorporateRoles())){
            //     $partyVerify=$partyVerify->where('username',$userProfile->userName);
            // }
            $partyVerify= $partyVerify->first();
        }

        if(!is_null($partyVerify)){
            if(is_null($partyVerify->customer_type_id)){
                $partyVerify->customer_type_id=$userProfile->customerType;
                $partyVerify->save();
            }
            if($partyVerify->customer_type_id==CustomerType::BUSINESS && isset($userProfile->customerRole) && in_array($userProfile->customerRole,[/*'full',*/'basic'])){
                $userProfile->customerType=$partyVerify->customer_type_id;
            }
        }

        $result->userProfile=$userProfile;

        // $request->setUserResolver(function () use ($result) {
        //     return $result;
        // });

        // Inject cookies to request
        $cookies=collect($response->cookies()->toArray())
        ->flatMap(function ($values) {
            Cookie::queue($values["Name"], $values["Value"]);
            return array_map(function ($value){
                return $value;
            }, [$values["Name"]=>$values["Value"]]);
        });
        $request->cookies->add($cookies->toArray());

        // Set lang config
        if ($request->hasHeader("lang")){
            App::setLocale($request->header("lang"));
        }

        return $result;
    }
    public static function party($request)
    {
        $service=new CustomerService($request);
        $response=$service->getHttpRequest('GET',"me/party");
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->party)){
            return $result->party;
        }
        return GeneralResponseData::from($result);
    }
    /**
     * Get user identety card info
     *
     * @param  \Illuminate\Http\Request  $request
     * @return GeneralResponseData
     */
    public static function cardInfo($request,$requiredValidData=true)
    {
        $service=new CustomerService($request);
        return $service->_cardInfo($request,$requiredValidData);
    }
    /**
     * Get user identety card info
     *
     * @param  \Illuminate\Http\Request  $request
     * @return GeneralResponseData
     */
    protected function _cardInfo($request,$requiredValidData=true)
    {
        $response=$this->getHttpRequest('GET','CardInquiry');
        if(!is_string($response)){
            $cardInfo=$response->object();
        }else{
            $cardInfo = new \stdClass();
        }

        if($requiredValidData && !isset($cardInfo->idNumber)){
            return GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[CardInquiry]",
                    "message"   => [
                        "title"   => __("Your information is not complete, please visit the nearest branch to update it!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_CUSTOMER_CARD_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ])->additional([
                "cardInfo"=> $cardInfo,
            ]);
        }

        return GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "cardInfo"=> $cardInfo,
        ]);
    }

    /**
     * Get user details info
     *
     * @param  \Illuminate\Http\Request  $request
     * @return $user
     */
    public static function details($request)
    {
        $user=$request->user();
        $userProfile=$user->userProfile;

        $service=new CustomerService($request);
        $result=$service->_cardInfo($request);
        if($result->status->message->code=="0"){
            $cardInfo=$result->getAdditionalData()["cardInfo"];
            $cardInfo->firstName=null;
            $cardInfo->lastName=null;
            $userProfile->cardInfo=$cardInfo;
        }

        $object=new \stdClass();
        $object->account_id=  $userProfile->partyId->value;
        $result=FlexService::customerEnInfo($object);
        if($result->status->message->code=="0" && isset($userProfile->cardInfo)){
            $enInfo=$result->getAdditionalData()["enInfo"];
            $cardInfo=$userProfile->cardInfo;
            $cardInfo->firstName=$enInfo->firstname??null;
            $cardInfo->lastName=$enInfo->lastname??null;
            $cardInfo->email=$enInfo->email_address??$userProfile->emailId->value;

            $userProfile->cardInfo=$cardInfo;

            $partyVerify=PartyVerify::where('party_id',$user->id)
            ->where('device_key',$request->header("deviceKey"))
            ->first();
            if(/*$user->id=="0183415" &&*/ isset($enInfo->cust_categ) && !is_null($partyVerify)){
                if($partyVerify->customer_type_id!=(CustomerType::mapUserCategory[$enInfo->cust_categ]??$partyVerify->customer_type_id)){
                    if(isset($userProfile->customerRole) && in_array($userProfile->customerRole,[/*'full',*/'basic'])){
                        $partyVerify->customer_type_id=CustomerType::mapUserCategory[$enInfo->cust_categ];
                        $userProfile->customerType=$partyVerify->customer_type_id;
                        $user->customerType=$partyVerify->customer_type_id;
                        $partyVerify->save();
                    }

                }
            }

            $user->userProfile=$userProfile;
        }
        return $user;
        /*$user=$request->user();
        $userProfile=$user->userProfile;

        $cardInfo= \Cache::tags(['cardInfo'])->remember("card.en.{$user->id}",86400, function () use ($user,$userProfile,$request){
            $service=new CustomerService($request);
            $result=$service->_cardInfo($request);
            if($result->status->message->code=="0"){
                $cardInfo=$result->getAdditionalData()["cardInfo"];
                $cardInfo->firstName=null;
                $cardInfo->lastName=null;
               // $customerInfo->cardInfo=$cardInfo;
            }

            $object=new \stdClass();
            $object->account_id=  $userProfile->partyId->value;
            $result=FlexService::customerEnInfo($object);
            if($result->status->message->code=="0" && isset($cardInfo)){
                $enInfo=$result->getAdditionalData()["enInfo"];
                //$cardInfo=$userProfile->cardInfo;
                $cardInfo->firstName=$enInfo->firstname??null;
                $cardInfo->lastName=$enInfo->lastname??null;
                $cardInfo->email=$enInfo->email_address??$userProfile->emailId->value;
                return $cardInfo;
            }
            return null;
        });
        if(!is_null($cardInfo)){
            $userProfile->cardInfo=$cardInfo;
            $user->userProfile=$userProfile;
        }
        return $user;*/
    }

    /**
     * Get user details info
     *
     * @param  \Illuminate\Http\Request  $request
     * @return $accounts|$result
     */
    public static function accounts($request)
    {
        $service=new CustomerService($request);

        $response=$service->getHttpRequest('GET','accounts/demandDeposit?status=ACTIVE&status=CLOSED&status=DORMANT');
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->accounts)){
            $accounts=AccountData::collect($result->accounts,DataCollection::class);
           // $helper=app(BaseHelper::class);
           // $helper->setAccounts($accounts);
            $accounts->each(function(&$account) use($accounts,$request){
                $account->openCurrencies=$account->allowedCurrenciesToOpen($accounts,$request);
                $account->excludeCurrencies=[\App\Enums\CurrencyTypeEnum::G21->value,\App\Enums\CurrencyTypeEnum::G24->value];
                if(in_array($account->currencyId,$account->openCurrencies)){
                    $account->excludeCurrencies[]=$account->currencyId;
                }
            });
            return $accounts;
        }
        return GeneralResponseData::from($result);
    }
    public static function account($request,$accountId):GeneralResponseData|AccountData
    {

        $service=new CustomerService($request);
        $response=$service->getHttpRequest('GET',"accounts/demandDeposit/$accountId");
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->demandDepositAccountDTO)){
            $account=AccountData::from($result->demandDepositAccountDTO);
            return $account;
        }
        return GeneralResponseData::from($result);
    }
    public static function history($request,$accountId,$count=6)
    {
        $service=new CustomerService($request);
        $response=$service->getHttpRequest('GET',"accounts/demandDeposit/$accountId/transactions",[
            "searchBy"=>"LNT",
            "noOfTransactions"=>$count
        ]);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->items)){
            return $result->items;
        }
        return GeneralResponseData::from($result);
    }

    public static function payees($request,$types="PEERTOPEER",$groupId=null){
        $service=new CustomerService($request);
        $response=$service->getHttpRequest('GET',"payments/payeeGroup",[
            "expand"=>"ALL",
            "types"=>$types
        ]);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->payeeGroups)){
            if(!is_null($groupId)){
                return collect($result->payeeGroups)->where('groupId',$groupId)->first();
            }
            return collect($result->payeeGroups);
        }
        return GeneralResponseData::from($result);
    }
    public static function billers($request,$billerIds=[]){
        $service=new CustomerService($request);
        $response=$service->getHttpRequest('GET',"payments/registeredBillers",[]);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->partyBillerDetails)){
            if(!empty($billerIds)){
                return collect($result->partyBillerDetails)->collapse()->whereIn('billerId',$billerIds)->toArray();
            }
            return collect($result->partyBillerDetails)->collapse();
        }
        return GeneralResponseData::from($result);
    }
    public static function billerRemove($request,$billerId,$number){
        $service=new CustomerService($request);
        $response=$service->getHttpRequest('DELETE',"payments/registeredBillers/$billerId/relations/$number",[]);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        return GeneralResponseData::from($result);
    }
    public static function transaction($request,$accountId,$referenceNo)
    {
        $service=new CustomerService($request);
        $params =  collect($request->all()+['referenceNo'=>$referenceNo])->except(['account', 'x-encoded-content-encoding','Vary','Location']);

        $response=$service->getHttpRequest('GET',"accounts/demandDeposit/$accountId/transactions",$params->toArray());
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) &&  $result->status->message->code=="0" && isset($result->items)){
            return collect($result->items)->where('key.transactionReferenceNumber',$referenceNo)->first();
        }
        return GeneralResponseData::from($result);
    }

     /**
     * Get user assigned limits packages info
     *
     * @param  ?LimitTypeEnum  $limitType
     * @return ?GeneralResponseData|?\Illuminate\Support\Collection|object
     */
    public static function assignedLimits(?LimitTypeEnum $limitType)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"me/assignedLimitPackage",[]);
        if(is_string($response)){
            return abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->limitPackageDTO->targetLimitLinkages)){
            if(!is_null($limitType)){
                return collect($result->limitPackageDTO->targetLimitLinkages)
                ->where('target.value',$limitType->value)
                ->first();
            }
            return collect($result->limitPackageDTO->targetLimitLinkages);
        }
        return abort(response()->json(GeneralResponseData::from($result)));
    }

     /**
     * Get user assigned limits packages info
     *
     * @param  string  $period
     * @param  ?LimitTypeEnum  $limitType
     * @return ?GeneralResponseData|?\Illuminate\Support\Collection
     */
    public static function limitUtilization(string $period,?LimitTypeEnum $limitType)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"financialLimitUtilization",[
            "limitType"=>"$period"
        ]);
        if(is_string($response)){
            return null;
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->limitUtilizationDTOs)){
            if(!is_null($limitType)){
                return collect($result->limitUtilizationDTOs)
                ->where('utilizationId',$limitType->value)
                ->first();
            }
            return collect($result->limitUtilizationDTOs);
        }
        return null;//abort(response()->json(GeneralResponseData::from($result)));
    }


    /**
     * Get user alerts messages count
     *
     * @return ?object
     */
    public static function mailboxCount()
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"mailbox/count",[
            "msgFlag"=>"T"
        ]);
        if(is_string($response)){
            return null;
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->summary)){

            $items=collect($result->summary->items);
            $items->pop();

            $result->summary->items=$items;
            $result->summary->total=$result->summary->items
            ->sum('unReadCount');
            return $result->summary;
        }
        return null;//abort(response()->json(GeneralResponseData::from($result)));
    }

    public static function internalTranseferWithFees($request)
    {
        $object = new \stdClass();
        $object->journals= [
            "genericPayee" =>[
                "nickName" =>'',
                "accountName" =>'temp',
                "accountNumber" => $request->service_name,
                "transferMode"=> 'ACC'
            ],
            "genericPayout" =>[
                "amount" =>$request->amount,
                "purpose" =>'FAML',
                "purposeText" => null,
                "debitAccountId" => $request->account_id,
                "remarks"=> $request->remarks
            ],
            "paymentType" => 'INTERNALFT'
        ];

        $result=static::internalTransefer($object);
        if ($result->status->message->code == "0") {
            $amount =new PaymentResultData(
                $result->getAdditionalData()["referenceId"],
                $result->getAdditionalData()["externalReferenceId"],
                InvoiceTransactionsTypeEnum::Payment->value
            );
            if($request->fee->amount>0){
                //Fee Payment
                $object = new \stdClass();
                $object->journals= [
                    "genericPayee" =>[
                        "nickName" =>'',
                        "accountName" =>'temp',
                        "accountNumber" => $request->fee_service_name,
                        "transferMode"=> 'ACC'
                    ],
                    "genericPayout" =>[
                        "amount" =>$request->fee,
                        "purpose" =>'FAML',
                        "purposeText" => null,
                        "debitAccountId" => $request->account_id,
                        "remarks"=> $request->remarksFee
                    ],
                    "paymentType" => 'INTERNALFT'
                ];

                $result=static::internalTransefer($object);
                if ($result->status->message->code == "0") {
                    $fee =new PaymentResultData(
                        $result->getAdditionalData()["referenceId"],
                        $result->getAdditionalData()["externalReferenceId"],
                        InvoiceTransactionsTypeEnum::Payment->value
                    );

                    return GeneralResponseData::from(array(
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "",
                            "message"   => [
                                "title"   => "Successfully recharge customer account",
                                "detail"  => "Successfully recharge customer account",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ))->additional([
                        'payment'=>[
                            'amount'=>$amount,
                            'fee'   =>$fee,
                        ]
                    ]);
                }else{
                    $object=new \stdClass();
                    $object->reference_id   = $amount->externalReferenceId;
                    $object->service_name   = $request->service_name;
                    $object->account_id     = $request->account_id;

                    $reverseResult=FlexService::reverseToAccount($object);
                    if($reverseResult->status->message->code=="0"){
                        $amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                    }
                    $result->additional([
                        'payment'=>[
                            'amount'=>$amount,
                        ]
                    ]);
                }
            }else{
                return GeneralResponseData::from(array(
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "",
                        "message"   => [
                            "title"   => "Successfully recharge customer account",
                            "detail"  => "Successfully recharge customer account",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ))->additional([
                    'payment'=>[
                        'amount'=>$amount
                    ]
                ]);
            }

        }
        return $result;
    }
    public static function internalTransefer($request)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('POST',"payments/generic",$request->journals);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->paymentId)){
            $referenceId=$result->paymentId;
            $response=$service->getHttpRequest('PATCH',"payments/generic/{$result->paymentId}?paymentType=INTERNALFT",[]);

            if(is_string($response)){
                return GeneralResponseData::from(static::defaultErrorResult);
            }

            $result=$response->object();
            if(isset($result->status->message->code)){
                if($result->status->message->code=="0"){
                    return GeneralResponseData::from($result)
                    ->additional([
                        "referenceId"=>$referenceId,
                        "externalReferenceId"=>$result->externalReferenceId
                    ]);
                }
            }

        }
        return GeneralResponseData::from($result);//abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function getInternalTransefer($id)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"payments/generic/$id");
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }
        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function patchInternalTransefer($id,$paymentType)
    {
        $service=new CustomerService(request()->instance());
        if(request()->hasHeader("X-CHALLENGE-RESPONSE")){
            $service->headers+=["X-CHALLENGE_RESPONSE"=>request()->header("X-CHALLENGE-RESPONSE")];
        }
        $response=$service->getHttpRequest('PATCH',"payments/generic/{$id}?paymentType=$paymentType",[],[],60);
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code)){
            if($result->status->message->code=="0"){
                $result->flag=TransactionStatusEnum::COMPLETED->value;
                return $result;
            }else if($result->status->message->code=="DIGX_APPROVAL_REQUIRED"){
                $result->flag=TransactionStatusEnum::PENDING->value;
                $result->externalReferenceId=$id;
                return $result;
            }
        }
        abort(response()
        ->json(GeneralResponseData::from($result),$response->status(),collect($response->getHeaders())
            ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location','Set-Cookie','set-cookie'])
            ->toArray()
        ));
    }

    public static function getSelfTransefer($id)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"payments/transfers/self/$id");
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }
        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function patchSelfTransefer($id,$paymentType)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('PATCH',"payments/transfers/self/{$id}?paymentType=$paymentType",[],[],60);
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code)){
            if($result->status->message->code=="0"){
                $result->flag=TransactionStatusEnum::COMPLETED->value;
                return $result;
            }else if($result->status->message->code=="DIGX_APPROVAL_REQUIRED"){
                $result->flag=TransactionStatusEnum::PENDING->value;
                $result->externalReferenceId=$id;
                return $result;
            }
        }
        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function postExternalTransefer($data)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('POST',"payments/transfers/external",$data,[],60);
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }
        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function getExternalTransefer($id)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"payments/transfers/external/$id");
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }
        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function patchExternalTransefer($id,$data)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('PATCH',"payments/transfers/external/{$id}",$data,[],60);
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }
        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function yeahMoneyTransefer()
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('POST',"yeahMoney/postpayin",request()->all(),[],60);
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->unique_Tracking_Code) && isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }else if(isset($result->message->code)) {
            $result=GeneralResponseData::from(array(
                'status'=>StatusData::from(collect($result)->toArray())
            ))->additional([
                "result_Code"=>$result->message->code,
                "result_Desc"=>$result->message->title??$result->message->detail,
            ]);
            if(in_array($result->status->message->code,["DIGX_CZ_1126","DIGX_FL_015"])){
                return abort(response()->json($result));
            }
        }
        abort(response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED));
    }

    public static function currentDate()
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"payments/currentDate");

        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->currentDate->valueDate)){
            return date_format(date_create($result->currentDate->valueDate), "Y-m-d");
        }

        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function billPayment($request)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('POST',"payments/transfers/bill",$request);

        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }

        abort(response()->json(GeneralResponseData::from($result)));
    }
    public static function patchBillPayment($id,$subscriberType)
    {
        $service=new CustomerService(request()->instance());
        if(request()->hasHeader("X-CHALLENGE-RESPONSE")){
            $service->headers+=["X-CHALLENGE_RESPONSE"=>request()->header("X-CHALLENGE-RESPONSE")];
        }
        if(is_null($subscriberType)){
            $response=$service->getHttpRequest('PATCH',"payments/transfers/bill/{$id}",[],[],60);
        }else{
            $response=$service->getHttpRequest('PATCH',"utilitypay/{$id}/$subscriberType",[],[],60);
        }
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code)){
            if($result->status->message->code=="0"){
                $result->flag=TransactionStatusEnum::COMPLETED->value;
                return $result;
            }else if($result->status->message->code=="DIGX_APPROVAL_REQUIRED"){
                $result->flag=TransactionStatusEnum::PENDING->value;
                $result->externalReferenceId=$id;
                return $result;
            }
        }
        abort(response()
        ->json(GeneralResponseData::from($result),$response->status(),collect($response->getHeaders())
            ->except(['Transfer-Encoding', 'x-encoded-content-encoding','Vary','Location','Set-Cookie','set-cookie'])
            ->toArray()
        ));
    }
    /**
     * Get user customer transfer account info
     *
     * @param  \Illuminate\Http\Request  $request
     * @return $accounts|$result
     */
    public static function customerTransfer($request)
    {
        $service=new CustomerService($request);
        $response=$service->getHttpRequest('GET','customerTransfer',[
            "customerNo"=>$request->customerNo
        ]);
        if(is_string($response)){
            return GeneralResponseData::from(static::defaultErrorResult);
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->accounts)){
            $accounts=AccountData::collect($result->accounts,DataCollection::class);
            $result->accounts=$accounts;
            return $result;
        }
        return GeneralResponseData::from($result);
    }

    public static function getMerchantInfo($code)
    {
        $service=new CustomerService(request()->instance());

        $response=$service->getHttpRequest('GET',"payments/merchants/$code");
        if(is_string($response)){
            return  abort(response()->json(GeneralResponseData::from(static::defaultErrorResult)));
        }
        $result=$response->object();
        if(isset($result->status->message->code) && $result->status->message->code=="0"){
            return $result;
        }
        abort(response()->json(GeneralResponseData::from($result)));
    }
}
