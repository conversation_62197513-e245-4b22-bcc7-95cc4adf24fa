<?php

namespace App\Data\Form;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;

use Illuminate\Contracts\Pagination\CursorPaginator as CursorPaginatorContract;
use Illuminate\Contracts\Pagination\Paginator as PaginatorContract;
use Illuminate\Pagination\AbstractCursorPaginator;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Enumerable;
use Illuminate\Support\LazyCollection;
use Spatie\LaravelData\CursorPaginatedDataCollection;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\PaginatedDataCollection;


class FormBaseData extends BaseNonNullableData
{
    public function __construct(
        #[MapOutputName('et'),MapInputName('et')]
        public ?string $element=null
    ) {
    }
    // public static function from(mixed ...$payloads) : static
    // {
    //     $data=$payloads[0];
    //     switch ($data['element']??$data['et']) {
    //         case 'header':
    //             return FormHeaderData::from($data);
    //         default:
    //             return FormFieldData::from($data);

    //     }
    // }
    public static function collect(mixed $items, ?string $into = null): array|DataCollection|PaginatedDataCollection|CursorPaginatedDataCollection|Enumerable|AbstractPaginator|PaginatorContract|AbstractCursorPaginator|CursorPaginatorContract|LazyCollection|Collection
    {
        if(!empty($items)){
            if(collect($items)->first() instanceof FormBaseData){
                 return static::factory()->collect($items, $into);
            }
            $result=[];
            foreach ($items as $item) {
                switch (data_get($item,"et")) {
                    case 'header':
                        $result[]=FormHeaderData::from($item);
                        break;
                    default:
                        $result[]=FormFieldData::from($item);
                        break;
                }
            }
            return static::factory()->collect($result, $into);
        }

        return static::factory()->collect($items, $into);
    }
        // public static function prepareForPipeline(array $properties) : array
    // {
    //     $properties['debitAccountId']= data_get($properties,"payload.debitAccountId");
    //     $properties['amount']= data_get($properties,"payload.amount");
    //     $properties['fee']=data_get($properties,"payload.fee");
    //     $properties['receiverNumber']=data_get($properties,"payload.receiverNumber",);
    //     $properties['receiverName']=data_get($properties,"payload.receiverName");
    //     $properties['bankCode']=data_get($properties,key: "payload.bankCode");
    //     $properties['remarks']=data_get($properties,key: "payload.remarks");

    //     return $properties;
    // }
}
