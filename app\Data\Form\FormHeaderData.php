<?php

namespace App\Data\Form;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\NameData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\DataCollection;


class FormHeaderData extends FormBaseData
{
    public function __construct(
        #[MapOutputName('ne'),MapInputName('ne')]
        public ?NameData $name=null,
        #[MapOutputName('et'),MapInputName('et')]
        public ?string $element = null,
        #[MapOutputName('bgc'),MapInputName('bgc')]
        public ?string $backgroundColor=null,
        #[MapOutputName('fgc'),MapInputName('fgc')]
        public ?string $foregroundColor=null
    ) {
        parent::__construct($element); // Call parent constructor
    }
}
