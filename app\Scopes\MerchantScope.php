<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class MerchantScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return $builder
     */
    public function apply(Builder $builder, Model $model)
    {
        $user=auth()->user();
        if($user?->hasRole('تاجر')==true){
            $column_name="initiator_id";
            return $builder->where($model->getTable().".initiator_type",'sdk')
                ->where($model->getTable().".$column_name",$user->id);
        }
        return $builder;

    }
}
