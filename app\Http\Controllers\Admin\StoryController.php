<?php

namespace App\Http\Controllers\Admin;
use App\Data\GeneralItemData;
use App\Http\Controllers\Controller;
use App\Models\Story;
use Illuminate\Http\Request;
use Auth;
use Spatie\LaravelData\PaginatedDataCollection;

class StoryController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['story.*','story.list'])) return abort(401);

        $filter=$request->all();

        $items=Story::select('id','name','icon','status')->orderBy('created_at','desc');


        if($request->filled('status') ){
            $items=$items->where('status',$request->status);
        }

        if($request->filled('searchname'))
            $items=$items->where(function($query) use($filter){
                return $query->where('name','like','%'.$filter['searchname'].'%')
                ->orWhere('name','like','%'.$filter['searchname'].'%');
            });

        $items=$items->paginate(15);

      //  GeneralItemData::collect($items->paginate(15));//->wrap('paginated_data');
        $status=[
            "0"=>__("Unactive"),
            "1"=>__("Active")
        ];

        return view('default.admin.story.index')
        ->with('items', $items)
        ->with('status', $status)
        ->with('filter', $filter);
    }


    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function create()
    {
        if (! Auth::user()->canAny(['story.*','story.create'])) return abort(401);

        if(request()->ajax()){
            return response(view('default.admin.story.status-item')
            ->with('isAjax',request()->ajax())
            ->with('key',request()->key)
            ->with('item',null)/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
        }
        return view('default.admin.story.view');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['story.*','story.create'])) return abort(401);

        $this->validate($request,[
            'name' => 'required|max:500',
            'items.*.duration' => 'required',
            'items.*.url' => 'nullable',
            'status' => 'required|in:0,1',
        ]);
        return $this->save($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Story  $story
     * @return \Illuminate\Contracts\View\View
     */
    public function show(Story $story){
        if (! Auth::user()->canAny(['story.*','story.view','story.edit'])) return abort(401);

        return view('default.admin.story.view')
        ->with('item', $story);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Story  $story
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Story $story)
    {
        if (! Auth::user()->canAny(['story.*','story.edit'])) return abort(401);
        //return response()->json($request->all());
        $this->validate($request,[
            'name' => 'required|max:500',
            'items.*.duration' => 'required',
            'items.*.url' => 'nullable',
            'status' => 'required|in:0,1',
        ]);
        return $this->save($request,$story);
    }

    protected function save(Request $request, ?Story $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new Story;
        }

        $this->setImage('icon',"stories");
        $this->setImage('items.*.image',"stories");

        $this->validate($request,[
            "icon" => 'required',
            "items.*.image" => 'required',
        ]);

        // dd($request->all());
        // return;
        $item->icon = $request->icon;

        $item->name = $request->name;
        $item->status = $request->status;
        $item->items = $request->items;

        $item->save();


        if(isset($isNew)){
            return redirect("/admin/story/$item->id")
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['story.*','story.delete'])) return abort(401);

    }

}
