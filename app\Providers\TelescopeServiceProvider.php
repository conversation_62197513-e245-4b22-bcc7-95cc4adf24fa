<?php

namespace App\Providers;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Gate;
use <PERSON><PERSON>\Telescope\EntryType;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Telescope::night();

        $this->hideSensitiveRequestDetails();
        Telescope::avatar(function ($id,$email) {
            return "/storage/account-placeholder.jpg";
        });
        Telescope::tag(function (IncomingEntry $entry) {
            $tags=[];
            if($entry->isRequest()){
                $tags[]=$entry->content["method"]??"";
                $tags[]=$entry->content["uri"]??"";
                if($entry->isFailedRequest()){
                    $tags[]='error';
                }
            }else if($entry->isClientRequest()){
                if(($entry->content['duration']?? 0)>5000){
                    $tags[]='slow';
                }else if(($entry->content['response_status'] ?? 1000)>=500){
                    $tags[]='error';
                }
            }else if($entry->isSlowQuery()){
                if(!str_starts_with(($entry->content['sql']??""),'insert into')){
                    $tags[]='high';
                }
            }
            return $tags;
        });
        Telescope::filterBatch(function (Collection $entries) {
            //return true;
            if (env('APP_ENV')=='local') {
                return true;
            }

            return $entries->contains(function (IncomingEntry $entry) {
                //$result= false;
                // if(!is_null($entry->content) && ($entry->content['class']??'')==\GuzzleHttp\Exception\ConnectException::class){

                // }
                return
                    (
                        $entry->isRequest() && (
                            ($entry->content['duration'] ?? 0)>12000 ||
                            ($entry->content['method'] ?? 'GET')!='GET' //||
                            //str_starts_with(($entry->content['uri']??""),'/digx/v1/switch/home')
                            //($entry->content['uri']??"")=="/digx/v1/switch/home"
                        )
                    )||
                   (
                       $entry->isClientRequest() && (
                           ($entry->content['duration'] ?? 0)>5000 ||
                           (
                               ($entry->content['response_status'] ?? 0)>=300 &&
                               ($entry->content['response_status'] ?? 1000)!=401 &&
                                    !str_starts_with(($entry->content['uri']??""),'https://cardvip')
                            )
                       )
                   )||
                    $entry->isReportableException() ||
                    $entry->isFailedRequest() ||
                    $entry->isFailedJob() ||
                    $entry->isScheduledTask() ||
                    $entry->isSlowQuery() ||
                    $entry->hasMonitoredTag()||
                    $entry->type==EntryType::COMMAND;
                }
            );
        });
    }

    /**
     * Prevent sensitive request details from being logged by Telescope.
     *
     * @return void
     */
    protected function hideSensitiveRequestDetails()
    {
        if ($this->app->environment('local')) {
            return;
        }

        Telescope::hideRequestParameters([
            '_token',
            'USERNAME',
            'PASSWORD',
            'SENDER_OTP',
            'RECEIVER_MOBILE',
            'j_password',
            'password',
            'newPassword',
            'oldPassword',
            'changedPassword',
            // auth2
            'username',
            'Password',
            'client_id',
            'client_secret',
        ]);

        Telescope::hideRequestHeaders([
            'token',
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
            'jwtoken',
            'jwt',
            'set-cookie',
            // auth2
            'trx-token',
            'Authorization'
        ]);
        Telescope::hideResponseParameters([
            //token response
            'access_token',
            'refresh_token',
            //cardless
            'TransRefNum',
            //user
            'email',
            'phone',
            //me
            'userProfile.emailId',
            'userProfile.phoneNumber',

            //jwt
            'jwtoken',

            //get_custinfo
            'email_address',
            'dateofbirth',
            'cardInfo.email',
            //CardInquiry
            'mobileNumber',
            'idNumber',
            //CardInquiry->home
            'userProfile.cardInfo.mobileNumber',
            'userProfile.cardInfo.idNumber',
            'userProfile.cardInfo.email',

            'access_token',

            //cards
            'data.deviceNumber',
            'data.*.cvv',
            'data.*.registeredEmailId',
            'data.*.msisdn',
            'data.*.expiryDate',
            //cards->home
            'virtualCards.*.cardNumber',
            'virtualCards.*.cvv',
            'virtualCards.*.registeredEmailId',
            'virtualCards.*.msisdn',
            'virtualCards.*.expiryDate',

            //credit_card
            'credit_card_details.primary_card_no',
            'credit_card_details.card_no',
            'credit_card_details.card_renewal_dt',
            //cards->home
            'creditCards.*.cardNumber',
            'creditCards.*.expiryDate',

        ]);

    }
    /**
     * Register the Telescope gate.
     *
     * This gate determines who can access Telescope in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewTelescope', function ($user) {
            return $user->can('telescope.*');

            // return in_array($user->email, [
            //     '<EMAIL>'
            // ]);
        });
    }
}
