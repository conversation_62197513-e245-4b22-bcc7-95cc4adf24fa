<?php

namespace App\Data\Agent;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use App\Data\NameData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class AgentGeneralItemData extends Data
{
    #[MapInputName('Code')]
    public ?string $id;

    public ?NameData $name;
    public static function prepareForPipeline(array $properties) : array
    {
       $properties['name']= [
          "ar"=>data_get($properties, "Name"),
          "en"=>data_get($properties, "Name"),
       ];
       return $properties;
    }

}
