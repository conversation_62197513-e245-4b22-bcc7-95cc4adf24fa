<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DB;
class Dual extends Model
{
    use HasFactory;
    protected $table = 'dual';

    public static function dual($fromDate,$toDate)
    {
        $_name=static::class;
        // select sysdate+ (1/24)*level as created_at
        // from dual
        // connect by sysdate+ (1/24)*level <=  TO_DATE('$toDate','YYYY-MM-DD HH24:MI:SS')
        // union
        return DB::query()->fromSub("
                select TO_DATE('$toDate','YYYY-MM-DD HH24:MI:SS')- (1/24)*level as created_at
                from dual
                connect by TO_DATE('$toDate','YYYY-MM-DD HH24:MI:SS')- (1/24)*level >= TO_DATE('$fromDate','YYYY-MM-DD HH24:MI:SS')
        ","dual");
    }

    public static function dualCharts($indentifier,$fromDate,$toDate)
    {
        return DB::query()
        ->fromSub(
            static::dual(
                $fromDate,
                $toDate
            )
            ->select(DB::raw("$indentifier as indentifier"),DB::raw("MIN(created_at) as created_at"))
            ->groupBy(DB::raw("$indentifier"))
        ,"dual")
        ->select("indentifier as name",DB::raw("coalesce(val,0) as val"))
        ->orderBy("created_at")
        ;
    }

    
    public static function dualBalanceCharts($indentifier,$fromDate,$toDate)
    {
        return DB::query()
        ->fromSub(
            static::dual(
                $fromDate,
                $toDate
            )
            ->select(DB::raw("$indentifier as indentifier"),DB::raw("MIN(created_at) as created_at"))
            ->groupBy(DB::raw("$indentifier"))
        ,"dual")
        ->select(
            DB::raw("MIN(to_number(created_at - to_date('01-JAN-1970','DD-MON-YYYY')) * (24 * 60 * 60 * 1000)) as name"),
            DB::raw("SUM(SUM(val)) OVER(order by MIN(created_at)) as val"),
        )
        ->groupBy("indentifier")
        ->orderBy(DB::raw("MIN(created_at)"));
    }

}
