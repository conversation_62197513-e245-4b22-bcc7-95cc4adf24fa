<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gift extends Model
{
    use HasFactory;
    protected $fillable = ["status","party_id","party_name","group","debit_account_id","type",'process_type','amount','remarks','payees','exchange_rate','at_date'];
    protected $casts = [
        'status' => 'int',
        'debit_account_id' => 'object',
        'amount' => 'object',
        'group' => 'object',
        'payees' => 'array',
        'exchange_rate'     => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        //'transactions' => 'array'

    ];
    protected $hidden = [
        'rn'
    ];

    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            $query->party_id = auth()->user()->id;
        });
    }

    public function transactions()
    {
        return $this->hasMany('App\Models\GiftTransaction', "gift_id");
    }
    public function request()
    {
        return $this->hasOne('App\Models\GiftRequest','gift_id')->withoutGlobalScope(CustomerScope::class);
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}
