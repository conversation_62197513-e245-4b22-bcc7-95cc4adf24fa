<?php

namespace App\Enums;
enum BillPaymentServiceCodeEnum:string {
    case sabafone="42101";
    case you="42102";
    case yemenMobile="42103";
    case y="42104";
    case adsl="42105";
    case landPhone="42106";

    case waterUtility="42107";
    case electricity="42108";
    case electricityCommercial="1";
    case teleyemen="42109";
    case adenNet="42112";
    case yemen4G="42113";

    case wallet="50000";
    case games="50001";
    case internetCards="50002";
    case government="50005";
    case education="50006";

    // case internetBankCards="80001";
    // case mastercardVirtualCards="80002";
    // case visaVirtualCards="80003";

    public static function findByValue(string $value):BillPaymentServiceCodeEnum|null {
        return collect(static::cases())->filter(function($item) use($value){
            return $item->value==$value;
        })->first();
    }
    public static function values():array {
        // $map=[

        // ];
        return collect(static::cases())->pluck("value")->toArray();
    }
}
