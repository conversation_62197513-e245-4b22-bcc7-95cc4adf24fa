<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;
//use Gecche\Multidomain\Foundation\Http\Kernel as HttpKernel;
class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [

        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\TrustProxies::class,
        \App\Http\Middleware\AddContentLength::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\RedirectToNonWwwMiddleware::class,
        ],

        'api' => [
            'throttle:60,1',
            'bindings',
            \App\Http\Middleware\FakeDataMiddleare::class,
        ],
        'digx' => [
            //'throttle:60,1',
            'bindings',
            //"cors",
            \App\Http\Middleware\CheckForMaintenanceMode::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \App\Http\Middleware\DecodeQueuedCookies::class
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'cors' => \App\Http\Middleware\Cors::class,
        //'password-grant' => \App\Http\Middleware\InjectPasswordGrantCredentials::class,
        'client' => \Laravel\Passport\Http\Middleware\CheckClientCredentials::class,
        'language' => \App\Http\Middleware\LanguageSwitcher::class,
        //'digx' => \App\Http\Middleware\OBDXMiddleware::class
        'appversion' => \App\Http\Middleware\AppVersionMiddleware::class,
        'fake' => \App\Http\Middleware\FakeDataMiddleare::class,
        'digx.obdx' => \App\Http\Middleware\OBDXMiddleware::class,
        'digx.agent' => \App\Http\Middleware\AgentMiddleware::class,
        'digx.login' => \App\Http\Middleware\EncryptDigxCookies::class,
        'wepapp' => \App\Http\Middleware\WebApp::class,
        'session' => \Illuminate\Session\Middleware\StartSession::class,
        'admin' => \App\Http\Middleware\AdminMiddleware::class
    ];

    /**
     * The priority-sorted list of middleware.
     *
     * This forces non-global middleware to always be in the given order.
     *
     * @var array
     */
    protected $middlewarePriority = [
        \App\Http\Middleware\DecodeQueuedCookies::class,
        \App\Http\Middleware\EncryptDigxCookies::class,
        \Illuminate\Session\Middleware\StartSession::class,
        \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        \App\Http\Middleware\Authenticate::class,
        \Illuminate\Session\Middleware\AuthenticateSession::class,
        \Illuminate\Auth\Middleware\Authorize::class,

        \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
        \App\Http\Middleware\FakeDataMiddleare::class,
        \App\Http\Middleware\OBDXMiddleware::class,
        \App\Http\Middleware\AppVersionMiddleware::class,
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
    ];
}
