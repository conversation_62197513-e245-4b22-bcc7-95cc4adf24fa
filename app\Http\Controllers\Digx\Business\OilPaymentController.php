<?php

namespace App\Http\Controllers\Digx\Business;
use App\Data\AccountConfigData;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\ReceiptData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\LimitTypeEnum;
use App\Enums\OilTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Enums\TransactionStatusEnum;
use App\Helpers\JsonCamel\JsonCamelHelperFacade;
use App\LogItem;
use App\Models\CustomerType;
use App\Models\OilPayment;
use App\Models\OilRegion;
use App\Services\FlexService;
use App\Services\OBDX\CustomerService;
use App\Http\Controllers\Controller;
use App\Services\OBDX\UtilsService;
use App\Traits\AuthorizesServices;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class OilPaymentController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::OIL_PAYMENT
        ];
    }
    // public function getAuthorizedCustomerTypes(): array
    // {
    //     return [
    //         CustomerType::CORPORATE
    //     ];
    // }

    /**
     * Display a listing of the resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'from' => 'required|numeric',
            'limit' => 'required|numeric|max_digits:22',
            'text' => 'nullable',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-OIL",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_OIL_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $oilPayments=OilPayment::with("region:id,name,account_id")
        ->select('id','oil_region_id','status','type','external_reference_id','customer_account_id','amount','fee','remarks','created_at as date')
        ->where('status',"!=",TransactionStatusEnum::INIT->value)
        ->skip( $request->from)
        ->take($request->limit)
        ->orderBy("created_at","DESC");

        if($request->filled('text')){
            $oilPayments=$oilPayments->whereHas('region', function ($query) use($request) {
                return $query->where('name->ar', 'like', '%' . $request->text . '%')
                ->orWhere('name->en', 'like', '%' . $request->text . '%');
            });
        }
        $oilPayments=$oilPayments->get();

        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])
        ->additional([
            "transactions"=>$oilPayments->toArray()
        ])->transform());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        $regions=OilRegion::select('id','name','account_id','types','branches')->where('status',1)->get();
        $types=OilTypeEnum::maps();
        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])
        ->additional([
            "types"=>$types,
            "regions"=>$regions,
        ])->transform());

        // return response()->json(GeneralResponseData::from([
        //     'status'=>[
        //         "result"    => "SUCCESSFUL",
        //         "contextID" => "INI-OIL",
        //         "message"   => [
        //             "title"   => "",
        //             "detail"  => "",
        //             "code"    => "0",
        //             "type"    => "INFO"
        //         ]
        //     ]
        // ])->additional([
        //     "types"=>$types,
        //     "regions"=>$regions,
        // ]));

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return ?\Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $regions=[
        //     "001"=>"محطة الستين"
        // ];
        $validator=validator()->make($request->all(),[
            "type"      => "required|in:".join(",",OilTypeEnum::values()),
            "region.id"   => "required|exists:oil_regions,id",
            'customerAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|integer",
            'amount.currency'=>"required|max:3|min:3|in:YER",
            "remarks"      => "nullable|string",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-OIL",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_OIL_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }


        $customerDebitAccountId=AccountIdData::from($request->customerAccountId);
        $customerCreditAccountId=AccountIdData::from([
            "value"=>substr_replace($customerDebitAccountId->value,CurrencyTypeEnum::YER->value,10, 3)
        ]);

        $amount=CurrencyAmountData::from($request->input("amount"));
        $fee=CurrencyAmountData::from([
            "amount"=>0,
            "currency"=>$amount->currency
        ]);

        $valid=false;
        $message="You don't have enough balance in your account!";
        $result=CustomerService::account($request, $customerDebitAccountId->value);
        if($result instanceof AccountData){
            $account=$result;
            if($account->status!="ACTIVE"){
                $message="Your account not active!";
            }else if(!$account->allowedService(AccountConfigData::oilPayment)){
                $message="This account not allowed to use this service!";
            }else if(!$account->isCash()){
                $message="Account is not cash!";
            }else if($account->currencyId!=$amount->currency){
                $message="Account currency not match!";
            }else if($account->balance->amount<$amount->amount){
                $message="You don't have enough balance in your account!";
            }else if(!(
                ($account->currencyId==CurrencyTypeEnum::YER->value && $account->isNorth()) ||
                $account->currencyId!=CurrencyTypeEnum::YER->value
            )){
                $message="You can't use this account for payment!";
            }else{
                $valid=true;
            }
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-OIL",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OIL_002",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

         // Getting exchange rate of amount to equlevent local currency
         $exchangeRate= UtilsService::exchange(
            $amount,
            $customerDebitAccountId,
            $customerCreditAccountId
        );

        // Start getting charge fee
        $object=new \stdClass();
        $object->chargeProductId='BENZ';
        $object->branchId= $account->branchId;
        $object->productId=$customerDebitAccountId->productId();
        $object->amount= $amount->amount;
        $object->currency= $amount->currency;

        $result=FlexService::chargeFee($object);
        if($result->status->message->code!="0"){
            return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }
        $chargeFee=$result->getAdditionalData()['fee'];
        $fee=$chargeFee->fee;

        // Getting exchange rate of fee to equlevent local currency
        $feeAccount=AccountIdData::from([
            "value"=>substr_replace($customerDebitAccountId->value,$fee->currency,10, 3)
        ]);
        $exchangeChargeRate= UtilsService::exchange(
            $fee,
            $customerDebitAccountId,
            $feeAccount
        );

        $creditExchangeChargeRate= UtilsService::exchange(
            $fee,
            $feeAccount,
            $customerDebitAccountId
        );

        $region=OilRegion::find($request->input("region.id"));


        // $debitAccountId= [
        //     "value"=>"101-2000071-1003-00",
        //     "displayValue"=>"CBY - Current A/C - Cash"
        // ];
        // $creditAccountId= [
        //     "value"=>$region->account_id,//"105-2000071-1003-00",
        //     "displayValue"=>$region->name
        // ];
        $oilPayment=OilPayment::create([
            "reference_id"          =>uniqid(),
            "status"                =>TransactionStatusEnum::INIT->value,
            //"type"                  =>$request->value,
            "type"                  =>$request->type,
            "oil_region_id"         =>$region->id,
            "customer_account_id"   =>$customerDebitAccountId->toArray(),
            //"debit_account_id"      =>$debitAccountId,
            //"credit_account_id"     =>$creditAccountId,
            "amount"                =>$amount->toArray(),
            "fee"                   =>$fee->toArray(),
            "exchange_rate"=>[
                "amount"            =>$exchangeRate->toArray(),
                "fee"               =>[
                    "debit"           =>$exchangeChargeRate->toArray(),
                    "credit"          =>$creditExchangeChargeRate->toArray(),
                ]
            ],
            "remarks"=>$request->remarks
        ]);
        LogItem::store($oilPayment);
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-OIL",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "paymentId"=>$oilPayment->id,
        ])->transform());


    }

    /**
     * Display the specified resource.
     *
     * This method generates a PDF document with the title "Hello World" and content "Hello World".
     *
     * @param $id
     * @return void
     */
    public function show($id)
    {
        $oilPayment=OilPayment::select('id','status','customer_account_id','amount','fee','remarks')
        //->where('party_id',auth()->user()->id)
        ->where('status',TransactionStatusEnum::INIT->value)
        ->find($id);


        if(is_null($oilPayment)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "OIL-DETAILS",
                    "message"   => [
                        "title"   => __("Transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OIL_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }
        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "OIL-DETAILS",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "transferDetails"=>$oilPayment,
        ])->transform());

    }

    public function receipt(Request $request,OilPayment $oilPayment)
    {
        $validator=validator()->make($request->all(),[
            'showAccountInfo' => 'Nullable|numeric|in:0,1',
            'showAccountNumber' => 'Nullable|numeric|in:0,1',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $oilPayment->load('region:id,name');
        $this->generateReceipt($this->getReceiptData($oilPayment));
    }
    protected function getReceiptData(OilPayment $oilPayment): ReceiptData
    {
        $beneficiary=$oilPayment->region;

        return ReceiptData::from([
            "id"=> $oilPayment->id,
            "date"=> date_format(date_create($oilPayment->created_at), "Y-m-d H:i:s"),
            "title"=> __("Payment for oil"),
            "beneficiary"=> $beneficiary->getName(),
            "statement"=> "",
            "details"=> [
                "referenceId"=> $oilPayment->reference_id,
                "debitAccountId"=> $oilPayment->customer_account_id??null,
                "remarks"=>$oilPayment->remarks,
                "amount"=>$oilPayment->amount,
                "fee"=>$oilPayment->fee,
            ]
        ]);
    }
    public static function limits($limitType){
        if(!in_array($limitType,[LimitTypeEnum::OIL_PAYMENT])){
            return;
        }

        $whareDate="updated_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="updated_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }
        $limits=[];
        $trxLimit=[];

        $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        $assignedLimits=$targetLimitLinkages
        ->filter(function ($element, $key) use($limitType){
            return $element->target->value==$limitType->value &&
             $element->target->area==(request()->input('area')??"") &&
              $element->target->currency==(request()->input('currency')??"");
        })
        ->first();

        foreach ($assignedLimits->limits as $item) {
            switch ($item->periodicity??"") {
                case 'DAILY':
                    $limitUtilization=OilPayment::select("exchange_rate->amount->limit->amount as limits")
                    ->where('status',TransactionStatusEnum::PENDING->value)
                    ->whereRaw($whareDate,[Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()])
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                case 'MONTHLY':
                    $limitUtilization=OilPayment::select("exchange_rate->amount->limit->amount as limits")
                    ->where('status',TransactionStatusEnum::PENDING->value)
                    ->whereRaw($whareDate,[Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()])
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                default:
                    if($item->limitType=="TXN" && isset($item->amountRange->minTransaction) && isset($item->amountRange->maxTransaction)){
                        $trxLimit=[
                            "minAmount"=>$item->amountRange->minTransaction,
                            "maxAmount"=>$item->amountRange->maxTransaction,
                        ];
                    }
            }
        }
        $trxLimit["limits"]=$limits;

        //return $this->verifyUser($request,$customer);
        return abort(response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional($trxLimit)));
    }
    /**
     * Update the transfer fund details.
     *
     * This method retrieves the internal transfer details using the provided ID,
     * updates the transfer details, and processes the transfer based on the payment type.
     * Currently, it handles 'INDIADOMESTICFT' payment type.
     *
     * @param \Illuminate\Http\Request $request The HTTP request instance.
     * @param  \App\Models\OilPayment  $oilPayment
     * @return \Illuminate\Http\JsonResponse The JSON response containing the result of the update operation.
     */
    public function update(Request $request, OilPayment $oilPayment)
    {

        LogItem::store($oilPayment);

        $debitAccountId=AccountIdData::from($oilPayment->customer_account_id);

        $valid=false;
        $message="You don't have enough balance in your account!";
        $result=CustomerService::account($request, $debitAccountId->value);
        if($result instanceof AccountData){
            $account=$result;
            if($account->status!="ACTIVE"){
                $message="Your account not active!";
            }else if(!$account->allowedService(AccountConfigData::oilPayment)){
                $message="This account not allowed to use this service!";
            }else if(!$account->isCash()){
                $message="Account is not cash!";
            }else if($account->balance->amount<($oilPayment->amount->amount+$oilPayment->fee->amount)){
                $message="You don't have enough balance in your account!";
            }else if(!(
                ($account->currencyId==CurrencyTypeEnum::YER->value && $account->isNorth()) ||
                $account->currencyId!=CurrencyTypeEnum::YER->value
            )){
                $message="You can't use this account for payment!";
            }else{
                $valid=true;
            }
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-OIL",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OIL_002",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }


        $whareDate="updated_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="updated_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }


        $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        $result=$targetLimitLinkages
        ->filter(function ($element, $key) use($debitAccountId){
            return $element->target->value==LimitTypeEnum::OIL_PAYMENT->value &&
             $element->target->area==($debitAccountId->isNorth()?'N':'S') &&
              $element->target->currency==$$debitAccountId->currencyId();
        })
        ->first();

        if(is_null($result)){
            $result=$targetLimitLinkages
            ->filter(function ($element, $key){
                return $element->target->value==LimitTypeEnum::OIL_PAYMENT->value &&
                 $element->target->area=='' &&
                  $element->target->currency=='';
            })
            ->first();
        }

        $trxLimit=$result->limits->filter(function ($element, $key){
            return $element->limitType=='TXN';
        })->first();
        if(!is_null($trxLimit) && ($oilPayment->exchange_rate->amount->limit->amount < $trxLimit->amountRange->minTransaction->amount || $oilPayment->exchange_rate->amount->limit->amount > $trxLimit->amountRange->maxTransaction->amount)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-WASIL",
                    "message"   => [
                        "title"   =>sprintf( __("Your transaction amount exceed the allowed limit! Min %s Max %s per transaction"),$trxLimit->amountRange->minTransaction->amount,$trxLimit->amountRange->maxTransaction->amount),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_WASIL_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $dailyLimit=$result->limits->filter(function ($element, $key){
            return $element->periodicity=='DAILY';
        })->first();
        if(!is_null($dailyLimit)){
            $limit=OilPayment::select("exchange_rate->amount->limit->amount as limits")
            ->where(function($query) use($oilPayment){
                return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                ->orWhere('id',$oilPayment->id);
            })
            ->whereRaw($whareDate,[Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()])
            ->get();

            if(!is_null($limit) && ($limit->count()>$dailyLimit->maxCount || $limit->sum("limits")>$dailyLimit->maxAmount->amount)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-GIFT",
                        "message"   => [
                            "title"   => __("Your daily account limit exceed the allowed limit!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_GIFT_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }
        }

        $monthlyLimit=$result->limits->filter(function ($element, $key){
            return $element->periodicity=='MONTHLY';
        })->first();
        if(!is_null($monthlyLimit)){
            $limit=OilPayment::select("exchange_rate->amount->limit->amount as limits")
            ->where(function($query) use($oilPayment){
                return $query->where('status',TransactionStatusEnum::COMPLETED->value)
                ->orWhere('id',$oilPayment->id);
            })
            ->whereRaw($whareDate,[Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()])
            ->get();

            if(!is_null($limit) && ($limit->count()>$monthlyLimit->maxCount || $limit->sum("limits")>$monthlyLimit->maxAmount->amount)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-GIFT",
                        "message"   => [
                            "title"   => __("Your monthly account limit exceed the allowed limit!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_GIFT_101",
                            "type"    => "ERROR"
                        ]
                     ]
                ]));
            }
        }
        // [
        //     "instid"    => $flexService->settings->instid,
        //     "req_ref_id"=>  "{$request->reference_id}",
        //     "amount"    => $request->amount,
        //     "currency"  => $request->currency,
        //     "description"  => $request->remarks,

        //     "db_account_no"    => $request->debitAccountId->value,
        //     "db_account_name"  => $request->debitAccountId->displayValue,
        //     "db_branch_no"     => "0",
        //     "db_branch_name"   => "0",
        //     "cr_account_no"    => $request->creditAccountId->value,
        //     "cr_account_name"  => $request->creditAccountId->displayValue,
        //     "bank_refrence"    => "YKBA_20250205_0002"
        // ]

        /** Payment Process**/
        $object = new \stdClass();
        $object->service_name = ThirdPartyServiceNameData::oil();
        $object->fee_service_name = ThirdPartyServiceNameData::oilFee();
        $object->account_id =$debitAccountId->value;
        $object->amount = $oilPayment->amount;
        $object->fee = $oilPayment->fee;
        $object->remarks =sprintf(trans("oil_remark_message"),
            $oilPayment->reference_id
        );
        $object->remarksFee =sprintf(trans("oil_fee_remark_message"),
            $oilPayment->reference_id
        );
       // return response()->json($object);
        /** Debit from customer account to service account**/
        $result = FlexService::debitToAccountWithFees($object);
        //$result=CustomerService::internalTranseferWithFees($object);
        $paymentResult=$result->getAdditionalData();
        if(isset($paymentResult['payment'])){
            $oilPayment->payment_result= $paymentResult['payment'];
            $oilPayment->save();
        }

        if($result->status->message->code=="0"){
            $oilPayment->load('region:id,name,account_id');
            /** Service Process**/
            $object=new \stdClass();
            $object->reference_id= $oilPayment->reference_id;
            $object->amount= $oilPayment->amount;
            $object->customerAccountId= $debitAccountId->value;
            $object->type=$oilPayment->type;
            //$object->remarks= $oilPayment->remarks;
            $object->remarks =sprintf(trans("oil_external_remark_message"),
                __(ucfirst($oilPayment->type)),
                $oilPayment->region->getName(),
                $oilPayment->remarks??""
            );

            $object->debitAccountId= AccountIdData::from([
                "value"=>ThirdPartyServiceNameData::oilExternal(),
                "displayValue"=>"YKB - Current A/C - Cash"
            ]);
            $object->creditAccountId=AccountIdData::from([
                "value"=>$oilPayment->region->account_id,//"105-2000071-1003-00",
                "displayValue"=>"CBY - Current A/C - Cash"
            ]);

            $result=FlexService::oilPayment($object);
            if($result->status->message->code=="0"){
                $oilPayment->debit_account_id= $object->debitAccountId->toArray();
                $oilPayment->transaction_id= $result->getAdditionalData()["transactionId"];
                $oilPayment->external_reference_id=  $result->getAdditionalData()["externalReferenceId"];
                $oilPayment->status=TransactionStatusEnum::COMPLETED->value;
                $oilPayment->save();

                // NotificationService::sendMessagesToParty([
                //     [
                //         'title'=>__("Send remittance"),
                //         'body'=>sprintf(__("Successfully send remittance to mobile number [%s] through [%s] service"),
                //             $requestData->receiverMobile,
                //             __("Wasil")
                //         ),
                //         'type'=>'operation',
                //     ]
                // ]);

                return response()->json(
                    GeneralResponseData::from($result->toArray())
                    ->additional([
                        'externalReferenceId'=>$oilPayment->external_reference_id,
                        'receipt'=> $this->getReceiptData($oilPayment)
                    ])
                );
            }else if($result->status->message->code=="DIGX_SWITCH_TIMEOUT"){
                $oilPayment->status=TransactionStatusEnum::PENDING->value;
                $oilPayment->save();
            }else{
                $oilPayment->status=TransactionStatusEnum::ERROR->value;
                $oilPayment->resolved=1;
                $oilPayment->save();

                $paymentResult=$oilPayment->payment_result;
                $object=new \stdClass();
                $object->reference_id   = $paymentResult->amount->referenceId;
                $object->service_name   =  ThirdPartyServiceNameData::oil();
                $object->account_id     = $debitAccountId->value;

                $reverseResult=FlexService::reverseToAccount($object);
                if($reverseResult->status->message->code=="0"){
                    $paymentResult->amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                    $oilPayment->payment_result= $paymentResult;
                    $oilPayment->save();
                }
                if(isset($paymentResult->fee)){
                    $object=new \stdClass();
                    $object->reference_id   = $paymentResult->fee->referenceId;
                    $object->service_name   = ThirdPartyServiceNameData::oilFee();
                    $object->account_id     = $debitAccountId->value;

                    $reverseResult=FlexService::reverseToAccount($object);
                    if($reverseResult->status->message->code=="0"){
                        $paymentResult->fee->status=InvoiceTransactionsTypeEnum::Reverse->value;
                        $oilPayment->payment_result= $paymentResult;
                        $oilPayment->save();
                    }
                }


            }

        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }
}
