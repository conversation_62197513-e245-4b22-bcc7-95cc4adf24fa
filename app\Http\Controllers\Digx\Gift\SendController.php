<?php

namespace App\Http\Controllers\Digx\Gift;

use App\Data\AccountConfigData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\ReceiptData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\GiftStatusEnum;
use App\Enums\LimitTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Models\Gift;
use App\Models\GiftTransaction;
use App\Scopes\CustomerScope;
use App\Services\NotificationService;
use App\Services\OBDX\CustomerService;
use App\Services\OBDX\UtilsService;
use App\Traits\AuthorizesServices;
use Carbon\Carbon;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Enums\TransactionStatusEnum;
use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\GiftRequest;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Services\FlexService;
class SendController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::GIFT
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $user=$request->user()->userProfile;
        //$port=\Request::getPort();
        //\Log::info("{$request->getSchemeAndHttpHost()}:$port");
        $processType=$request->processType??'group';
        $transaction=Gift::with(['transactions'=>function($query){
            return $query->select('id','status','gift_id','amount','payee','external_reference_id')
            ->with(['sharing'=>function($query){
                return $query->select('data_id','txn_token','data','type');
            }])
            ->where('status','<>', GiftStatusEnum::INIT->value);
        }])
        ->select('id','status','type','party_name','group','amount',
            'debit_account_id','remarks','at_date','created_at as date')
        ->whereHas('transactions',function($query){
            return $query->where('status','<>', GiftStatusEnum::INIT->value);
        })
        ->where('process_type',$processType);

        if(in_array($request->currencyId??'',[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
            $transaction=$transaction->whereIn('amount->currency',[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value]);
        }else{
            $transaction=$transaction->whereNotIn('amount->currency',[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value]);
        }
        $transaction=$transaction->skip($request->from)
        ->take($request->limit)
        ->orderBy("created_at","DESC")
        ->get();

        return response()->json($transaction);

    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'debitAccountId.displayValue'=>"required",
            'debitAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required",
            'type'=>"required|in:friday,eid,general,eidAlfitr,eidAladha,engagement,success,other",
            'groupId'=>"required",
            'payees.*.payeeId'=>"required",
            'payees.*.amount.amount'=>"required|numeric",
            'payees.*.amount.currency'=>"required"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GIFT",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_GIFT_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $user=$request->user();


        if($request->filled("giftRequestId")){
            $giftRequest=GiftRequest::withoutGlobalScope(CustomerScope::class)
            ->where("id",$request->giftRequestId)
            ->where("status",TransactionStatusEnum::INIT->value)
            ->where("sender->value",$user->phone)
            ->whereDoesntHave('gift',function($query){
                return $query->where('status', "<>",GiftStatusEnum::INIT->value);
            })
            ->first();
            if(is_null($giftRequest)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-GIFT",
                        "message"   => [
                            "title"   => "This request already ended",
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_GIFT_100",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }
        }



        $valid=false;
        $message="You don't have the correct account!";
        $result=CustomerService::account($request,$request->input("debitAccountId.value"));
        if($result instanceof AccountData){
            $account=$result;
            $valid=$account->status=="ACTIVE" && ($account->isCash()|| $account->isGold()) && $account->currencyId==$request->input("amount.currency") &&
                $account->allowedService(AccountConfigData::gift);
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GIFT",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GIFT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $amount_sum=0;
        $group=CustomerService::payees($request,"PEERTOPEER",$request->groupId);
        if(!is_null($group)){
            $payees=[];

            foreach( $request->payees as $payeeGroup){
                $payee=collect($group->listPayees)->where('id',$payeeGroup["payeeId"])->first();
                if(!is_null($payee)){
                    $amount_sum+=$payeeGroup["amount"]["amount"];
                    $payees[]=[
                        "amount"=>[
                            "amount"=>$payeeGroup["amount"]["amount"],
                            "currency"=>$request->input("amount.currency"),
                        ],
                        "payee"=>[
                            "id"=>$payeeGroup["payeeId"],
                            "nickName"=>html_entity_decode($payee->nickName, ENT_QUOTES, "UTF-8"),
                            "transferMode"=>$payee->transferMode,
                            "transferValue"=>$payee->transferValue,
                        ],

                    ];
                }else{
                    $payees=null;
                    break 1;

                }
            }
        }
        if(!isset($payees) || is_null($payees)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => __("Payee list is not correct!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $amount=CurrencyAmountData::from([
            "amount"        => $amount_sum,
            "currency"      =>  $request->input("amount.currency"),
        ]);
        $exchangeRate= UtilsService::exchange(
            $amount,
            $debitAccountId,
            AccountIdData::from([
                "value"=>substr_replace($debitAccountId->value,CurrencyTypeEnum::YER->value,10, 3)
            ])
        );

        $gift=Gift::create([
            "status"      =>GiftStatusEnum::INIT->value,
            "party_name"  =>html_entity_decode("{$user->userProfile->firstName} {$user->userProfile->lastName}", ENT_QUOTES, "UTF-8"),
            "group"  =>[
                "id"=>$request->groupId,
                "name"  =>$group->name,
            ],
            "type"              =>$request->type,
            "process_type"      =>$request->processType??'group',
            "debit_account_id"  =>$debitAccountId->toArray(),
            "amount"            =>$amount->toArray(),
            "exchange_rate"=>[
                "amount"        =>$exchangeRate->toArray(),
            ],
            "remarks"           =>$request->remarks,
            "payees"            =>$payees,
            "at_date"           =>!is_null($request->atDate??null)?Carbon::createFromFormat("Y-m-d H:i",$request->atDate)->format("Y-m-d H:i"):null
        ]);

        if(isset($giftRequest)){
            $giftRequest->gift_id=$gift->id;
            $giftRequest->save();
        }

        LogItem::store($gift);



        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-INVOICE",
                "message"   => [
                    "title"   => "The initial request has been created",
                    "detail"  => "The initial request has been created",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "externalReferenceId"=>$gift->id,
        ]));

    }
    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function receipt($id,Request $request)
    {
        $item=Gift::with(['transactions'=>function($query)use($request){
            $query->select('id','status','gift_id','amount','payee','external_reference_id')
            ->where('status','<>', 0);
            if($request->filled('transactionId')){
                $query->where('id', $request->transactionId);
            }
            return $query;
        }])
        ->select('id','status','type','party_name','group','amount',
            'debit_account_id','remarks','created_at as date')
        ->whereHas('transactions',function($query)use($request){
            $query->where('status','<>', 0);
            if($request->filled('transactionId')){
                $query->where('id', $request->transactionId);
            }
            return $query;
        })
        ->where('id', $id)
        ->first();

        $this->generateReceipt($this->getReceiptData($item));

    }
    protected function getReceiptData(Gift $gift): ReceiptData
    {
        $title=__("Funds transfer") ." (".__('Gift').")";

        if(count($gift->transactions)==1){
            $title.=" ".__("gift_status_". $gift->transactions->first()->status);
        }
        return ReceiptData::from([
            "id"=> $gift->id,
            "date"=> date_format(date_create($gift->created_at), "Y-m-d H:i:s"),
            "title"=>  $title,
            //"beneficiary"=> count($gift->transactions).' '.__("Persons"),
            "statement"=>  $title,
            "details"=> [
                "debitAccountId"=> $gift->debit_account_id,
                "referenceId"=>count($gift->transactions)==1?$gift->transactions->first()->external_reference_id:null,
                "items"=>collect($gift->transactions)->map(function($transaction){
                    return [
                        "id"=> $transaction->id,
                        "date"=> date_format(date_create($transaction->created_at), "Y-m-d H:i:s"),
                        "title"=>  "",
                        "beneficiary"=> "",
                        "statement"=> html_entity_decode($transaction->payee->nickName)." - ".$transaction->payee->transferValue." (".__("gift_status_". $transaction->status).")",
                        "details"=> [
                            "amount"=>$transaction->amount,
                        ]
                    ];
                })->toArray(),
                "amount"=>[
                    'amount'=>$gift->amount->amount,
                    'currency'=>$gift->amount->currency
                ],
                "remarks"=>sprintf(trans("Gift to %s Persons, On the occasion of %s"),
                            count($gift->transactions),
                            __($gift->type)).(is_null($gift->remarks)?"":" - ".$gift->remarks),
            ]
        ]);
    }
    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $user=$request->user()->userProfile;

        $gift=Gift::select("id","group","debit_account_id","type",'amount','remarks','payees')
        ->where("status",GiftStatusEnum::INIT->value)
        ->find($id);

        if(is_null($gift)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "GIFT-DETAILS",
                    "message"   => [
                        "title"   => __("Gift not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GIFT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "GIFT-DETAILS",
                "message"   => [
                    "title"   => "The initial request has been created",
                    "detail"  => "The initial request has been created",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "gift"=>$gift,
        ]));

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Gift  $gift
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Gift $gift)
    {
        LogItem::store($gift);
        // Start getting user ID card info.
        $resultCardInfo=CustomerService::cardInfo($request,false);
        if($resultCardInfo->status->message->code!="0"){
            return response()->json($resultCardInfo);
        }
        $userCardInfo=$resultCardInfo->getAdditionalData()["cardInfo"];

        $user = $request->user()->userProfile;

        $valid=false;
        $message="You don't have the correct account!";
        if ($gift->party_id != $user->partyId->value) {
            $message = "You don't have a permission!";
        } else if ($gift->status != GiftStatusEnum::INIT->value) {
            $message = "This transaction has already been performed!";
        }else{
            $result=CustomerService::account($request,$gift->debit_account_id->value);
            if($result instanceof AccountData){
                $account=$result;
                if($account->status!="ACTIVE"){
                    $message="Your account not active!";
                }else if(!$account->allowedService(AccountConfigData::gift)){
                    $message="This account not allowed to use this service!";
                }else if(!($account->isCash() || $account->isGold())){
                    $message="Account is not cash!";
                }else if($account->currencyId!=$gift->amount->currency){
                    $message="Account currency not match!";
                }else if($account->balance->amount<$gift->amount->amount){
                    $message="You don't have enough balance in your account!";
                }else{
                    $valid=true;
                }
            }
        }
        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GIFT",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GIFT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $debitAccountId=AccountIdData::from($gift->debit_account_id);
        $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        $result=$targetLimitLinkages
        ->filter(function ($element, $key) use($debitAccountId){
            return $element->target->value==LimitTypeEnum::GIFT->value &&
             $element->target->area==($debitAccountId->isNorth()?'N':'S') &&
              $element->target->currency==$debitAccountId->currencyId();
        })
        ->first();

        if(is_null($result)){
            $result=$targetLimitLinkages
            ->filter(function ($element, $key){
                return $element->target->value==LimitTypeEnum::GIFT->value &&
                 $element->target->area=='' &&
                  $element->target->currency=='';
            })
            ->first();
        }

        // if(is_null($result)){
        //     return response()->json(GeneralResponseData::from([
        //         'status'=>[
        //             "result"    => "ERROR",
        //             "contextID" => "STORE-GIFT",
        //             "message"   => [
        //                 "title"   => __($message),
        //                 "detail"  => "",
        //                 "code"    => "DIGX_SWITCH_GIFT_101",
        //                 "type"    => "ERROR"
        //             ]
        //         ]
        //     ]));
        // }

        $whareDate="updated_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="updated_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }

        $trxLimit=$result->limits->filter(function ($element, $key){
            return $element->limitType=='TXN';
        })->first();
        if(!is_null($trxLimit) && ($gift->exchange_rate->amount->limit->amount < $trxLimit->amountRange->minTransaction->amount || $gift->exchange_rate->amount->limit->amount > $trxLimit->amountRange->maxTransaction->amount)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-GIFT",
                    "message"   => [
                        "title"   =>sprintf( __("Your transaction amount exceed the allowed limit! Min %s Max %s per transaction"),$trxLimit->amountRange->minTransaction->amount,$trxLimit->amountRange->maxTransaction->amount),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GIFT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $dailyLimit=$result->limits->filter(function ($element, $key){
            return $element->periodicity=='DAILY';
        })->first();
        if(!is_null($dailyLimit)){
            $limit=Gift::select("exchange_rate->amount->limit->amount as limits")
            ->where(function($query) use($gift){
                return $query->whereIn('status',[GiftStatusEnum::SUCCESS->value,GiftStatusEnum::SCHEDULE->value])
                ->orWhere('id',$gift->id);
            })
            ->whereRaw($whareDate,[Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()])
            //->whereBetween('updated_at',[\Carbon\Carbon::now()->startOfDay()->toDateTimeString(),\Carbon\Carbon::now()->endOfDay()->toDateTimeString()])
            ->get();

            if(!is_null($limit) && ($limit->count()>$dailyLimit->maxCount || $limit->sum("limits")>$dailyLimit->maxAmount->amount)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-GIFT",
                        "message"   => [
                            "title"   => __("Your daily account limit exceed the allowed limit!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_GIFT_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }
        }

        $monthlyLimit=$result->limits->filter(function ($element, $key){
            return $element->periodicity=='MONTHLY';
        })->first();
        if(!is_null($monthlyLimit)){
            $limit=Gift::select("exchange_rate->amount->limit->amount as limits")
            ->where(function($query) use($gift){
                return $query->whereIn('status',[GiftStatusEnum::SUCCESS->value,GiftStatusEnum::SCHEDULE->value])
                ->orWhere('id',$gift->id);
            })
            ->whereRaw($whareDate,[Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()])
            //->whereBetween('updated_at',[\Carbon\Carbon::now()->startOfMonth()->toDateTimeString(),\Carbon\Carbon::now()->endOfMonth()->toDateTimeString()])
            ->get();

            if(!is_null($limit) && ($limit->count()>$monthlyLimit->maxCount || $limit->sum("limits")>$monthlyLimit->maxAmount->amount)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-GIFT",
                        "message"   => [
                            "title"   => __("Your monthly account limit exceed the allowed limit!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_GIFT_101",
                            "type"    => "ERROR"
                        ]
                     ]
                ]));
            }
        }


        $gift->load('request');
        foreach ($gift->payees as $payee) {
            // Getting exchange rate of amount to equlevent local currency
            $exchangeRate= UtilsService::exchange(
                CurrencyAmountData::from($payee["amount"]),
                AccountIdData::from($gift->debit_account_id),
                AccountIdData::from([
                    "value"=>substr_replace($gift->debit_account_id->value,CurrencyTypeEnum::YER->value,10, 3)
                ])
            );

            $payee['gift_id'] = $gift->id;
            $payee['status'] = 0;
            $payee['party_id'] = $gift->party_id;
            $payee['reference_id'] =uniqid();
            $payee['sender_gender'] =$userCardInfo->gender;
            $payee['sender_phone']  =$user->phoneNumber->value;
            $payee['exchange_rate']=[
                "amount"        =>$exchangeRate->toArray(),
            ];
            $giftTransaction = GiftTransaction::create($payee);



            $object = new \stdClass();
            $object->reference_id = $giftTransaction->reference_id;
            $object->service_name = ThirdPartyServiceNameData::gift();
            $object->account_id = $gift->debit_account_id->value;
            $object->amount = $giftTransaction->amount->amount;
            $object->currency = $giftTransaction->amount->currency;
            $object->remarks = trans("gift_to")." ".html_entity_decode($payee['payee']['nickName'], ENT_QUOTES, "UTF-8") ?? "Deduct the value of the gift";

            $result = FlexService::debitToAccount($object);
            if ($result->status->message->code == "0") {
                $giftTransaction->external_reference_id = $result->getAdditionalData()["externalReferenceId"];
                $giftTransaction->status = GiftStatusEnum::SUCCESS->value;
                $giftTransaction->save();

                $sharing = \App\Models\Sharing::create([
                    "txn_token" => (string)Str::orderedUuid(),
                    "type" => "gift",
                    "party_id" => $user->partyId->value,
                    "data" => [
                        "id" => $giftTransaction->id
                    ]
                ]);
                if(is_null($gift->at_date)){
                    /*$smsMessage = sprintf(trans("gift_sms_message"),
                        html_entity_decode($gift->party_name, ENT_QUOTES, "UTF-8"),
                        $gift->type=='other'?$gift->remarks:trans("$gift->type"),
                        $giftTransaction->amount->amount . " " . $giftTransaction->amount->currency,
                        env('APP_URL').route('sharing',[$sharing->txn_token],false)
                    );*/
                    //$current_date= Carbon::now();
                    if(!is_null($gift->request)){
                        $smsMessage = sprintf(trans("gift_sms_message_accept_request"),
                            $gift->request->party_name,
                            $gift->party_name,
                            $giftTransaction->amount->amount . " " . $giftTransaction->amount->currency,
                            env('APP_URL').route('sharing',[$sharing->txn_token],false)
                        );
                    }else{
                        $is_eid=Carbon::parse($giftTransaction->created_at)->betweenIncluded('2024-04-10', '2024-04-17');
                        $smsMessage = sprintf(trans($is_eid?"gift_sms_message_new_eid":"gift_sms_message_new"),
                            $payee['payee']['nickName'],
                            $gift->party_name,
                            $gift->type=='other'?$gift->remarks:trans("$gift->type"),
                            $giftTransaction->amount->amount . " " . $giftTransaction->amount->currency,
                            env('APP_URL').route('sharing',[$sharing->txn_token],false)
                        );
                    }

                    if(env('APP_ENV', 'production')!='local' || in_array($giftTransaction->payee->transferValue,['*********','*********'])){
                        NotificationService::sendSMS([
                            "mobile" => $giftTransaction->payee->transferValue,
                            "message" => $smsMessage
                        ]);
                    }
                }
            } else {
                $gift->status = GiftStatusEnum::ERROR->value;
                $gift->save();
                return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }
        }
        $gift->status = is_null($gift->at_date)?GiftStatusEnum::SUCCESS->value:GiftStatusEnum::SCHEDULE->value;
        $gift->save();

        if(is_null($gift->at_date)){
            $giftRequest=$gift->request;
            if(!is_null($giftRequest)){
                $giftRequest->status=TransactionStatusEnum::COMPLETED->value;
                $giftRequest->save();
            }
        }


        $item=Gift::with(['transactions'=>function($query)use($request){
            $query->select('id','status','gift_id','amount','payee','external_reference_id')
            ->where('status','<>', 0);
            return $query;
        }])
        ->select('id','status','type','party_name','group','amount',
            'debit_account_id','remarks','created_at as date')
        ->whereHas('transactions',function($query)use($request){
            $query->where('status','<>', 0);
            return $query;
        })
        ->where('id', $gift->id)
        ->first();


        return response()->json(GeneralResponseData::from(array(
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "STORE-INVOICE" ,
                "referenceNumber" => $giftTransaction->reference_id,
                "message" => [
                    "title" => $result->status->message->title,
                    "detail" => $result->status->message->detail,
                    "code" => $result->status->message->code,
                    "type" => "INFO"
                ]
            ]
        ))->additional([
            'externalReferenceId' => $result->getAdditionalData()["externalReferenceId"],
            "receipt"=> $this->getReceiptData($item)
        ]));
    }

    public static function limits($limitType){
        if(!in_array($limitType,[LimitTypeEnum::GIFT])){
            return;
        }

        $whareDate="updated_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')";
        if(env('DB_CONNECTION')!='oracle'){
            $whareDate="updated_at between STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
        }
        $limits=[];
        $trxLimit=[];

        $targetLimitLinkages= app(\App\Settings\ConfigSettings::class)->limitPackageConfig->targetLimitLinkages;
        $assignedLimits=$targetLimitLinkages
        ->filter(function ($element, $key) use($limitType){
            return $element->target->value==$limitType->value &&
             $element->target->area==(request()->input('area')??"") &&
              $element->target->currency==(request()->input('currency')??"");
        })
        ->first();

        foreach ($assignedLimits->limits as $item) {
            switch ($item->periodicity??"") {
                case 'DAILY':
                    $limitUtilization=Gift::select("exchange_rate->amount->limit->amount as limits")
                    ->whereIn('status',[GiftStatusEnum::SUCCESS->value,GiftStatusEnum::SCHEDULE->value])
                    ->whereRaw($whareDate,[Carbon::now()->startOfDay()->toDateTimeString(),Carbon::now()->endOfDay()->toDateTimeString()])
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                case 'MONTHLY':
                    $limitUtilization=Gift::select("exchange_rate->amount->limit->amount as limits")
                    ->whereIn('status',[GiftStatusEnum::SUCCESS->value,GiftStatusEnum::SCHEDULE->value])
                    ->whereRaw($whareDate,[Carbon::now()->startOfMonth()->toDateTimeString(),Carbon::now()->endOfMonth()->toDateTimeString()])
                    ->get();
                    $limits[]=[
                        "period"=>$item->periodicity,
                        "maxAmount"=>$item->maxAmount,
                        "maxCount"=>$item->maxCount,
                        "amount"=>CurrencyAmountData::from([
                            "amount"=>$limitUtilization->sum("limits"),
                            "currency"=>CurrencyTypeEnum::YER->value,
                        ]),
                        "count"=>$limitUtilization->count(),
                    ];
                    break;
                default:
                    if($item->limitType=="TXN" && isset($item->amountRange->minTransaction) && isset($item->amountRange->maxTransaction)){
                        $trxLimit=[
                            "minAmount"=>$item->amountRange->minTransaction,
                            "maxAmount"=>$item->amountRange->maxTransaction,
                        ];
                    }
            }
        }
        $trxLimit["limits"]=$limits;

        //return $this->verifyUser($request,$customer);
        return abort(response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional($trxLimit)));
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\GiftTransaction  $transaction
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function claim(GiftTransaction $transaction,Request $request)
    {
        $validator=validator()->make($request->all(),[
            'creditAccountId.displayValue'=>"required",
            'creditAccountId.value'=>"required|max:20|min:20"
        ]);


        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CLAIM-GIFT",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $user=$request->user()->userProfile;
        $currency=$transaction->amount->currency;
        $creditAccountId=$request->input("creditAccountId.value");

        $gift=Gift::withoutGlobalScope(CustomerScope::class)->where("id",$transaction->gift_id)->first();

        $debitAccountId=AccountIdData::from($gift->debit_account_id);


        $valid=false;
        $message="Can't load your accounts!";
        $result=CustomerService::account($request,$request->input("creditAccountId.value"));
        if($result instanceof AccountData){
            $account=$result;
            if($account->status!="ACTIVE"){
                $message="Your account not active!";
            }else if(!$account->allowedService(AccountConfigData::gift)){
                $message="This account not allowed to use this service!";
            }else if(!($account->isCash() || $account->isGold())){
                $message="Account is not cash!";
            }else if($account->currencyId!=$currency){
                $message="Account currency not match!";
            }else if(!(($account->currencyId==CurrencyTypeEnum::YER->value && $account->isNorth()==$debitAccountId->isNorth()) ||
                            $account->currencyId!=CurrencyTypeEnum::YER->value)){
                $message="You don't have correct account to claim this gift!";
            }else{
                $valid=true;
            }
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CLAIM-GIFT",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        if(is_null($transaction) || $transaction->status!=GiftStatusEnum::SUCCESS->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CLAIM-GIFT",
                    "message"   => [
                        "title"   => "This transaction already expired",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }
        if($transaction->payee->transferValue != $user->phoneNumber->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CLAIM-GIFT",
                    "message"   => [
                        "title"   => __("You don't have permission to claim this gift!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }



        if(is_null($transaction->claim_reference_id)){
            $transaction->claim_reference_id= uniqid();
            $transaction->save();
        }
        LogItem::store($gift);

        $object=new \stdClass();
        $object->reference_id   = $transaction->claim_reference_id;
        $object->service_name   = ThirdPartyServiceNameData::gift();
        $object->account_id     = $creditAccountId;
        $object->amount         = $transaction->amount->amount;
        $object->currency       = $transaction->amount->currency;
        $object->remarks        = trans("gift_recive")." ".html_entity_decode($gift->party_name, ENT_QUOTES, "UTF-8");


        $result=FlexService::accountToDebit($object);
        if($result->status->message->code=="0"){
            $transaction->claim_external_reference_id= $result->getAdditionalData()["externalReferenceId"];
            $transaction->credit_account_id=$request->input("creditAccountId");
            $transaction->claim_party_id=$user->partyId->value;
            $transaction->status=GiftStatusEnum::CLAIM->value;
            $transaction->save();

            $userShortName=html_entity_decode("{$user->firstName} {$user->lastName}", ENT_QUOTES, "UTF-8");
            $smsMessage = sprintf(trans("claim_gift_sms_message_new"),
                $gift->party_name,
                $userShortName,
                $transaction->amount->amount . " " . $transaction->amount->currency
            );
            if (!is_null($transaction->sender_phone)){
                NotificationService::sendSMS([
                    "mobile" => $transaction->sender_phone,
                    "message" => $smsMessage
                ]);
            }
            return response()->json($result);
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);

    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\GiftTransaction  $transaction
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function claimback(GiftTransaction $transaction,Request $request)
    {
        if(is_null($transaction) || $transaction->status!=GiftStatusEnum::SUCCESS->value || !is_null($transaction->claim_external_reference_id) ){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CLAIM-GIFT-BACK",
                    "message"   => [
                        "title"   => "This transaction already expired",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $user=$request->user()->userProfile;
        if($transaction->party_id != $user->partyId->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CLAIM-GIFT-BACK",
                    "message"   => [
                        "title"   => __("You don't have permission to claim back this gift!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }


        $gift=Gift::where("id",$transaction->gift_id)->first();
        LogItem::store($gift);

        if(is_null($transaction->claim_reference_id)){
            $transaction->claim_reference_id= uniqid();
            $transaction->save();
        }

        $object=new \stdClass();
        $object->reference_id   = $transaction->reference_id;
        $object->service_name   = ThirdPartyServiceNameData::gift();
        $object->account_id     = $gift->debit_account_id->value;

        $result=FlexService::reverseToAccount($object);
        if($result->status->message->code=="0"){
            $transaction->claim_external_reference_id= $transaction->reference_id;
            $transaction->credit_account_id=$gift->debit_account_id->value;
            $transaction->claim_party_id=$user->partyId->value;
            $transaction->status=GiftStatusEnum::CLAIMBACK->value;
            $transaction->save();


            if(env('APP_ENV', 'production')!='local' || in_array($transaction->payee->transferValue,['*********','*********'])){
                $smsMessage = sprintf(trans("claim_back_gift_sms_message"),
                    $transaction->payee->nickName,
                    $gift->party_name,
                    $transaction->amount->amount . " " . $transaction->amount->currency
                );
                NotificationService::sendSMS([
                    "mobile" => $transaction->payee->transferValue,
                    "message" => $smsMessage
                ]);
            }

            return response()->json(GeneralResponseData::from($result->toArray())->additional([
                'externalReferenceId'=>$transaction->claim_external_reference_id,
            ]));
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }

}
