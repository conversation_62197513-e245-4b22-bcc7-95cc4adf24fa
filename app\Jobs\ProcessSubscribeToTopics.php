<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Laravel\Horizon\Contracts\Silenced;

class ProcessSubscribeToTopics implements ShouldQueue, Silenced
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @param array $topics
     * @param array $tokens
     * @return void
     */
    public function __construct(
        public array $topics,
        public array $tokens
    ){
        //
    }

    /* Get the tags that should be assigned to the job.
    *
    * @return array<int, string>
    */
    public function tags(): array
    {
        $tags=['topic'];
        return $tags;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $appFlag= env('APP_FLAG', 'banky.commercial')=='banky.islamic'?'islamic_':'';
        try{
            $_topics=[];
            foreach ($this->topics as $topic) {
                $_topic="{$topic}_";
                $_topic.= $appFlag;
                //$_topic.= env('APP_ENV', 'production')=='production'?'bankylite':'general';
                $_topics[]=$_topic.(env('APP_ENV', 'production')=='production'?'bankylite':'general');
                $_topics[]=$_topic.(env('APP_ENV', 'production')=='production'?'commercial':'general');
            }


            $messaging = app('firebase.messaging');
            $result=$messaging->subscribeToTopics($_topics,$this->tokens);
            return $result;
        }catch(\Exception $e){
            \Log::error("[Notifications]: subscribeToTopics");
        }
    }
}
