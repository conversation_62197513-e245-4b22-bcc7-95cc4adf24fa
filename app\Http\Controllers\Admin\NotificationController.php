<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Models\CustomerNotification;
use App\Models\CustomerType;
use App\Models\Notification;
use App\Models\PartyVerify;
use App\Models\Service;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Auth;



class NotificationController extends Controller
{

    protected $types=[
        "unicast"=>"Unicast",
        "brodcast"=>"Brodcast",
    ];

    protected $actions=[
        "general"=>"General",
        "update"=>"Update",
    ];
    public function __construct(Request $request){$this->middleware('auth');}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['notification.*','notification.view','notification.list'])) return abort(401);

        $filter=$request->all();

        $items=Notification::orderBy('created_at','desc');
        $items=$items->paginate(15);

        $types=collect($this->types)->map(function ($element, $key){
            return [$key=>__("$element")];
        })->collapse()->toArray();

        $actions=collect($this->actions)->map(function ($element, $key){
            return [$key=>__("$element")];
        })->collapse()->toArray();

        $customer_types=CustomerType::pluck('name','topic')->toArray();
        $customer_types=collect($customer_types)->map(function ($element, $key){
            return [$key=>__("$element")];
        })->collapse()->toArray();

        $services=Service::select("id","title")
        ->whereNotNull('service_id')
        ->whereNotNull('url')
        ->whereHas('customerTypes', function ($query) use($request) {
            return $query->whereNull('page_service_id')
            ->where('service_customer_types.status','<>',0);
        })
        ->orderBy('sort','asc')
        ->get();

        $services=collect($services)->pluck('name','id');

        return view('default.admin.notification.index')
        ->with('items', $items)
        ->with('types',$types)
        ->with('actions', $actions)
        ->with('topics', $customer_types)
        ->with('services', $services)

        ->with('filter', $filter);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['notification.*','notification.create'])) return abort(401);

        $this->validate($request,[
            'title' => 'required|max:200',
            'body' => 'required|max:700',
            'type' => 'required|in:'.join(",",collect($this->types)->keys()->toArray()),
            'status' => 'required',

            'action' => 'nullable|in:'.join(",",collect($this->actions)->keys()->toArray()),
            'topic' => 'required_if:type,==,brodcast',
            'party_id' => 'required_if:type,==,unicast',
            'service_id' => 'required_if:action_type,==,1',

            'url' => 'nullable',
        ]);
        // $validations+=[
        //     "reportType" => 'required',
        //     "cacheSignature.$idRetail"=>'nullable',
        //     "cacheSignature.$idCorporate"=>'nullable',
        //     "cacheSignature.$idAgent"=>'nullable',
        //     "tokenValidation"=>'required|numeric|max_digits:1|max:1',
        //     "app2FASetting.attempts"=>'required|numeric|max:5',
        //     "app2FASetting.resends"=>'required|numeric|max:5',
        //     "app2FASetting.status"=>'required|numeric|max_digits:1|max:1',
        //     "app2FASetting.firstTimeOnly"=>'required|numeric|max_digits:1|max:1',
        // ];
        $params=[
            "title"=>$request->title,
            "body"=>$request->body,
            "type"=>$request->type,
            "status"=>$request->status,
            "action"=>$request->action??null,
            "user_id"=>auth()->user()->id,
        ];

        $topic=null;
        if($request->type=="unicast"){
            if(!$request->filled("party_id")){
                return back()->with('error',__("Party id is required!"));
            }
            $partyIds=explode(",",$request->party_id);
            $partyVerifies=PartyVerify::whereIn('party_id',$partyIds)->get();
            if(!count($partyVerifies)){
                return back()->with('error',__("Party id not found!"));
            }
        }else{
            if(!$request->filled("topic")){
                return back()->with('error',__("Topic identifier is required!"));
            }
            $params["topic"]=$request->topic;
        }

        if($request->filled("action_type")){
            $service=Service::where('id',$request->service_id)
            ->whereNotNull('service_id')
            ->whereNotNull('url')
            ->whereHas('customerTypes', function ($query) use($request) {
                $query->whereNull('page_service_id')
                ->whereIn('service_customer_types.status',[1,3]);

                if($request->type =="brodcast" && $request->topic!='main'){
                    $query=$query->where('topic',$request->topic);
                // }else if($request->type=="unicast"){
                //     $query=$query->where('id',$customer->userProfile->customerType);
                }
                return $query;
            });

            $service=$service->first();

            if(is_null($service)){
                return back()->with('error',__("The service you select not active!"));
            }
            $params["service_id"]=$request->service_id;
            $params["url"]=$service->url;
        }else{
            $params["url"]=$request->url??null;
        }


        $this->setImage('image',"notifications");
        $params["image"]=$request->image??null;

        $notification=Notification::create($params);

        $fields=[
            "extra_id"=>$notification->id,
            'title'=>$notification->title,
            'body'=>$notification->body,
            'notificationId'=>$notification->id
        ];
        if($request->filled("action")){
            $fields['type']=$request->action;
        }
        if($request->filled("action_type")){
            $fields['service_id']=$notification->service_id;
            $fields['url']=$notification->url;
        }else if($request->filled("url")){
            $fields['url']=$notification->url;
        }

        if(!is_null($notification->image)){
            $fields['image']=$request->image;
        }

        if($request->type=="unicast"){
            $notification->party_ids=$request->party_id;
            $notification->save();
        }



        if($request->type=="brodcast"){
            $notificationJob=NotificationService::broadcast($fields,$notification->topic);

            if($request->filled("at_date")){
                $notificationJob->delay(Carbon::createFromFormat("Y-m-d\TH:i",$request->at_date));
            }
        }else{
            collect($partyVerifies->filter(function($party){
                return !is_null($party->party_id) && !is_null($party->customer_type_id);
            })->unique('party_id'))->map(function($party) use($notification){
                return [
                    'customer_id'       =>$party->party_id,
                    'customer_type_id'  =>$party->customer_type_id,
                    'notification_id'   =>$notification->id,
                    'status'   =>'U'
                ];
            })->chunk(200)->each(function ($chunked) {
                CustomerNotification::insert($chunked->toArray());
            });

            collect($partyVerifies->map(function ($element,int $key){
                if(isset($element->terminal?->registrationToken)){
                    return $element->terminal->registrationToken;
                }
                return null;
            })->filter()->unique()->toArray())->chunk(1000)->each(function ($chunked) use ($fields,$request) {
                $fields['tokens']=$chunked->values()->toArray();
                $notificationJob=NotificationService::send($fields);

                if($request->filled("at_date")){
                    $notificationJob->delay(Carbon::createFromFormat("Y-m-d\TH:i",$request->at_date));
                }
            });


        }
        return back()->with('success',__("Notification will send in background & result will show in history!"));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Notification $notification
     * @return ?\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function show(Notification $notification){
        return response(view('default.admin.notification.model')
        ->with('json',json_decode(json_encode($notification->result)))/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
    }
}
